#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أنماط واجهة المستخدم المحسنة
تحتوي على أنماط محسنة لجميع عناصر الواجهة لضمان وضوح النصوص
"""

from PyQt5.QtCore import QSettings
from PyQt5.QtGui import QFont


class UIStyles:
    """فئة أنماط واجهة المستخدم المحسنة"""
    
    @staticmethod
    def get_current_font():
        """الحصول على الخط الحالي من الإعدادات"""
        try:
            settings = QSettings("SchoolManagement", "Settings")
            font_family = settings.value("font_family", "Arial")
            font_size = int(settings.value("font_size", 12))
            return QFont(font_family, font_size)
        except:
            return QFont("Arial", 12)
    
    @staticmethod
    def get_label_style(font_size=None, bold=False, color="#2c3e50"):
        """أنماط الليبلز المحسنة"""
        current_font = UIStyles.get_current_font()
        if font_size is None:
            font_size = current_font.pointSize()
        
        font_weight = "bold" if bold else "normal"
        
        return f"""
            QLabel {{
                font-family: '{current_font.family()}';
                font-size: {font_size}px;
                font-weight: {font_weight};
                color: {color};
                padding: 5px;
                background-color: transparent;
                border: none;
                text-align: right;
                qproperty-alignment: AlignRight;
            }}
        """
    
    @staticmethod
    def get_form_label_style():
        """أنماط ليبلز النماذج"""
        current_font = UIStyles.get_current_font()
        font_size = max(current_font.pointSize(), 12)  # حد أدنى 12
        
        return f"""
            QLabel {{
                font-family: '{current_font.family()}';
                font-size: {font_size}px;
                font-weight: bold;
                color: #2c3e50;
                padding: 8px 5px;
                background-color: transparent;
                border: none;
                text-align: right;
                qproperty-alignment: 'AlignRight | AlignVCenter';
                min-height: 25px;
                margin-right: 10px;
            }}
        """
    
    @staticmethod
    def get_title_style(font_size=None):
        """أنماط العناوين"""
        current_font = UIStyles.get_current_font()
        if font_size is None:
            font_size = current_font.pointSize() + 6
        
        return f"""
            QLabel {{
                font-family: '{current_font.family()}';
                font-size: {font_size}px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
                text-align: center;
                qproperty-alignment: AlignCenter;
                min-height: 40px;
            }}
        """
    
    @staticmethod
    def get_input_style():
        """أنماط حقول الإدخال المحسنة"""
        current_font = UIStyles.get_current_font()
        font_size = max(current_font.pointSize(), 11)  # حد أدنى 11
        
        return f"""
            QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox, QComboBox {{
                font-family: '{current_font.family()}';
                font-size: {font_size}px;
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 10px 12px;
                background-color: white;
                color: #2c3e50;
                min-height: 20px;
            }}
            QLineEdit:focus, QTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {{
                border-color: #3498db;
                outline: none;
                background-color: #f8f9fa;
            }}
            QLineEdit:disabled, QTextEdit:disabled, QSpinBox:disabled, QDoubleSpinBox:disabled, QComboBox:disabled {{
                background-color: #ecf0f1;
                color: #7f8c8d;
                border-color: #d5dbdb;
            }}
            QComboBox::drop-down {{
                border: none;
                width: 30px;
            }}
            QComboBox::down-arrow {{
                image: none;
                border: 2px solid #7f8c8d;
                width: 8px;
                height: 8px;
                border-top: none;
                border-right: none;
                transform: rotate(45deg);
                margin-right: 10px;
            }}
        """
    
    @staticmethod
    def get_button_style(bg_color="#3498db", hover_color="#2980b9"):
        """أنماط الأزرار المحسنة"""
        current_font = UIStyles.get_current_font()
        font_size = max(current_font.pointSize(), 12)  # حد أدنى 12
        
        return f"""
            QPushButton {{
                font-family: '{current_font.family()}';
                font-size: {font_size}px;
                font-weight: bold;
                background-color: {bg_color};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                min-height: 20px;
                min-width: 80px;
            }}
            QPushButton:hover {{
                background-color: {hover_color};
            }}
            QPushButton:pressed {{
                background-color: {hover_color};
                padding: 13px 19px 11px 21px;
            }}
            QPushButton:disabled {{
                background-color: #bdc3c7;
                color: #7f8c8d;
            }}
        """
    
    @staticmethod
    def get_checkbox_style():
        """أنماط صناديق الاختيار المحسنة"""
        current_font = UIStyles.get_current_font()
        font_size = max(current_font.pointSize(), 12)
        
        return f"""
            QCheckBox {{
                font-family: '{current_font.family()}';
                font-size: {font_size}px;
                color: #2c3e50;
                padding: 5px;
                spacing: 8px;
            }}
            QCheckBox::indicator {{
                width: 20px;
                height: 20px;
                border-radius: 3px;
            }}
            QCheckBox::indicator:checked {{
                background-color: #27ae60;
                border: 2px solid #27ae60;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
            }}
            QCheckBox::indicator:unchecked {{
                background-color: white;
                border: 2px solid #bdc3c7;
            }}
            QCheckBox::indicator:hover {{
                border-color: #3498db;
            }}
        """
    
    @staticmethod
    def get_frame_style():
        """أنماط الإطارات المحسنة"""
        return """
            QFrame {
                background-color: white;
                border: 2px solid #e1e8ed;
                border-radius: 10px;
                padding: 20px;
                margin: 5px;
            }
        """
    
    @staticmethod
    def get_group_box_style():
        """أنماط صناديق المجموعات المحسنة"""
        current_font = UIStyles.get_current_font()
        font_size = max(current_font.pointSize(), 13)
        
        return f"""
            QGroupBox {{
                font-family: '{current_font.family()}';
                font-size: {font_size}px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: white;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                background-color: white;
                border-radius: 3px;
            }}
        """
    
    @staticmethod
    def get_table_style():
        """أنماط الجداول المحسنة"""
        current_font = UIStyles.get_current_font()
        font_size = max(current_font.pointSize(), 11)
        
        return f"""
            QTableWidget {{
                font-family: '{current_font.family()}';
                font-size: {font_size}px;
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #3498db;
                selection-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
            }}
            QTableWidget::item {{
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }}
            QTableWidget::item:selected {{
                background-color: #3498db;
                color: white;
            }}
            QHeaderView::section {{
                font-family: '{current_font.family()}';
                font-size: {font_size + 1}px;
                font-weight: bold;
                background-color: #34495e;
                color: white;
                padding: 12px 8px;
                border: none;
                text-align: center;
            }}
        """
    
    @staticmethod
    def apply_form_layout_style(form_layout):
        """تطبيق أنماط محسنة على تخطيط النموذج"""
        from PyQt5.QtWidgets import QFormLayout, QLabel
        from PyQt5.QtCore import Qt

        form_layout.setSpacing(15)
        form_layout.setContentsMargins(20, 20, 20, 20)
        form_layout.setLabelAlignment(Qt.AlignRight | Qt.AlignVCenter)
        form_layout.setFormAlignment(Qt.AlignTop)

        # تطبيق أنماط على جميع الليبلز في النموذج
        for i in range(form_layout.rowCount()):
            label_item = form_layout.itemAt(i, QFormLayout.LabelRole)
            if label_item and label_item.widget():
                label = label_item.widget()
                if isinstance(label, QLabel):
                    label.setStyleSheet(UIStyles.get_form_label_style())
                    label.setWordWrap(True)
                    label.setMinimumWidth(120)
