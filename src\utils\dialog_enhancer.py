#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محسن النوافذ - أداة لتحسين مظهر جميع النوافذ والحوارات
"""

from PyQt5.QtWidgets import (QDialog, QLabel, QLineEdit, QTextEdit, QSpinBox, 
                             QDoubleSpinBox, QComboBox, QPushButton, QCheckBox,
                             QFrame, QGroupBox, QFormLayout, QTableWidget)
from PyQt5.QtCore import Qt
from src.utils.ui_styles import UIStyles


class DialogEnhancer:
    """فئة تحسين النوافذ والحوارات"""
    
    @staticmethod
    def enhance_dialog(dialog):
        """تحسين نافذة حوار بالكامل"""
        try:
            # تطبيق الأنماط على جميع العناصر
            DialogEnhancer._enhance_labels(dialog)
            DialogEnhancer._enhance_inputs(dialog)
            DialogEnhancer._enhance_buttons(dialog)
            DialogEnhancer._enhance_checkboxes(dialog)
            DialogEnhancer._enhance_frames(dialog)
            DialogEnhancer._enhance_group_boxes(dialog)
            DialogEnhancer._enhance_tables(dialog)
            DialogEnhancer._enhance_form_layouts(dialog)
            
            print(f"تم تحسين النافذة: {dialog.windowTitle()}")
            
        except Exception as e:
            print(f"خطأ في تحسين النافذة: {e}")
    
    @staticmethod
    def _enhance_labels(parent):
        """تحسين جميع الليبلز"""
        labels = parent.findChildren(QLabel)
        for label in labels:
            # تحديد نوع الليبل وتطبيق النمط المناسب
            if label.parent() and hasattr(label.parent(), 'layout'):
                layout = label.parent().layout()
                if isinstance(layout, QFormLayout):
                    # ليبل في نموذج
                    label.setStyleSheet(UIStyles.get_form_label_style())
                    label.setWordWrap(True)
                    label.setMinimumWidth(120)
                elif "title" in label.objectName().lower() or label.font().pointSize() > 14:
                    # ليبل عنوان
                    label.setStyleSheet(UIStyles.get_title_style())
                else:
                    # ليبل عادي
                    label.setStyleSheet(UIStyles.get_label_style())
            else:
                # ليبل عادي
                label.setStyleSheet(UIStyles.get_label_style())
    
    @staticmethod
    def _enhance_inputs(parent):
        """تحسين جميع حقول الإدخال"""
        inputs = (parent.findChildren(QLineEdit) + 
                 parent.findChildren(QTextEdit) + 
                 parent.findChildren(QSpinBox) + 
                 parent.findChildren(QDoubleSpinBox) + 
                 parent.findChildren(QComboBox))
        
        for input_widget in inputs:
            input_widget.setStyleSheet(UIStyles.get_input_style())
    
    @staticmethod
    def _enhance_buttons(parent):
        """تحسين جميع الأزرار"""
        buttons = parent.findChildren(QPushButton)
        for button in buttons:
            text = button.text().lower()
            if "حفظ" in text or "save" in text:
                button.setStyleSheet(UIStyles.get_button_style("#27ae60", "#229954"))
            elif "إلغاء" in text or "cancel" in text:
                button.setStyleSheet(UIStyles.get_button_style("#95a5a6", "#7f8c8d"))
            elif "حذف" in text or "delete" in text:
                button.setStyleSheet(UIStyles.get_button_style("#e74c3c", "#c0392b"))
            elif "تطبيق" in text or "apply" in text:
                button.setStyleSheet(UIStyles.get_button_style("#3498db", "#2980b9"))
            else:
                button.setStyleSheet(UIStyles.get_button_style())
    
    @staticmethod
    def _enhance_checkboxes(parent):
        """تحسين جميع صناديق الاختيار"""
        checkboxes = parent.findChildren(QCheckBox)
        for checkbox in checkboxes:
            checkbox.setStyleSheet(UIStyles.get_checkbox_style())
    
    @staticmethod
    def _enhance_frames(parent):
        """تحسين جميع الإطارات"""
        frames = parent.findChildren(QFrame)
        for frame in frames:
            if frame.frameStyle() != QFrame.NoFrame:
                frame.setStyleSheet(UIStyles.get_frame_style())
    
    @staticmethod
    def _enhance_group_boxes(parent):
        """تحسين جميع صناديق المجموعات"""
        group_boxes = parent.findChildren(QGroupBox)
        for group_box in group_boxes:
            group_box.setStyleSheet(UIStyles.get_group_box_style())
    
    @staticmethod
    def _enhance_tables(parent):
        """تحسين جميع الجداول"""
        tables = parent.findChildren(QTableWidget)
        for table in tables:
            table.setStyleSheet(UIStyles.get_table_style())
    
    @staticmethod
    def _enhance_form_layouts(parent):
        """تحسين جميع تخطيطات النماذج"""
        # البحث عن جميع QFormLayout في النافذة
        def find_form_layouts(widget):
            layouts = []
            if hasattr(widget, 'layout') and widget.layout():
                layout = widget.layout()
                if isinstance(layout, QFormLayout):
                    layouts.append(layout)
                
                # البحث في التخطيطات الفرعية
                for i in range(layout.count()):
                    item = layout.itemAt(i)
                    if item and item.widget():
                        layouts.extend(find_form_layouts(item.widget()))
            
            # البحث في العناصر الفرعية
            for child in widget.findChildren(QFrame):
                layouts.extend(find_form_layouts(child))
            
            return layouts
        
        form_layouts = find_form_layouts(parent)
        for form_layout in form_layouts:
            UIStyles.apply_form_layout_style(form_layout)


class EnhancedDialog(QDialog):
    """نافذة حوار محسنة تطبق الأنماط تلقائياً"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)
    
    def showEvent(self, event):
        """تطبيق التحسينات عند عرض النافذة"""
        super().showEvent(event)
        DialogEnhancer.enhance_dialog(self)
    
    def setup_enhanced_ui(self, title, width=500, height=400):
        """إعداد واجهة محسنة أساسية"""
        self.setWindowTitle(title)
        self.setFixedSize(width, height)
        
        # إنشاء عنوان محسن
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(UIStyles.get_title_style())
        
        return title_label
    
    def create_enhanced_frame(self):
        """إنشاء إطار محسن"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.StyledPanel)
        frame.setStyleSheet(UIStyles.get_frame_style())
        return frame
    
    def create_enhanced_form_layout(self, parent_frame):
        """إنشاء تخطيط نموذج محسن"""
        form_layout = QFormLayout(parent_frame)
        UIStyles.apply_form_layout_style(form_layout)
        return form_layout
    
    def create_enhanced_button(self, text, button_type="default"):
        """إنشاء زر محسن"""
        button = QPushButton(text)
        button.setFixedHeight(40)
        
        if button_type == "save":
            button.setStyleSheet(UIStyles.get_button_style("#27ae60", "#229954"))
        elif button_type == "cancel":
            button.setStyleSheet(UIStyles.get_button_style("#95a5a6", "#7f8c8d"))
        elif button_type == "delete":
            button.setStyleSheet(UIStyles.get_button_style("#e74c3c", "#c0392b"))
        elif button_type == "apply":
            button.setStyleSheet(UIStyles.get_button_style("#3498db", "#2980b9"))
        else:
            button.setStyleSheet(UIStyles.get_button_style())
        
        return button
    
    def create_enhanced_input(self, input_type="line", placeholder=""):
        """إنشاء حقل إدخال محسن"""
        if input_type == "line":
            widget = QLineEdit()
            widget.setPlaceholderText(placeholder)
        elif input_type == "text":
            widget = QTextEdit()
            widget.setPlaceholderText(placeholder)
            widget.setMaximumHeight(100)
        elif input_type == "spin":
            widget = QSpinBox()
        elif input_type == "double_spin":
            widget = QDoubleSpinBox()
        elif input_type == "combo":
            widget = QComboBox()
        else:
            widget = QLineEdit()
        
        widget.setStyleSheet(UIStyles.get_input_style())
        return widget
    
    def create_enhanced_checkbox(self, text, checked=False):
        """إنشاء صندوق اختيار محسن"""
        checkbox = QCheckBox(text)
        checkbox.setChecked(checked)
        checkbox.setStyleSheet(UIStyles.get_checkbox_style())
        return checkbox


def enhance_all_dialogs():
    """تحسين جميع النوافذ الموجودة في التطبيق"""
    try:
        from PyQt5.QtWidgets import QApplication
        app = QApplication.instance()
        if app:
            for widget in app.allWidgets():
                if isinstance(widget, QDialog) and widget.isVisible():
                    DialogEnhancer.enhance_dialog(widget)
            print("تم تحسين جميع النوافذ المفتوحة")
    except Exception as e:
        print(f"خطأ في تحسين النوافذ: {e}")
