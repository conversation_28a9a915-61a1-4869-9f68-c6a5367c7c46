# تطبيق التوليد التلقائي لأرقام الطلاب والموظفين

## نظرة عامة
تم تطوير نظام التوليد التلقائي لأرقام الطلاب والموظفين لتبسيط عملية إدخال البيانات وضمان تفرد الأرقام وتنظيمها بشكل منهجي.

## الميزات المطبقة

### 1. **التوليد التلقائي لأرقام الطلاب** 🎓
- **تنسيق الرقم**: `STU000001`, `STU000002`, `STU000003`, إلخ
- **البادئة**: `STU` (اختصار Student)
- **طول الرقم**: 6 أرقام مع أصفار بادئة
- **التسلسل**: تلقائي ومتتالي

### 2. **التوليد التلقائي لأرقام الموظفين** 👨‍🏫
- **تنسيق الرقم**: `EMP000001`, `EMP000002`, `EMP000003`, إلخ
- **البادئة**: `EMP` (اختصار Employee)
- **طول الرقم**: 6 أرقام مع أصفار بادئة
- **التسلسل**: تلقائي ومتتالي

## التطبيق التقني

### 1. **نموذج الطلاب (`src/models/student.py`)**

#### دالة توليد رقم الطالب:
```python
def generate_student_number(self):
    """توليد رقم طالب تلقائي"""
    try:
        # الحصول على آخر رقم طالب
        query = "SELECT student_number FROM students ORDER BY student_id DESC LIMIT 1"
        last_student = self.db_manager.fetch_one(query)
        
        if last_student and last_student['student_number']:
            # استخراج الرقم من آخر رقم طالب
            last_number = last_student['student_number']
            if last_number.startswith('STU'):
                try:
                    number_part = int(last_number[3:])
                    new_number = number_part + 1
                except ValueError:
                    new_number = 1
            else:
                new_number = 1
        else:
            new_number = 1
        
        # تكوين رقم الطالب الجديد
        student_number = f"STU{new_number:06d}"  # مثال: STU000001
        
        # التحقق من عدم وجود الرقم مسبقاً
        while self.get_student_by_number(student_number):
            new_number += 1
            student_number = f"STU{new_number:06d}"
        
        return student_number
        
    except Exception as e:
        # في حالة الخطأ، استخدم timestamp
        import time
        timestamp = int(time.time())
        return f"STU{timestamp}"
```

#### تعديل دالة إضافة الطالب:
```python
def add_student(self, student_data):
    """إضافة طالب جديد"""
    # توليد رقم الطالب تلقائياً إذا لم يتم تمريره
    if 'student_number' not in student_data or not student_data['student_number']:
        student_data['student_number'] = self.generate_student_number()
    
    # إزالة student_number من الحقول المطلوبة مؤقتاً للتحقق
    required_fields_temp = [field for field in self.required_fields if field != 'student_number']
    
    # التحقق من الحقول المطلوبة
    self.validate_required_fields(student_data, required_fields_temp)
    
    # باقي عمليات التحقق والإدراج...
    return self.insert(student_data)
```

### 2. **نموذج المعلمين (`src/models/teacher.py`)**

#### دالة توليد رقم الموظف:
```python
def generate_employee_number(self):
    """توليد رقم موظف تلقائي"""
    try:
        # الحصول على آخر رقم موظف
        query = "SELECT employee_number FROM teachers ORDER BY teacher_id DESC LIMIT 1"
        last_teacher = self.db_manager.fetch_one(query)
        
        if last_teacher and last_teacher['employee_number']:
            # استخراج الرقم من آخر رقم موظف
            last_number = last_teacher['employee_number']
            if last_number.startswith('EMP'):
                try:
                    number_part = int(last_number[3:])
                    new_number = number_part + 1
                except ValueError:
                    new_number = 1
            else:
                new_number = 1
        else:
            new_number = 1
        
        # تكوين رقم الموظف الجديد
        employee_number = f"EMP{new_number:06d}"  # مثال: EMP000001
        
        # التحقق من عدم وجود الرقم مسبقاً
        while self.get_teacher_by_employee_number(employee_number):
            new_number += 1
            employee_number = f"EMP{new_number:06d}"
        
        return employee_number
        
    except Exception as e:
        # في حالة الخطأ، استخدم timestamp
        import time
        timestamp = int(time.time())
        return f"EMP{timestamp}"
```

#### تعديل دالة إضافة المعلم:
```python
def add_teacher(self, teacher_data):
    """إضافة معلم أو موظف جديد"""
    # توليد رقم الموظف تلقائياً إذا لم يتم تمريره
    if 'employee_number' not in teacher_data or not teacher_data['employee_number']:
        teacher_data['employee_number'] = self.generate_employee_number()
    
    # إزالة employee_number من الحقول المطلوبة مؤقتاً للتحقق
    required_fields_temp = [field for field in self.required_fields if field != 'employee_number']
    
    # التحقق من الحقول المطلوبة
    self.validate_required_fields(teacher_data, required_fields_temp)
    
    # باقي عمليات التحقق والإدراج...
    return self.insert(teacher_data)
```

## تحديثات واجهة المستخدم

### 1. **نافذة إضافة الطلاب (`src/ui/dialogs/student_dialog.py`)**

#### تعديل حقل رقم الطالب:
```python
# رقم الطالب
self.student_number_input = QLineEdit()
self.student_number_input.setPlaceholderText("سيتم توليده تلقائياً")
self.student_number_input.setReadOnly(True)
self.student_number_input.setStyleSheet("background-color: #f0f0f0; color: #666;")
layout.addRow("رقم الطالب:", self.student_number_input)
```

#### تعديل جمع البيانات:
```python
student_data = {
    'first_name': self.first_name_input.text().strip(),
    'last_name': self.last_name_input.text().strip(),
    # ... باقي الحقول
}

# إضافة رقم الطالب فقط في حالة التعديل
if self.student_id and self.student_number_input.text().strip():
    student_data['student_number'] = self.student_number_input.text().strip()
```

#### تحديث الحقول المطلوبة:
```python
# التحقق من الحقول المطلوبة (رقم الطالب سيتم توليده تلقائياً)
required_fields = ['first_name', 'last_name', 'parent_name', 'parent_phone']
for field in required_fields:
    if not student_data[field]:
        field_names = {
            'first_name': 'الاسم الأول',
            'last_name': 'الاسم الأخير', 
            'parent_name': 'اسم ولي الأمر',
            'parent_phone': 'رقم هاتف ولي الأمر'
        }
        QMessageBox.warning(self, "تحذير", f"الحقل '{field_names.get(field, field)}' مطلوب")
        return
```

### 2. **نافذة إضافة المعلمين (`src/ui/dialogs/teacher_dialog.py`)**

#### تعديل حقل رقم الموظف:
```python
# رقم الموظف
self.employee_number_input = QLineEdit()
self.employee_number_input.setPlaceholderText("سيتم توليده تلقائياً")
self.employee_number_input.setReadOnly(True)
self.employee_number_input.setStyleSheet("background-color: #f0f0f0; color: #666;")
layout.addRow("رقم الموظف:", self.employee_number_input)
```

#### تعديل جمع البيانات:
```python
teacher_data = {
    'first_name': self.first_name_input.text().strip(),
    'last_name': self.last_name_input.text().strip(),
    # ... باقي الحقول
}

# إضافة رقم الموظف فقط في حالة التعديل
if self.teacher_id and self.employee_number_input.text().strip():
    teacher_data['employee_number'] = self.employee_number_input.text().strip()
```

#### تحديث الحقول المطلوبة:
```python
# التحقق من الحقول المطلوبة (رقم الموظف سيتم توليده تلقائياً)
required_fields = ['first_name', 'last_name', 'phone', 'position']
for field in required_fields:
    if not teacher_data[field]:
        field_names = {
            'first_name': 'الاسم الأول',
            'last_name': 'الاسم الأخير',
            'phone': 'رقم الهاتف',
            'position': 'المنصب'
        }
        QMessageBox.warning(self, "تحذير", f"الحقل '{field_names.get(field, field)}' مطلوب")
        return
```

## آلية العمل

### 1. **عند إضافة طالب جديد**:
1. المستخدم يفتح نافذة إضافة طالب
2. حقل رقم الطالب يظهر بلون رمادي مع نص "سيتم توليده تلقائياً"
3. المستخدم يملأ باقي الحقول المطلوبة
4. عند الضغط على "حفظ"، يتم توليد رقم الطالب تلقائياً
5. يتم حفظ الطالب مع الرقم المولد

### 2. **عند إضافة معلم جديد**:
1. المستخدم يفتح نافذة إضافة معلم
2. حقل رقم الموظف يظهر بلون رمادي مع نص "سيتم توليده تلقائياً"
3. المستخدم يملأ باقي الحقول المطلوبة
4. عند الضغط على "حفظ"، يتم توليد رقم الموظف تلقائياً
5. يتم حفظ المعلم مع الرقم المولد

### 3. **عند تعديل بيانات موجودة**:
1. يتم عرض الرقم الحالي في الحقل
2. الحقل يبقى للقراءة فقط
3. لا يتم تغيير الرقم الموجود

## الفوائد المحققة

### 1. **تبسيط الإدخال** 📝:
- ✅ المستخدم لا يحتاج لإدخال رقم الطالب/الموظف يدوياً
- ✅ تقليل الأخطاء البشرية في إدخال الأرقام
- ✅ توفير الوقت في عملية الإدخال

### 2. **ضمان التفرد** 🔒:
- ✅ كل رقم طالب فريد ولا يتكرر
- ✅ كل رقم موظف فريد ولا يتكرر
- ✅ التحقق التلقائي من عدم التكرار

### 3. **التنظيم والترتيب** 📊:
- ✅ أرقام متسلسلة ومنظمة
- ✅ سهولة في البحث والفرز
- ✅ تنسيق موحد لجميع الأرقام

### 4. **المرونة** 🔄:
- ✅ يعمل مع قواعد البيانات الفارغة والممتلئة
- ✅ معالجة الأخطاء مع استخدام timestamp كبديل
- ✅ إمكانية التعديل المستقبلي للتنسيق

## الملفات المحدثة

### الملفات المعدلة:
1. **`src/models/student.py`**:
   - إضافة دالة `generate_student_number()`
   - تعديل دالة `add_student()` للتوليد التلقائي

2. **`src/models/teacher.py`**:
   - إضافة دالة `generate_employee_number()`
   - تعديل دالة `add_teacher()` للتوليد التلقائي

3. **`src/ui/dialogs/student_dialog.py`**:
   - تعديل حقل رقم الطالب ليكون للقراءة فقط
   - تحديث منطق جمع البيانات
   - تحديث الحقول المطلوبة

4. **`src/ui/dialogs/teacher_dialog.py`**:
   - تعديل حقل رقم الموظف ليكون للقراءة فقط
   - تحديث منطق جمع البيانات
   - تحديث الحقول المطلوبة

## اختبار النظام

### خطوات الاختبار:
1. **تشغيل التطبيق** ✅
2. **اختبار إضافة طالب جديد**:
   - فتح نافذة إضافة طالب ✅
   - التحقق من أن حقل رقم الطالب للقراءة فقط ✅
   - ملء البيانات المطلوبة ✅
   - حفظ الطالب ✅
   - التحقق من توليد رقم تلقائي ✅
3. **اختبار إضافة معلم جديد**:
   - فتح نافذة إضافة معلم ✅
   - التحقق من أن حقل رقم الموظف للقراءة فقط ✅
   - ملء البيانات المطلوبة ✅
   - حفظ المعلم ✅
   - التحقق من توليد رقم تلقائي ✅

### النتائج:
- ✅ **التوليد التلقائي يعمل** بشكل صحيح
- ✅ **الأرقام متسلسلة ومنظمة** حسب التنسيق المحدد
- ✅ **واجهة المستخدم محدثة** مع حقول للقراءة فقط
- ✅ **التطبيق مستقر** بدون أخطاء

## النتيجة النهائية

**تم تطبيق نظام التوليد التلقائي لأرقام الطلاب والموظفين بنجاح!**

- ✅ **أرقام الطلاب**: تتولد تلقائياً بتنسيق `STU000001`
- ✅ **أرقام الموظفين**: تتولد تلقائياً بتنسيق `EMP000001`
- ✅ **واجهة مستخدم محسنة** مع حقول للقراءة فقط
- ✅ **تجربة مستخدم مبسطة** بدون حاجة لإدخال الأرقام يدوياً
- ✅ **ضمان التفرد والتنظيم** لجميع الأرقام

الآن يمكن للمستخدمين إضافة طلاب ومعلمين جدد بسهولة أكبر، حيث سيتم توليد الأرقام تلقائياً بشكل منظم ومتسلسل! 🎉📝✨🚀
