#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لقاعدة البيانات
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.database.db_manager import DatabaseManager
from src.models.user import User


def test_simple():
    """اختبار بسيط"""
    print("=== اختبار بسيط لقاعدة البيانات ===")
    
    try:
        # اختبار مدير قاعدة البيانات
        db_manager = DatabaseManager()
        
        # اختبار جلب مستخدم واحد
        user = db_manager.fetch_one("SELECT * FROM users LIMIT 1")
        if user:
            print(f"✓ تم جلب مستخدم: {type(user)}")
            print(f"✓ اسم المستخدم: {user.get('username', 'غير محدد')}")
        
        # اختبار نموذج المستخدمين
        user_model = User()
        users = user_model.get_all()
        print(f"✓ تم جلب {len(users)} مستخدم من النموذج")
        
        if users:
            first_user = users[0]
            print(f"✓ نوع البيانات: {type(first_user)}")
            print(f"✓ المفاتيح المتاحة: {list(first_user.keys())}")
        
        print("✅ جميع الاختبارات نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_simple()
    print(f"\nالنتيجة: {'نجح' if success else 'فشل'}")
    sys.exit(0 if success else 1)
