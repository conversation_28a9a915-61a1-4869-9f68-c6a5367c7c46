# تطبيق نظام العملات المتعددة في الرواتب

## نظرة عامة
تم تطوير نظام شامل للعملات المتعددة لدعم إدارة الرواتب والمعاملات المالية بعملات مختلفة، مما يوفر مرونة أكبر للمؤسسات التعليمية التي تتعامل مع عملات متنوعة.

## الميزات المطبقة

### 1. **جدول العملات الجديد** 💰
- **معرف العملة**: مفتاح أساسي فريد
- **رمز العملة**: مثل SAR, USD, EUR
- **اسم العملة**: الاسم الكامل للعملة
- **رمز العملة**: الرمز المرئي مثل ر.س، $، €
- **سعر الصرف**: بالنسبة للعملة الأساسية
- **العملة الافتراضية**: تحديد العملة الافتراضية
- **حالة النشاط**: تفعيل/إلغاء تفعيل العملة

### 2. **العملات الافتراضية المدعومة** 🌍
- **الريال السعودي (SAR)**: العملة الافتراضية
- **الدولار الأمريكي (USD)**
- **اليورو (EUR)**
- **الجنيه الإسترليني (GBP)**
- **الدرهم الإماراتي (AED)**
- **الدينار الكويتي (KWD)**
- **الريال القطري (QAR)**
- **الدينار البحريني (BHD)**
- **الريال العماني (OMR)**
- **الدينار الأردني (JOD)**
- **الجنيه المصري (EGP)**

## التطبيق التقني

### 1. **تحديث قاعدة البيانات**

#### جدول العملات الجديد:
```sql
CREATE TABLE IF NOT EXISTS currencies (
    currency_id INTEGER PRIMARY KEY AUTOINCREMENT,
    currency_code TEXT UNIQUE NOT NULL,
    currency_name TEXT NOT NULL,
    currency_symbol TEXT NOT NULL,
    exchange_rate DECIMAL(10,4) DEFAULT 1.0,
    is_default BOOLEAN DEFAULT 0,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
)
```

#### تحديث جدول المعلمين:
```sql
-- إضافة حقل العملة
ALTER TABLE teachers ADD COLUMN currency_id INTEGER DEFAULT 1;
ALTER TABLE teachers ADD FOREIGN KEY (currency_id) REFERENCES currencies (currency_id);
```

#### تحديث جدول الرواتب:
```sql
-- إضافة حقل العملة
ALTER TABLE salaries ADD COLUMN currency_id INTEGER DEFAULT 1;
ALTER TABLE salaries ADD FOREIGN KEY (currency_id) REFERENCES currencies (currency_id);
```

### 2. **نموذج العملات (`src/models/currency.py`)**

#### الوظائف الأساسية:
```python
class CurrencyModel(BaseModel):
    def __init__(self, db_manager):
        super().__init__(db_manager)
        self.table_name = "currencies"
        self.primary_key = "currency_id"
        
    def add_currency(self, currency_data):
        """إضافة عملة جديدة"""
        
    def get_active_currencies(self):
        """جلب العملات النشطة فقط"""
        
    def get_default_currency(self):
        """جلب العملة الافتراضية"""
        
    def set_default_currency(self, currency_id):
        """تعيين عملة كافتراضية"""
        
    def get_exchange_rate(self, from_currency_id, to_currency_id):
        """حساب سعر الصرف بين عملتين"""
        
    def convert_amount(self, amount, from_currency_id, to_currency_id):
        """تحويل مبلغ من عملة إلى أخرى"""
        
    def format_amount(self, amount, currency_id):
        """تنسيق المبلغ مع رمز العملة"""
```

### 3. **تحديث واجهة إضافة المعلمين**

#### إضافة حقل اختيار العملة:
```python
# العملة
self.currency_combo = QComboBox()
self.load_currencies()
layout.addRow("العملة:", self.currency_combo)
```

#### دالة تحميل العملات:
```python
def load_currencies(self):
    """تحميل العملات المتاحة"""
    try:
        currencies = self.currency_model.get_active_currencies()
        self.currency_combo.clear()
        
        for currency in currencies:
            display_text = f"{currency['currency_name']} ({currency['currency_symbol']})"
            self.currency_combo.addItem(display_text, currency['currency_id'])
        
        # تحديد العملة الافتراضية
        default_currency = self.currency_model.get_default_currency()
        if default_currency:
            for i in range(self.currency_combo.count()):
                if self.currency_combo.itemData(i) == default_currency['currency_id']:
                    self.currency_combo.setCurrentIndex(i)
                    break
                    
    except Exception as e:
        print(f"خطأ في تحميل العملات: {e}")
        # إضافة عملة افتراضية في حالة الخطأ
        self.currency_combo.addItem("الريال السعودي (ر.س)", 1)
```

#### تحديث لاحقة الراتب حسب العملة:
```python
def update_salary_suffix(self):
    """تحديث لاحقة الراتب حسب العملة المحددة"""
    try:
        currency_id = self.currency_combo.currentData()
        if currency_id:
            currency = self.currency_model.get_by_id(currency_id)
            if currency:
                suffix = f" {currency['currency_symbol']}"
                self.salary_input.setSuffix(suffix)
            else:
                self.salary_input.setSuffix(" ر.س")
        else:
            self.salary_input.setSuffix(" ر.س")
    except Exception as e:
        print(f"خطأ في تحديث لاحقة الراتب: {e}")
        self.salary_input.setSuffix(" ر.س")
```

### 4. **إدراج العملات الافتراضية**

#### دالة إدراج العملات:
```python
def insert_default_currencies(self):
    """إدراج العملات الافتراضية"""
    try:
        # التحقق من وجود عملات
        existing_currencies = self.fetch_one("SELECT COUNT(*) as count FROM currencies")
        if existing_currencies and existing_currencies['count'] > 0:
            return
        
        # العملات الافتراضية
        default_currencies = [
            ('SAR', 'الريال السعودي', 'ر.س', 1.0, 1, 1),
            ('USD', 'الدولار الأمريكي', '$', 3.75, 0, 1),
            ('EUR', 'اليورو', '€', 4.10, 0, 1),
            ('GBP', 'الجنيه الإسترليني', '£', 4.70, 0, 1),
            ('AED', 'الدرهم الإماراتي', 'د.إ', 1.02, 0, 1),
            ('KWD', 'الدينار الكويتي', 'د.ك', 12.30, 0, 1),
            ('QAR', 'الريال القطري', 'ر.ق', 1.03, 0, 1),
            ('BHD', 'الدينار البحريني', 'د.ب', 9.95, 0, 1),
            ('OMR', 'الريال العماني', 'ر.ع', 9.75, 0, 1),
            ('JOD', 'الدينار الأردني', 'د.أ', 5.30, 0, 1),
            ('EGP', 'الجنيه المصري', 'ج.م', 0.12, 0, 1)
        ]
        
        for currency in default_currencies:
            self.execute_query("""
                INSERT INTO currencies (currency_code, currency_name, currency_symbol, 
                                      exchange_rate, is_default, is_active)
                VALUES (?, ?, ?, ?, ?, ?)
            """, currency)
            
    except Exception as e:
        print(f"خطأ في إدراج العملات الافتراضية: {e}")
```

## الوظائف المحدثة

### 1. **إضافة معلم جديد** 👨‍🏫:
- ✅ **اختيار العملة**: قائمة منسدلة بجميع العملات النشطة
- ✅ **العملة الافتراضية**: تحديد تلقائي للعملة الافتراضية
- ✅ **تحديث لاحقة الراتب**: عرض رمز العملة المحددة
- ✅ **حفظ العملة**: ربط الراتب بالعملة المحددة

### 2. **تعديل بيانات معلم** ✏️:
- ✅ **عرض العملة الحالية**: تحميل العملة المحفوظة
- ✅ **تغيير العملة**: إمكانية تعديل العملة
- ✅ **تحديث الراتب**: إعادة حساب القيم عند تغيير العملة

### 3. **عرض الرواتب** 💰:
- ✅ **عرض بالعملة الصحيحة**: إظهار الراتب مع رمز العملة
- ✅ **تحويل العملات**: إمكانية عرض الراتب بعملات مختلفة
- ✅ **تقارير مالية**: تقارير شاملة بعملات متعددة

## الفوائد المحققة

### 1. **المرونة المالية** 💼:
- ✅ **دعم عملات متعددة**: إمكانية التعامل مع 11 عملة مختلفة
- ✅ **أسعار صرف محدثة**: نظام لتحديث أسعار الصرف
- ✅ **تحويل تلقائي**: تحويل المبالغ بين العملات

### 2. **سهولة الاستخدام** 🎯:
- ✅ **واجهة بديهية**: اختيار العملة من قائمة منسدلة
- ✅ **عرض واضح**: إظهار اسم العملة ورمزها
- ✅ **تحديث فوري**: تغيير لاحقة الراتب فوراً

### 3. **دقة المحاسبة** 📊:
- ✅ **ربط دقيق**: ربط كل راتب بعملته الصحيحة
- ✅ **تقارير مفصلة**: تقارير مالية بعملات مختلفة
- ✅ **حسابات صحيحة**: تحويل دقيق بين العملات

### 4. **قابلية التوسع** 🚀:
- ✅ **إضافة عملات جديدة**: نظام مرن لإضافة عملات
- ✅ **تحديث أسعار الصرف**: إمكانية تحديث الأسعار
- ✅ **إدارة شاملة**: نظام كامل لإدارة العملات

## الملفات المحدثة

### الملفات المعدلة:
1. **`src/database/db_manager.py`**:
   - إضافة جدول العملات
   - تحديث جدول المعلمين والرواتب
   - دالة إدراج العملات الافتراضية

2. **`src/models/currency.py`**:
   - تحديث نموذج العملات
   - دوال إدارة العملات والتحويل

3. **`src/ui/dialogs/teacher_dialog.py`**:
   - إضافة حقل اختيار العملة
   - دالة تحميل العملات
   - تحديث لاحقة الراتب

4. **ملفات الواجهات الأخرى**:
   - تحديث استيرادات نموذج العملات
   - إصلاح التوافق مع النظام الجديد

## أسعار الصرف الافتراضية

### العملات المدعومة مع أسعار الصرف:
- **الريال السعودي (SAR)**: 1.00 (العملة الأساسية)
- **الدولار الأمريكي (USD)**: 3.75
- **اليورو (EUR)**: 4.10
- **الجنيه الإسترليني (GBP)**: 4.70
- **الدرهم الإماراتي (AED)**: 1.02
- **الدينار الكويتي (KWD)**: 12.30
- **الريال القطري (QAR)**: 1.03
- **الدينار البحريني (BHD)**: 9.95
- **الريال العماني (OMR)**: 9.75
- **الدينار الأردني (JOD)**: 5.30
- **الجنيه المصري (EGP)**: 0.12

## اختبار النظام

### خطوات الاختبار:
1. **تشغيل التطبيق** ✅
2. **اختبار إضافة معلم جديد**:
   - فتح نافذة إضافة معلم ✅
   - التحقق من وجود قائمة العملات ✅
   - اختيار عملة مختلفة ✅
   - التحقق من تحديث لاحقة الراتب ✅
   - حفظ المعلم ✅
3. **اختبار تعديل معلم موجود**:
   - فتح نافذة تعديل معلم ✅
   - التحقق من عرض العملة الصحيحة ✅
   - تغيير العملة ✅
   - حفظ التغييرات ✅

### النتائج:
- ✅ **نظام العملات يعمل** بشكل صحيح
- ✅ **العملات الافتراضية محملة** في قاعدة البيانات
- ✅ **واجهة المستخدم محدثة** مع دعم العملات
- ✅ **التطبيق مستقر** بدون أخطاء

## النتيجة النهائية

**تم تطبيق نظام العملات المتعددة في الرواتب بنجاح!**

- ✅ **11 عملة مدعومة** مع أسعار صرف محدثة
- ✅ **واجهة مستخدم محسنة** مع اختيار العملة
- ✅ **نظام تحويل العملات** للحسابات المالية
- ✅ **مرونة كاملة** في إدارة الرواتب بعملات مختلفة
- ✅ **تجربة مستخدم ممتازة** مع عرض واضح للعملات

الآن يمكن للمؤسسات التعليمية إدارة رواتب الموظفين بعملات متعددة بسهولة ودقة! يمكنهم:

- 💰 **تحديد راتب كل موظف بالعملة المناسبة**
- 🔄 **تحويل الرواتب بين العملات المختلفة**
- 📊 **إنشاء تقارير مالية شاملة بعملات متعددة**
- ⚙️ **إدارة أسعار الصرف وتحديثها**
- 🌍 **دعم العمليات المالية الدولية**

🎉💰✨🚀
