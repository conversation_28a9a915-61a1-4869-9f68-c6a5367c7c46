#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نموذج العملات والإعدادات المالية
يحتوي على جميع العمليات المتعلقة بإدارة العملات وأسعار الصرف
"""

from .base_model import BaseModel
from datetime import datetime


class CurrencyModel(BaseModel):
    """نموذج العملات"""

    def __init__(self, db_manager):
        super().__init__(db_manager)
        self.table_name = "currencies"
        self.primary_key = "currency_id"

        # الحقول المطلوبة
        self.required_fields = [
            'currency_code', 'currency_name', 'symbol'
        ]

        # حقول البحث
        self.search_fields = [
            'currency_code', 'currency_name', 'symbol'
        ]
    
    def create_tables(self):
        """إنشاء جداول العملات"""
        try:
            # جدول العملات
            currencies_table = """
            CREATE TABLE IF NOT EXISTS currencies (
                currency_id INTEGER PRIMARY KEY AUTOINCREMENT,
                currency_code TEXT UNIQUE NOT NULL,
                currency_name TEXT NOT NULL,
                symbol TEXT NOT NULL,
                exchange_rate REAL DEFAULT 1.0,
                is_base_currency BOOLEAN DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at TEXT,
                updated_at TEXT
            )
            """
            
            # جدول أسعار الصرف التاريخية
            exchange_rates_table = """
            CREATE TABLE IF NOT EXISTS exchange_rates (
                rate_id INTEGER PRIMARY KEY AUTOINCREMENT,
                from_currency_id INTEGER,
                to_currency_id INTEGER,
                rate REAL NOT NULL,
                effective_date TEXT,
                created_at TEXT,
                FOREIGN KEY (from_currency_id) REFERENCES currencies (currency_id),
                FOREIGN KEY (to_currency_id) REFERENCES currencies (currency_id)
            )
            """
            
            # جدول الإعدادات المالية
            financial_settings_table = """
            CREATE TABLE IF NOT EXISTS financial_settings (
                setting_id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_key TEXT UNIQUE NOT NULL,
                setting_value TEXT,
                description TEXT,
                updated_at TEXT
            )
            """
            
            self.db_manager.execute_query(currencies_table)
            self.db_manager.execute_query(exchange_rates_table)
            self.db_manager.execute_query(financial_settings_table)
            
            # إضافة البيانات الافتراضية
            self.create_default_currencies()
            
        except Exception as e:
            print(f"خطأ في إنشاء جداول العملات: {e}")
    
    def create_default_currencies(self):
        """إنشاء العملات الافتراضية"""
        try:
            # التحقق من وجود عملات
            existing_currencies = self.get_all()
            if existing_currencies:
                return
            
            # العملات الافتراضية
            default_currencies = [
                {
                    'currency_code': 'SAR',
                    'currency_name': 'الريال السعودي',
                    'symbol': 'ر.س',
                    'exchange_rate': 1.0,
                    'is_base_currency': True,
                    'is_active': True
                },
                {
                    'currency_code': 'USD',
                    'currency_name': 'الدولار الأمريكي',
                    'symbol': '$',
                    'exchange_rate': 3.75,
                    'is_base_currency': False,
                    'is_active': True
                },
                {
                    'currency_code': 'EUR',
                    'currency_name': 'اليورو',
                    'symbol': '€',
                    'exchange_rate': 4.10,
                    'is_base_currency': False,
                    'is_active': True
                },
                {
                    'currency_code': 'GBP',
                    'currency_name': 'الجنيه الإسترليني',
                    'symbol': '£',
                    'exchange_rate': 4.65,
                    'is_base_currency': False,
                    'is_active': True
                }
            ]
            
            for currency_data in default_currencies:
                self.add_currency(currency_data)
                
        except Exception as e:
            print(f"خطأ في إنشاء العملات الافتراضية: {e}")
    
    def add_currency(self, currency_data):
        """إضافة عملة جديدة"""
        try:
            # التحقق من الحقول المطلوبة
            self.validate_required_fields(currency_data, self.required_fields)
            
            # التحقق من تفرد رمز العملة
            if self.currency_code_exists(currency_data['currency_code']):
                raise ValueError("رمز العملة موجود مسبقاً")
            
            # إضافة القيم الافتراضية
            if 'exchange_rate' not in currency_data:
                currency_data['exchange_rate'] = 1.0
            if 'is_base_currency' not in currency_data:
                currency_data['is_base_currency'] = False
            if 'is_active' not in currency_data:
                currency_data['is_active'] = True
            
            # إضافة تاريخ الإنشاء
            currency_data['created_at'] = datetime.now().isoformat()
            currency_data['updated_at'] = datetime.now().isoformat()
            
            return self.insert(currency_data)
        except Exception as e:
            raise Exception(f"خطأ في إضافة العملة: {str(e)}")
    
    def update_currency(self, currency_id, currency_data):
        """تحديث بيانات عملة"""
        try:
            # التحقق من وجود العملة
            existing_currency = self.get_by_id(currency_id)
            if not existing_currency:
                raise ValueError("العملة غير موجودة")
            
            # التحقق من تفرد رمز العملة
            if 'currency_code' in currency_data:
                if self.currency_code_exists(currency_data['currency_code'], currency_id):
                    raise ValueError("رمز العملة موجود مسبقاً")
            
            # إضافة تاريخ التحديث
            currency_data['updated_at'] = datetime.now().isoformat()
            
            return self.update(currency_id, currency_data)
        except Exception as e:
            raise Exception(f"خطأ في تحديث العملة: {str(e)}")
    
    def delete_currency(self, currency_id):
        """حذف عملة"""
        try:
            # التحقق من أن العملة ليست العملة الأساسية
            currency = self.get_by_id(currency_id)
            if currency and currency.get('is_base_currency'):
                raise ValueError("لا يمكن حذف العملة الأساسية")
            
            return self.delete(currency_id)
        except Exception as e:
            raise Exception(f"خطأ في حذف العملة: {str(e)}")
    
    def get_all_currencies(self):
        """الحصول على جميع العملات"""
        return self.get_all()
    
    def get_active_currencies(self):
        """جلب العملات النشطة فقط"""
        return self.get_all("is_active = 1")

    def get_default_currency(self):
        """جلب العملة الافتراضية"""
        return self.db_manager.fetch_one(
            "SELECT * FROM currencies WHERE is_default = 1 AND is_active = 1"
        )
    
    def get_base_currency(self):
        """الحصول على العملة الأساسية"""
        return self.db_manager.fetch_one(
            "SELECT * FROM currencies WHERE is_base_currency = 1 LIMIT 1"
        )
    
    def set_base_currency(self, currency_id):
        """تعيين العملة الأساسية"""
        try:
            # إلغاء تعيين العملة الأساسية الحالية
            self.db_manager.execute_query(
                "UPDATE currencies SET is_base_currency = 0"
            )
            
            # تعيين العملة الجديدة كأساسية
            self.update_currency(currency_id, {'is_base_currency': True})
            
            return True
        except Exception as e:
            raise Exception(f"خطأ في تعيين العملة الأساسية: {str(e)}")
    
    def currency_code_exists(self, currency_code, exclude_currency_id=None):
        """التحقق من وجود رمز العملة"""
        try:
            if exclude_currency_id:
                where_clause = "currency_code = ? AND currency_id != ?"
                params = (currency_code, exclude_currency_id)
            else:
                where_clause = "currency_code = ?"
                params = (currency_code,)
            
            return self.exists(where_clause, params)
        except Exception:
            return False
    
    def search_currencies(self, search_term):
        """البحث في العملات"""
        return self.search(search_term, self.search_fields)
    
    def get_exchange_rate(self, from_currency_code, to_currency_code):
        """الحصول على سعر الصرف بين عملتين"""
        try:
            query = """
            SELECT 
                (fc.exchange_rate / tc.exchange_rate) as rate
            FROM currencies fc, currencies tc
            WHERE fc.currency_code = ? AND tc.currency_code = ?
            AND fc.is_active = 1 AND tc.is_active = 1
            """
            result = self.db_manager.fetch_one(query, (from_currency_code, to_currency_code))
            return result['rate'] if result else 1.0
        except Exception:
            return 1.0
    
    def update_exchange_rate(self, currency_id, new_rate):
        """تحديث سعر الصرف لعملة"""
        try:
            # حفظ السعر في التاريخ
            self.save_exchange_rate_history(currency_id, new_rate)
            
            # تحديث السعر الحالي
            return self.update_currency(currency_id, {'exchange_rate': new_rate})
        except Exception as e:
            raise Exception(f"خطأ في تحديث سعر الصرف: {str(e)}")
    
    def save_exchange_rate_history(self, currency_id, rate):
        """حفظ تاريخ أسعار الصرف"""
        try:
            base_currency = self.get_base_currency()
            if not base_currency:
                return
            
            history_data = {
                'from_currency_id': base_currency['currency_id'],
                'to_currency_id': currency_id,
                'rate': rate,
                'effective_date': datetime.now().isoformat(),
                'created_at': datetime.now().isoformat()
            }
            
            query = """
            INSERT INTO exchange_rates 
            (from_currency_id, to_currency_id, rate, effective_date, created_at)
            VALUES (?, ?, ?, ?, ?)
            """
            
            self.db_manager.execute_query(query, (
                history_data['from_currency_id'],
                history_data['to_currency_id'],
                history_data['rate'],
                history_data['effective_date'],
                history_data['created_at']
            ))
            
        except Exception as e:
            print(f"خطأ في حفظ تاريخ سعر الصرف: {e}")
    
    def get_exchange_rate_history(self, currency_id, limit=10):
        """الحصول على تاريخ أسعار الصرف"""
        try:
            query = """
            SELECT er.*, fc.currency_code as from_currency, tc.currency_code as to_currency
            FROM exchange_rates er
            JOIN currencies fc ON er.from_currency_id = fc.currency_id
            JOIN currencies tc ON er.to_currency_id = tc.currency_id
            WHERE er.to_currency_id = ?
            ORDER BY er.effective_date DESC
            LIMIT ?
            """
            return self.db_manager.fetch_all(query, (currency_id, limit))
        except Exception:
            return []
    
    def get_currencies_statistics(self):
        """إحصائيات العملات"""
        try:
            stats = {}
            
            # إجمالي العملات
            stats['total_currencies'] = self.count()
            
            # العملات النشطة
            stats['active_currencies'] = self.count("is_active = 1")
            
            # العملات غير النشطة
            stats['inactive_currencies'] = self.count("is_active = 0")
            
            # العملة الأساسية
            base_currency = self.get_base_currency()
            stats['base_currency'] = base_currency['currency_code'] if base_currency else 'غير محدد'
            
            return stats
        except Exception as e:
            raise Exception(f"خطأ في جلب إحصائيات العملات: {str(e)}")
    
    def convert_amount(self, amount, from_currency_code, to_currency_code):
        """تحويل مبلغ من عملة إلى أخرى"""
        try:
            if from_currency_code == to_currency_code:
                return amount
            
            rate = self.get_exchange_rate(from_currency_code, to_currency_code)
            return amount * rate
        except Exception:
            return amount
