#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نموذج المواد الدراسية
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.models.subject import Subject


def test_subject_methods():
    """اختبار دوال نموذج المواد الدراسية"""
    print("=== اختبار نموذج المواد الدراسية ===")
    
    try:
        subject_model = Subject()
        
        # اختبار وجود الدوال
        print("اختبار وجود الدوال:")
        
        # get_all_subjects
        if hasattr(subject_model, 'get_all_subjects'):
            print("✓ get_all_subjects موجودة")
            subjects = subject_model.get_all_subjects()
            print(f"✓ تم جلب {len(subjects)} مادة")
        else:
            print("✗ get_all_subjects غير موجودة")
            return False
        
        # get_subjects_statistics
        if hasattr(subject_model, 'get_subjects_statistics'):
            print("✓ get_subjects_statistics موجودة")
            stats = subject_model.get_subjects_statistics()
            print(f"✓ إحصائيات المواد: {stats.get('active_subjects', 0)} نشط")
        else:
            print("✗ get_subjects_statistics غير موجودة")
            return False
        
        # get_active_subjects
        if hasattr(subject_model, 'get_active_subjects'):
            print("✓ get_active_subjects موجودة")
            active_subjects = subject_model.get_active_subjects()
            print(f"✓ المواد النشطة: {len(active_subjects)}")
        else:
            print("✗ get_active_subjects غير موجودة")
            return False
        
        # search_subjects
        if hasattr(subject_model, 'search_subjects'):
            print("✓ search_subjects موجودة")
            search_results = subject_model.search_subjects("test")
            print(f"✓ نتائج البحث: {len(search_results)}")
        else:
            print("✗ search_subjects غير موجودة")
            return False
        
        # get_subject_statistics
        if hasattr(subject_model, 'get_subject_statistics'):
            print("✓ get_subject_statistics موجودة")
            old_stats = subject_model.get_subject_statistics()
            print(f"✓ الإحصائيات القديمة: {old_stats.get('total_active', 0)} نشط")
        else:
            print("✗ get_subject_statistics غير موجودة")
        
        # validate_subject_data
        if hasattr(subject_model, 'validate_subject_data'):
            print("✓ validate_subject_data موجودة")
        else:
            print("✗ validate_subject_data غير موجودة")
        
        # subject_code_exists
        if hasattr(subject_model, 'subject_code_exists'):
            print("✓ subject_code_exists موجودة")
        else:
            print("✗ subject_code_exists غير موجودة")
        
        print("\n=== جميع الدوال تعمل بنجاح! ===")
        return True
        
    except Exception as e:
        print(f"✗ خطأ في اختبار نموذج المواد: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_subject_data_access():
    """اختبار الوصول لبيانات المواد"""
    print("\n=== اختبار الوصول لبيانات المواد ===")
    
    try:
        subject_model = Subject()
        subjects = subject_model.get_all_subjects()
        
        if subjects:
            subject = subjects[0]
            print(f"✓ نوع البيانات: {type(subject)}")
            
            # اختبار الوصول للمفاتيح
            subject_code = subject.get('subject_code', 'غير محدد')
            subject_name = subject.get('subject_name', 'غير محدد')
            subject_id = subject.get('subject_id', 0)
            
            print(f"✓ رمز المادة: {subject_code}")
            print(f"✓ اسم المادة: {subject_name}")
            print(f"✓ معرف المادة: {subject_id}")
            
            return True
        else:
            print("✓ لا توجد مواد في قاعدة البيانات (طبيعي)")
            return True
            
    except Exception as e:
        print(f"✗ خطأ في اختبار الوصول للبيانات: {e}")
        return False


def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار نموذج المواد الدراسية")
    print("=" * 50)
    
    tests = [
        test_subject_methods,
        test_subject_data_access
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبار: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 نموذج المواد الدراسية يعمل بنجاح!")
        print("\n✅ الدوال المتاحة:")
        print("   - get_all_subjects()")
        print("   - get_subjects_statistics()")
        print("   - get_active_subjects()")
        print("   - search_subjects()")
        print("   - validate_subject_data()")
        print("   - subject_code_exists()")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
