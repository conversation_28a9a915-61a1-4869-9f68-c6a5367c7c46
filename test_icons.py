#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الأيقونات والصور
يعرض نافذة اختبار لجميع الأيقونات المتاحة
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QWidget, QLabel, QPushButton, QScrollArea, QGridLayout,
                             QGroupBox, QFrame)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon

from src.utils.icon_manager import IconManager, get_icon


class IconTestWindow(QMainWindow):
    """نافذة اختبار الأيقونات"""
    
    def __init__(self):
        super().__init__()
        self.icon_manager = IconManager()
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("اختبار الأيقونات - Icon Test")
        self.setGeometry(100, 100, 900, 700)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تعيين أيقونة النافذة
        self.setWindowIcon(self.icon_manager.get_app_icon())
        
        # الويدجت الرئيسي
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # عنوان النافذة
        title_label = QLabel("اختبار الأيقونات والصور")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        scroll_layout.addWidget(title_label)
        
        # معلومات الأيقونات
        self.add_icon_info(scroll_layout)
        
        # أيقونات القوائم
        self.add_menu_icons_test(scroll_layout)
        
        # أيقونات الأزرار
        self.add_button_icons_test(scroll_layout)
        
        # أيقونات الحالة
        self.add_status_icons_test(scroll_layout)
        
        # اختبار الأحجام المختلفة
        self.add_size_test(scroll_layout)
        
        # إعداد منطقة التمرير
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(main_widget)
        main_layout.addWidget(scroll_area)
        
    def add_icon_info(self, layout):
        """إضافة معلومات الأيقونات"""
        group = QGroupBox("معلومات الأيقونات")
        group_layout = QVBoxLayout(group)
        
        # عدد الأيقونات المتاحة
        available_icons = self.icon_manager.get_available_icons()
        info_label = QLabel(f"عدد الأيقونات المتاحة: {len(available_icons)}")
        info_label.setStyleSheet("font-size: 14px; padding: 10px;")
        group_layout.addWidget(info_label)
        
        # قائمة الأيقونات
        if available_icons:
            icons_text = "الأيقونات المتاحة: " + ", ".join(available_icons)
        else:
            icons_text = "لا توجد أيقونات متاحة - سيتم إنشاؤها تلقائياً"
        
        icons_label = QLabel(icons_text)
        icons_label.setWordWrap(True)
        icons_label.setStyleSheet("font-size: 12px; padding: 10px; background-color: #f8f9fa; border-radius: 5px;")
        group_layout.addWidget(icons_label)
        
        layout.addWidget(group)
        
    def add_menu_icons_test(self, layout):
        """إضافة اختبار أيقونات القوائم"""
        group = QGroupBox("أيقونات القوائم")
        group_layout = QGridLayout(group)
        
        menu_icons = [
            ("dashboard", "الرئيسية"),
            ("students", "الطلاب"),
            ("teachers", "المعلمين"),
            ("subjects", "المواد"),
            ("classes", "الصفوف"),
            ("fees", "الرسوم"),
            ("results", "النتائج"),
            ("reports", "التقارير"),
            ("settings", "الإعدادات"),
            ("users", "المستخدمين"),
            ("currency", "العملات")
        ]
        
        row = 0
        col = 0
        for icon_name, text in menu_icons:
            # إنشاء إطار للأيقونة
            frame = QFrame()
            frame.setFrameStyle(QFrame.StyledPanel)
            frame.setFixedSize(120, 80)
            frame_layout = QVBoxLayout(frame)
            
            # الأيقونة
            icon_label = QLabel()
            icon = get_icon(icon_name, 32)
            if icon:
                icon_label.setPixmap(icon.pixmap(32, 32))
            icon_label.setAlignment(Qt.AlignCenter)
            
            # النص
            text_label = QLabel(text)
            text_label.setAlignment(Qt.AlignCenter)
            text_label.setStyleSheet("font-size: 10px;")
            
            frame_layout.addWidget(icon_label)
            frame_layout.addWidget(text_label)
            
            group_layout.addWidget(frame, row, col)
            
            col += 1
            if col >= 4:
                col = 0
                row += 1
        
        layout.addWidget(group)
        
    def add_button_icons_test(self, layout):
        """إضافة اختبار أيقونات الأزرار"""
        group = QGroupBox("أيقونات الأزرار")
        group_layout = QHBoxLayout(group)
        
        button_icons = [
            ("add", "إضافة"),
            ("edit", "تعديل"),
            ("delete", "حذف"),
            ("save", "حفظ"),
            ("cancel", "إلغاء"),
            ("search", "بحث"),
            ("print", "طباعة"),
            ("export", "تصدير"),
            ("refresh", "تحديث")
        ]
        
        for icon_name, text in button_icons:
            button = QPushButton(text)
            icon = get_icon(icon_name, 16)
            if icon:
                button.setIcon(icon)
            button.setStyleSheet("""
                QPushButton {
                    padding: 8px 12px;
                    margin: 2px;
                    background-color: #3498db;
                    color: white;
                    border: none;
                    border-radius: 4px;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
            """)
            group_layout.addWidget(button)
        
        layout.addWidget(group)
        
    def add_status_icons_test(self, layout):
        """إضافة اختبار أيقونات الحالة"""
        group = QGroupBox("أيقونات الحالة")
        group_layout = QHBoxLayout(group)
        
        status_icons = [
            ("success", "نجح", "#27ae60"),
            ("warning", "تحذير", "#f39c12"),
            ("error", "خطأ", "#e74c3c"),
            ("info", "معلومات", "#3498db"),
            ("question", "سؤال", "#9b59b6")
        ]
        
        for icon_name, text, color in status_icons:
            frame = QFrame()
            frame.setFrameStyle(QFrame.StyledPanel)
            frame.setFixedSize(100, 60)
            frame.setStyleSheet(f"background-color: {color}; border-radius: 5px;")
            frame_layout = QVBoxLayout(frame)
            
            # الأيقونة
            icon_label = QLabel()
            icon = get_icon(icon_name, 24)
            if icon:
                icon_label.setPixmap(icon.pixmap(24, 24))
            icon_label.setAlignment(Qt.AlignCenter)
            
            # النص
            text_label = QLabel(text)
            text_label.setAlignment(Qt.AlignCenter)
            text_label.setStyleSheet("color: white; font-size: 10px; font-weight: bold;")
            
            frame_layout.addWidget(icon_label)
            frame_layout.addWidget(text_label)
            
            group_layout.addWidget(frame)
        
        layout.addWidget(group)
        
    def add_size_test(self, layout):
        """إضافة اختبار الأحجام المختلفة"""
        group = QGroupBox("اختبار الأحجام المختلفة")
        group_layout = QHBoxLayout(group)
        
        sizes = [16, 24, 32, 48, 64]
        
        for size in sizes:
            frame = QFrame()
            frame.setFrameStyle(QFrame.StyledPanel)
            frame.setFixedSize(size + 20, size + 40)
            frame_layout = QVBoxLayout(frame)
            
            # الأيقونة
            icon_label = QLabel()
            icon = get_icon("dashboard", size)
            if icon:
                icon_label.setPixmap(icon.pixmap(size, size))
            icon_label.setAlignment(Qt.AlignCenter)
            
            # حجم النص
            size_label = QLabel(f"{size}px")
            size_label.setAlignment(Qt.AlignCenter)
            size_label.setStyleSheet("font-size: 10px;")
            
            frame_layout.addWidget(icon_label)
            frame_layout.addWidget(size_label)
            
            group_layout.addWidget(frame)
        
        layout.addWidget(group)


def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # إعداد اللغة العربية
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء النافذة
    window = IconTestWindow()
    window.show()
    
    # تشغيل التطبيق
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
