#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ويدجت إدارة العملات والإعدادات المالية
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                             QMessageBox, QHeaderView, QFrame, QGroupBox,
                             QComboBox, QDoubleSpinBox, QCheckBox, QFormLayout,
                             QTabWidget, QTextEdit, QSpinBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon

from src.models.currency import CurrencyModel
from src.ui.dialogs.currency_dialog import CurrencyDialog


class CurrencyWidget(QWidget):
    """ويدجت إدارة العملات والإعدادات المالية"""

    # إشارة تغيير العملة الافتراضية
    default_currency_changed = pyqtSignal()

    # إشارة تحديث العملات
    currencies_updated = pyqtSignal()

    def __init__(self):
        super().__init__()
        from src.database.db_manager import DatabaseManager
        db_manager = DatabaseManager()
        self.currency_model = CurrencyModel(db_manager)
        self._updating_currency = False  # علامة لمنع الدورة اللا نهائية
        self.setup_ui()
        self.load_currencies()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # تطبيق الأنماط
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Tahoma, Arial;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #495057;
            }
            QTableWidget {
                border: 1px solid #dee2e6;
                border-radius: 8px;
                background-color: white;
                gridline-color: #dee2e6;
                selection-background-color: #007bff;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
            QLineEdit, QComboBox, QDoubleSpinBox {
                border: 2px solid #dee2e6;
                border-radius: 5px;
                padding: 8px;
                font-size: 11px;
                background-color: white;
            }
            QLineEdit:focus, QComboBox:focus, QDoubleSpinBox:focus {
                border-color: #007bff;
            }
        """)

        # علامات التبويب
        self.tab_widget = QTabWidget()

        # تبويب العملات
        self.currencies_tab = QWidget()
        self.setup_currencies_tab()
        self.tab_widget.addTab(self.currencies_tab, "إدارة العملات")

        # تبويب أسعار الصرف
        self.exchange_rates_tab = QWidget()
        self.setup_exchange_rates_tab()
        self.tab_widget.addTab(self.exchange_rates_tab, "أسعار الصرف")

        # تبويب الإعدادات المالية
        self.financial_settings_tab = QWidget()
        self.setup_financial_settings_tab()
        self.tab_widget.addTab(self.financial_settings_tab, "الإعدادات المالية")

        layout.addWidget(self.tab_widget)

        # ربط جميع الأحداث بعد إنشاء جميع العناصر
        self.setup_all_connections()

    def on_currency_updated(self):
        """استدعاء عند تحديث العملات"""
        self.load_currencies()
        self.currencies_updated.emit()

    def setup_currencies_tab(self):
        """إعداد تبويب العملات"""
        layout = QVBoxLayout(self.currencies_tab)
        layout.setSpacing(15)

        # عنوان القسم
        title_label = QLabel("إدارة العملات")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #212529;
                padding: 15px;
                background-color: #e9ecef;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # شريط الأدوات
        toolbar_frame = QFrame()
        toolbar_layout = QHBoxLayout(toolbar_frame)
        toolbar_layout.setContentsMargins(0, 0, 0, 0)

        # البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("البحث في العملات...")
        self.search_input.textChanged.connect(self.search_currencies)

        # أزرار الإدارة
        self.add_currency_button = QPushButton("إضافة عملة")
        self.add_currency_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)

        self.edit_currency_button = QPushButton("تعديل")
        self.delete_currency_button = QPushButton("حذف")
        self.delete_currency_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)

        self.refresh_currencies_button = QPushButton("تحديث")
        self.refresh_currencies_button.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)

        toolbar_layout.addWidget(QLabel("البحث:"))
        toolbar_layout.addWidget(self.search_input)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.add_currency_button)
        toolbar_layout.addWidget(self.edit_currency_button)
        toolbar_layout.addWidget(self.delete_currency_button)
        toolbar_layout.addWidget(self.refresh_currencies_button)

        layout.addWidget(toolbar_frame)

        # جدول العملات
        self.currencies_table = QTableWidget()
        self.setup_currencies_table()
        layout.addWidget(self.currencies_table)

        # سيتم ربط الأحداث في نهاية setup_ui()

    def setup_currencies_table(self):
        """إعداد جدول العملات"""
        columns = [
            "رمز العملة", "اسم العملة", "الرمز", "سعر الصرف",
            "العملة الأساسية", "الحالة"
        ]
        
        self.currencies_table.setColumnCount(len(columns))
        self.currencies_table.setHorizontalHeaderLabels(columns)
        
        # تنسيق الجدول
        header = self.currencies_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        header.setStyleSheet("""
            QHeaderView::section {
                background-color: #495057;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)
        
        self.currencies_table.setAlternatingRowColors(True)
        self.currencies_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.currencies_table.verticalHeader().setVisible(False)

    def setup_exchange_rates_tab(self):
        """إعداد تبويب أسعار الصرف"""
        layout = QVBoxLayout(self.exchange_rates_tab)
        layout.setSpacing(15)

        # عنوان القسم
        title_label = QLabel("إدارة أسعار الصرف")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #212529;
                padding: 15px;
                background-color: #e9ecef;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # نموذج تحديث أسعار الصرف
        rates_group = QGroupBox("تحديث أسعار الصرف")
        rates_layout = QFormLayout(rates_group)

        self.from_currency_combo = QComboBox()
        self.to_currency_combo = QComboBox()
        self.exchange_rate_spin = QDoubleSpinBox()
        self.exchange_rate_spin.setRange(0.0001, 999999.9999)
        self.exchange_rate_spin.setDecimals(4)
        self.exchange_rate_spin.setValue(1.0000)

        self.update_rate_button = QPushButton("تحديث السعر")
        self.update_rate_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)

        rates_layout.addRow("من العملة:", self.from_currency_combo)
        rates_layout.addRow("إلى العملة:", self.to_currency_combo)
        rates_layout.addRow("سعر الصرف:", self.exchange_rate_spin)
        rates_layout.addRow("", self.update_rate_button)

        layout.addWidget(rates_group)

        # جدول أسعار الصرف الحالية
        current_rates_group = QGroupBox("أسعار الصرف الحالية")
        current_rates_layout = QVBoxLayout(current_rates_group)

        self.rates_table = QTableWidget()
        self.setup_rates_table()
        current_rates_layout.addWidget(self.rates_table)

        layout.addWidget(current_rates_group)

    def setup_rates_table(self):
        """إعداد جدول أسعار الصرف"""
        columns = ["من العملة", "إلى العملة", "السعر", "آخر تحديث"]
        
        self.rates_table.setColumnCount(len(columns))
        self.rates_table.setHorizontalHeaderLabels(columns)
        
        header = self.rates_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        header.setStyleSheet("""
            QHeaderView::section {
                background-color: #495057;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

    def setup_financial_settings_tab(self):
        """إعداد تبويب الإعدادات المالية"""
        layout = QVBoxLayout(self.financial_settings_tab)
        layout.setSpacing(15)

        # عنوان القسم
        title_label = QLabel("الإعدادات المالية العامة")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #212529;
                padding: 15px;
                background-color: #e9ecef;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # إعدادات العملة الأساسية
        currency_group = QGroupBox("إعدادات العملة")
        currency_layout = QFormLayout(currency_group)

        self.default_currency_combo = QComboBox()
        self.currency_precision_spin = QSpinBox()
        self.currency_precision_spin.setRange(0, 6)
        self.currency_precision_spin.setValue(2)

        currency_layout.addRow("العملة الافتراضية:", self.default_currency_combo)
        currency_layout.addRow("دقة العملة (عدد الخانات العشرية):", self.currency_precision_spin)

        layout.addWidget(currency_group)

        # إعدادات الرسوم
        fees_group = QGroupBox("إعدادات الرسوم")
        fees_layout = QFormLayout(fees_group)

        self.late_fee_percentage = QDoubleSpinBox()
        self.late_fee_percentage.setRange(0, 100)
        self.late_fee_percentage.setSuffix("%")
        self.late_fee_percentage.setValue(5.0)

        self.discount_enabled = QCheckBox("تفعيل نظام الخصومات")
        self.max_discount_percentage = QDoubleSpinBox()
        self.max_discount_percentage.setRange(0, 100)
        self.max_discount_percentage.setSuffix("%")
        self.max_discount_percentage.setValue(20.0)

        fees_layout.addRow("رسوم التأخير:", self.late_fee_percentage)
        fees_layout.addRow("", self.discount_enabled)
        fees_layout.addRow("أقصى نسبة خصم:", self.max_discount_percentage)

        layout.addWidget(fees_group)

        # زر الحفظ
        save_settings_button = QPushButton("حفظ الإعدادات")
        save_settings_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                font-size: 14px;
                padding: 12px 30px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        layout.addWidget(save_settings_button)

        layout.addStretch()

    def setup_all_connections(self):
        """ربط جميع الأحداث"""
        # أحداث العملات
        self.add_currency_button.clicked.connect(self.add_currency)
        self.edit_currency_button.clicked.connect(self.edit_currency)
        self.delete_currency_button.clicked.connect(self.delete_currency)
        self.refresh_currencies_button.clicked.connect(self.load_currencies)

        # ربط تغيير العملة الافتراضية (إذا كان موجوداً)
        if hasattr(self, 'default_currency_combo'):
            self.default_currency_combo.currentIndexChanged.connect(self.set_default_currency)

    def setup_currencies_connections(self):
        """ربط أحداث العملات (deprecated - استخدم setup_all_connections)"""
        # هذه الدالة لم تعد مستخدمة
        pass

    def load_currencies(self):
        """تحميل قائمة العملات"""
        try:
            currencies = self.currency_model.get_all_currencies()
            self.populate_currencies_table(currencies)
            self.load_currency_combos()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل العملات: {str(e)}")

    def populate_currencies_table(self, currencies):
        """ملء جدول العملات بالبيانات"""
        self.currencies_table.setRowCount(len(currencies))

        for row, currency in enumerate(currencies):
            try:
                # رمز العملة
                self.currencies_table.setItem(row, 0, QTableWidgetItem(str(currency.get('currency_code', ''))))

                # اسم العملة
                self.currencies_table.setItem(row, 1, QTableWidgetItem(str(currency.get('currency_name', ''))))

                # الرمز
                self.currencies_table.setItem(row, 2, QTableWidgetItem(str(currency.get('symbol', ''))))

                # سعر الصرف
                exchange_rate = currency.get('exchange_rate', 1.0)
                self.currencies_table.setItem(row, 3, QTableWidgetItem(f"{exchange_rate:.4f}"))

                # العملة الأساسية
                is_base = currency.get('is_base_currency', False)
                base_text = "نعم" if is_base else "لا"
                self.currencies_table.setItem(row, 4, QTableWidgetItem(base_text))

                # الحالة
                is_active = currency.get('is_active', True)
                status_text = "نشط" if is_active else "غير نشط"
                self.currencies_table.setItem(row, 5, QTableWidgetItem(status_text))

                # حفظ معرف العملة في البيانات المخفية
                self.currencies_table.item(row, 0).setData(Qt.UserRole, currency.get('currency_id'))

            except Exception as e:
                print(f"خطأ في إضافة العملة رقم {row}: {e}")

    def load_currency_combos(self):
        """تحميل العملات في القوائم المنسدلة"""
        try:
            currencies = self.currency_model.get_active_currencies()

            # منع الإشارات أثناء التحديث لتجنب الدورة اللا نهائية
            if hasattr(self, 'default_currency_combo'):
                self.default_currency_combo.blockSignals(True)

            # تحديث قوائم أسعار الصرف (إذا كانت موجودة)
            if hasattr(self, 'from_currency_combo'):
                self.from_currency_combo.clear()
            if hasattr(self, 'to_currency_combo'):
                self.to_currency_combo.clear()
            if hasattr(self, 'default_currency_combo'):
                self.default_currency_combo.clear()

            default_currency_index = -1

            for i, currency in enumerate(currencies):
                currency_text = f"{currency['currency_code']} - {currency['currency_name']}"
                currency_id = currency['currency_id']

                if hasattr(self, 'from_currency_combo'):
                    self.from_currency_combo.addItem(currency_text, currency_id)
                if hasattr(self, 'to_currency_combo'):
                    self.to_currency_combo.addItem(currency_text, currency_id)
                if hasattr(self, 'default_currency_combo'):
                    self.default_currency_combo.addItem(currency_text, currency_id)

                # تحديد العملة الافتراضية
                if currency.get('is_base_currency', False):
                    default_currency_index = i

            # تعيين العملة الافتراضية في القائمة المنسدلة
            if default_currency_index >= 0 and hasattr(self, 'default_currency_combo'):
                self.default_currency_combo.setCurrentIndex(default_currency_index)

            # إعادة تفعيل الإشارات
            if hasattr(self, 'default_currency_combo'):
                self.default_currency_combo.blockSignals(False)

        except Exception as e:
            print(f"خطأ في تحميل قوائم العملات: {e}")

    def search_currencies(self):
        """البحث في العملات"""
        search_text = self.search_input.text().strip()

        if not search_text:
            self.load_currencies()
            return

        try:
            currencies = self.currency_model.search_currencies(search_text)
            self.populate_currencies_table(currencies)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في البحث: {str(e)}")

    def add_currency(self):
        """إضافة عملة جديدة"""
        dialog = CurrencyDialog(parent=self)
        dialog.currency_saved.connect(self.on_currency_updated)
        dialog.exec_()

    def edit_currency(self):
        """تعديل عملة"""
        current_row = self.currencies_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عملة للتعديل")
            return

        try:
            currency_id = self.currencies_table.item(current_row, 0).data(Qt.UserRole)
            currency_data = self.currency_model.get_by_id(currency_id)

            if currency_data:
                dialog = CurrencyDialog(currency_data=currency_data, parent=self)
                dialog.currency_saved.connect(self.on_currency_updated)
                dialog.exec_()
            else:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على بيانات العملة")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل بيانات العملة: {str(e)}")

    def delete_currency(self):
        """حذف عملة"""
        current_row = self.currencies_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عملة للحذف")
            return

        try:
            currency_code = self.currencies_table.item(current_row, 0).text()
            currency_id = self.currencies_table.item(current_row, 0).data(Qt.UserRole)

            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                f"هل أنت متأكد من حذف العملة '{currency_code}'؟\n"
                "هذا الإجراء لا يمكن التراجع عنه.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                success = self.currency_model.delete_currency(currency_id)
                if success:
                    QMessageBox.information(self, "نجح", "تم حذف العملة بنجاح")
                    self.on_currency_updated()
                else:
                    QMessageBox.critical(self, "خطأ", "فشل في حذف العملة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حذف العملة: {str(e)}")

    def set_default_currency(self):
        """تعيين العملة الافتراضية"""
        try:
            # منع الدورة اللا نهائية
            if self._updating_currency:
                return

            # التحقق من وجود العنصر
            if not hasattr(self, 'default_currency_combo'):
                return

            if self.default_currency_combo.currentIndex() < 0:
                return

            new_currency_id = self.default_currency_combo.currentData()
            if not new_currency_id:
                return

            # تعيين علامة التحديث
            self._updating_currency = True

            # إزالة العملة الأساسية الحالية
            self.currency_model.db_manager.execute_query(
                "UPDATE currencies SET is_base_currency = 0"
            )

            # تعيين العملة الجديدة كأساسية
            self.currency_model.db_manager.execute_query(
                "UPDATE currencies SET is_base_currency = 1 WHERE currency_id = ?",
                (new_currency_id,)
            )

            # إعادة تحميل البيانات
            self.load_currencies()
            self.load_currency_combos()

            # إرسال إشارة تغيير العملة الافتراضية
            self.default_currency_changed.emit()

            QMessageBox.information(self, "نجح", "تم تغيير العملة الافتراضية بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تعيين العملة الافتراضية: {str(e)}")
        finally:
            # إزالة علامة التحديث
            self._updating_currency = False
