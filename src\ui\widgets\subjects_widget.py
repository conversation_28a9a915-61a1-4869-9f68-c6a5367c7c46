#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ويدجت إدارة المواد الدراسية والجداول
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                             QMessageBox, QHeaderView, QFrame, QTabWidget,
                             QComboBox, QSpinBox, QTreeWidget, QTreeWidgetItem,
                             QSplitter)
from PyQt5.QtCore import Qt

from src.models.subject import Subject
from src.models.class_model import ClassModel
from src.models.teacher import Teacher
from src.ui.dialogs.subject_dialog import SubjectDialog
from src.ui.dialogs.schedule_dialog import ScheduleDialog


class SubjectsWidget(QWidget):
    """ويدجت إدارة المواد الدراسية والجداول"""

    def __init__(self):
        super().__init__()
        self.subject_model = Subject()
        self.class_model = ClassModel()
        self.teacher_model = Teacher()
        self.setup_ui()
        self.load_subjects()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # علامات التبويب
        self.tab_widget = QTabWidget()

        # تبويب المواد الدراسية
        self.subjects_tab = QWidget()
        self.setup_subjects_tab()
        self.tab_widget.addTab(self.subjects_tab, "المواد الدراسية")

        # تبويب ربط المواد بالصفوف
        self.assignments_tab = QWidget()
        self.setup_assignments_tab()
        self.tab_widget.addTab(self.assignments_tab, "ربط المواد بالصفوف")

        # تبويب الجداول الزمنية
        self.schedules_tab = QWidget()
        self.setup_schedules_tab()
        self.tab_widget.addTab(self.schedules_tab, "الجداول الزمنية")

        layout.addWidget(self.tab_widget)

    def setup_subjects_tab(self):
        """إعداد تبويب المواد الدراسية"""
        layout = QVBoxLayout(self.subjects_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)

        # شريط البحث والأزرار
        top_frame = QFrame()
        top_layout = QHBoxLayout(top_frame)

        # حقل البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("البحث عن مادة...")
        self.search_input.setFixedHeight(35)
        self.search_input.textChanged.connect(self.search_subjects)

        # أزرار الإجراءات
        self.add_subject_button = QPushButton("إضافة مادة")
        self.edit_subject_button = QPushButton("تعديل")
        self.delete_subject_button = QPushButton("حذف")
        self.refresh_subjects_button = QPushButton("تحديث")

        # تطبيق الأنماط على الأزرار
        button_style = """
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """

        for button in [self.add_subject_button, self.edit_subject_button,
                      self.delete_subject_button, self.refresh_subjects_button]:
            button.setStyleSheet(button_style)
            button.setFixedHeight(35)

        # تخصيص لون زر الحذف
        self.delete_subject_button.setStyleSheet(
            button_style.replace("#9b59b6", "#e74c3c").replace("#8e44ad", "#c0392b")
        )

        top_layout.addWidget(QLabel("البحث:"))
        top_layout.addWidget(self.search_input)
        top_layout.addStretch()
        top_layout.addWidget(self.add_subject_button)
        top_layout.addWidget(self.edit_subject_button)
        top_layout.addWidget(self.delete_subject_button)
        top_layout.addWidget(self.refresh_subjects_button)

        layout.addWidget(top_frame)

        # جدول المواد
        self.subjects_table = QTableWidget()
        self.setup_subjects_table()
        layout.addWidget(self.subjects_table)

        # ربط الأحداث
        self.setup_subjects_connections()

    def setup_subjects_table(self):
        """إعداد جدول المواد الدراسية"""
        columns = [
            "رمز المادة", "اسم المادة", "الساعات المعتمدة",
            "الوصف", "الحالة"
        ]

        self.subjects_table.setColumnCount(len(columns))
        self.subjects_table.setHorizontalHeaderLabels(columns)

        # إعداد خصائص الجدول
        self.subjects_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.subjects_table.setAlternatingRowColors(True)
        self.subjects_table.setSortingEnabled(True)

        # تخصيص عرض الأعمدة
        header = self.subjects_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)

        # تطبيق الأنماط
        self.subjects_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
            }
            QTableWidget::item:selected {
                background-color: #9b59b6;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

    def setup_subjects_connections(self):
        """ربط أحداث المواد"""
        self.add_subject_button.clicked.connect(self.add_subject)
        self.edit_subject_button.clicked.connect(self.edit_subject)
        self.delete_subject_button.clicked.connect(self.delete_subject)
        self.refresh_subjects_button.clicked.connect(self.load_subjects)

    def load_subjects(self):
        """تحميل قائمة المواد الدراسية"""
        try:
            subjects = self.subject_model.get_all_subjects()
            self.populate_subjects_table(subjects)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل المواد: {str(e)}")

    def populate_subjects_table(self, subjects):
        """ملء جدول المواد بالبيانات"""
        self.subjects_table.setRowCount(len(subjects))

        for row, subject in enumerate(subjects):
            try:
                # رمز المادة
                self.subjects_table.setItem(row, 0, QTableWidgetItem(str(subject['subject_code'] or '')))

                # اسم المادة
                self.subjects_table.setItem(row, 1, QTableWidgetItem(str(subject['subject_name'] or '')))

                # الساعات المعتمدة
                credit_hours = subject['credit_hours'] if subject['credit_hours'] else 1
                self.subjects_table.setItem(row, 2, QTableWidgetItem(str(credit_hours)))

                # الوصف
                description = subject['description'] if subject['description'] else ""
                self.subjects_table.setItem(row, 3, QTableWidgetItem(str(description)))

                # الحالة
                is_active = subject['is_active'] if 'is_active' in subject else True
                status_text = "نشط" if is_active else "غير نشط"
                self.subjects_table.setItem(row, 4, QTableWidgetItem(status_text))

                # حفظ معرف المادة في البيانات المخفية
                self.subjects_table.item(row, 0).setData(Qt.UserRole, subject['subject_id'])

            except Exception as e:
                print(f"خطأ في إضافة المادة رقم {row}: {e}")

    def search_subjects(self):
        """البحث في المواد الدراسية"""
        search_text = self.search_input.text().strip()

        if not search_text:
            self.load_subjects()
            return

        try:
            subjects = self.subject_model.search_subjects(search_text)
            self.populate_subjects_table(subjects)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في البحث: {str(e)}")

    def add_subject(self):
        """إضافة مادة جديدة"""
        dialog = SubjectDialog(parent=self)
        dialog.subject_saved.connect(self.load_subjects)
        dialog.exec_()

    def edit_subject(self):
        """تعديل مادة"""
        current_row = self.subjects_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مادة للتعديل")
            return

        # الحصول على معرف المادة
        subject_id = self.subjects_table.item(current_row, 0).data(Qt.UserRole)

        dialog = SubjectDialog(subject_id=subject_id, parent=self)
        dialog.subject_saved.connect(self.load_subjects)
        dialog.exec_()

    def delete_subject(self):
        """حذف مادة"""
        current_row = self.subjects_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مادة للحذف")
            return

        # الحصول على معرف المادة
        subject_id = self.subjects_table.item(current_row, 0).data(Qt.UserRole)
        subject_name = self.subjects_table.item(current_row, 1).text()

        # تأكيد الحذف
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل أنت متأكد من رغبتك في حذف المادة: {subject_name}؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                self.subject_model.deactivate_subject(subject_id)
                QMessageBox.information(self, "نجح", "تم حذف المادة بنجاح")
                self.load_subjects()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ في حذف المادة: {str(e)}")

    def setup_assignments_tab(self):
        """إعداد تبويب ربط المواد بالصفوف"""
        layout = QVBoxLayout(self.assignments_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)

        # شريط التحكم
        control_frame = QFrame()
        control_layout = QHBoxLayout(control_frame)

        # اختيار السنة الدراسية
        control_layout.addWidget(QLabel("السنة الدراسية:"))
        self.academic_year_combo = QComboBox()
        self.academic_year_combo.addItems(["2024-2025", "2025-2026", "2026-2027"])
        control_layout.addWidget(self.academic_year_combo)

        # اختيار الفصل الدراسي
        control_layout.addWidget(QLabel("الفصل الدراسي:"))
        self.semester_combo = QComboBox()
        self.semester_combo.addItems(["الفصل الأول", "الفصل الثاني"])
        control_layout.addWidget(self.semester_combo)

        # أزرار التحكم
        self.load_assignments_button = QPushButton("تحميل الربط")
        self.add_assignment_button = QPushButton("ربط مادة بصف")
        self.remove_assignment_button = QPushButton("إلغاء الربط")

        assignment_button_style = """
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """

        for button in [self.load_assignments_button, self.add_assignment_button, self.remove_assignment_button]:
            button.setStyleSheet(assignment_button_style)
            button.setFixedHeight(35)

        control_layout.addStretch()
        control_layout.addWidget(self.load_assignments_button)
        control_layout.addWidget(self.add_assignment_button)
        control_layout.addWidget(self.remove_assignment_button)

        layout.addWidget(control_frame)

        # جدول ربط المواد بالصفوف
        self.assignments_table = QTableWidget()
        self.setup_assignments_table()
        layout.addWidget(self.assignments_table)

        # ربط الأحداث
        self.setup_assignments_connections()

    def setup_assignments_table(self):
        """إعداد جدول ربط المواد بالصفوف"""
        columns = [
            "الصف", "المادة", "المعلم", "السنة الدراسية", "الفصل الدراسي"
        ]

        self.assignments_table.setColumnCount(len(columns))
        self.assignments_table.setHorizontalHeaderLabels(columns)

        # إعداد خصائص الجدول
        self.assignments_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.assignments_table.setAlternatingRowColors(True)
        self.assignments_table.setSortingEnabled(True)

        # تخصيص عرض الأعمدة
        header = self.assignments_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)

        # تطبيق الأنماط
        self.assignments_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #2c3e50;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

    def setup_assignments_connections(self):
        """ربط أحداث ربط المواد"""
        self.load_assignments_button.clicked.connect(self.load_assignments)
        self.add_assignment_button.clicked.connect(self.add_assignment)
        self.remove_assignment_button.clicked.connect(self.remove_assignment)
        self.academic_year_combo.currentTextChanged.connect(self.load_assignments)
        self.semester_combo.currentTextChanged.connect(self.load_assignments)

    def load_assignments(self):
        """تحميل ربط المواد بالصفوف"""
        try:
            academic_year = self.academic_year_combo.currentText()
            semester = self.semester_combo.currentText()

            query = """
            SELECT cs.*, c.class_name, s.subject_name, t.first_name, t.last_name
            FROM class_subjects cs
            JOIN classes c ON cs.class_id = c.class_id
            JOIN subjects s ON cs.subject_id = s.subject_id
            JOIN teachers t ON cs.teacher_id = t.teacher_id
            WHERE cs.academic_year = ? AND cs.semester = ?
            ORDER BY c.class_name, s.subject_name
            """
            assignments = self.subject_model.db_manager.fetch_all(query, (academic_year, semester))
            self.populate_assignments_table(assignments)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل الربط: {str(e)}")

    def populate_assignments_table(self, assignments):
        """ملء جدول ربط المواد بالبيانات"""
        self.assignments_table.setRowCount(len(assignments))

        for row, assignment in enumerate(assignments):
            try:
                # الصف
                self.assignments_table.setItem(row, 0, QTableWidgetItem(str(assignment['class_name'])))

                # المادة
                self.assignments_table.setItem(row, 1, QTableWidgetItem(str(assignment['subject_name'])))

                # المعلم
                teacher_name = f"{assignment['first_name']} {assignment['last_name']}"
                self.assignments_table.setItem(row, 2, QTableWidgetItem(teacher_name))

                # السنة الدراسية
                self.assignments_table.setItem(row, 3, QTableWidgetItem(str(assignment['academic_year'])))

                # الفصل الدراسي
                self.assignments_table.setItem(row, 4, QTableWidgetItem(str(assignment['semester'])))

                # حفظ معرف الربط في البيانات المخفية
                self.assignments_table.item(row, 0).setData(Qt.UserRole, assignment['id'])

            except Exception as e:
                print(f"خطأ في إضافة الربط رقم {row}: {e}")

    def add_assignment(self):
        """إضافة ربط مادة بصف"""
        QMessageBox.information(self, "قريباً", "سيتم إضافة نافذة ربط المواد بالصفوف قريباً")

    def remove_assignment(self):
        """إلغاء ربط مادة بصف"""
        current_row = self.assignments_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار ربط لإلغائه")
            return

        # الحصول على معرف الربط
        assignment_id = self.assignments_table.item(current_row, 0).data(Qt.UserRole)
        class_name = self.assignments_table.item(current_row, 0).text()
        subject_name = self.assignments_table.item(current_row, 1).text()

        # تأكيد الحذف
        reply = QMessageBox.question(
            self,
            "تأكيد إلغاء الربط",
            f"هل أنت متأكد من رغبتك في إلغاء ربط المادة '{subject_name}' بالصف '{class_name}'؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                query = "DELETE FROM class_subjects WHERE id = ?"
                self.subject_model.db_manager.execute_query(query, (assignment_id,))
                QMessageBox.information(self, "نجح", "تم إلغاء الربط بنجاح")
                self.load_assignments()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ في إلغاء الربط: {str(e)}")

    def setup_schedules_tab(self):
        """إعداد تبويب الجداول الزمنية"""
        layout = QVBoxLayout(self.schedules_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)

        # شريط التحكم في الجداول
        schedule_control_frame = QFrame()
        schedule_control_layout = QHBoxLayout(schedule_control_frame)

        # اختيار الصف
        schedule_control_layout.addWidget(QLabel("الصف:"))
        self.schedule_class_combo = QComboBox()
        self.load_classes_for_schedule()
        schedule_control_layout.addWidget(self.schedule_class_combo)

        # أزرار الجداول
        self.view_schedule_button = QPushButton("عرض الجدول")
        self.edit_schedule_button = QPushButton("تعديل الجدول")
        self.print_schedule_button = QPushButton("طباعة الجدول")

        schedule_button_style = """
            QPushButton {
                background-color: #e67e22;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
        """

        for button in [self.view_schedule_button, self.edit_schedule_button, self.print_schedule_button]:
            button.setStyleSheet(schedule_button_style)
            button.setFixedHeight(35)

        schedule_control_layout.addStretch()
        schedule_control_layout.addWidget(self.view_schedule_button)
        schedule_control_layout.addWidget(self.edit_schedule_button)
        schedule_control_layout.addWidget(self.print_schedule_button)

        layout.addWidget(schedule_control_frame)

        # جدول الجدول الزمني
        self.schedule_table = QTableWidget()
        self.setup_schedule_table()
        layout.addWidget(self.schedule_table)

        # ربط أحداث الجداول
        self.setup_schedules_connections()

    def setup_schedule_table(self):
        """إعداد جدول الجدول الزمني"""
        # أيام الأسبوع والحصص
        days = ["الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس"]
        periods = ["الحصة الأولى", "الحصة الثانية", "الحصة الثالثة",
                  "الحصة الرابعة", "الحصة الخامسة", "الحصة السادسة"]

        self.schedule_table.setRowCount(len(periods))
        self.schedule_table.setColumnCount(len(days))

        # تعيين عناوين الصفوف والأعمدة
        self.schedule_table.setVerticalHeaderLabels(periods)
        self.schedule_table.setHorizontalHeaderLabels(days)

        # إعداد خصائص الجدول
        self.schedule_table.setSelectionBehavior(QTableWidget.SelectItems)

        # تخصيص عرض الأعمدة والصفوف
        header = self.schedule_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)

        v_header = self.schedule_table.verticalHeader()
        v_header.setSectionResizeMode(QHeaderView.Stretch)

        # تطبيق الأنماط
        self.schedule_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #34495e;
                background-color: white;
                font-size: 12px;
            }
            QTableWidget::item {
                padding: 10px;
                text-align: center;
                border: 1px solid #bdc3c7;
            }
            QHeaderView::section {
                background-color: #e67e22;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

        # ملء الجدول بخلايا فارغة
        for row in range(len(periods)):
            for col in range(len(days)):
                item = QTableWidgetItem("")
                item.setTextAlignment(Qt.AlignCenter)
                self.schedule_table.setItem(row, col, item)

    def setup_schedules_connections(self):
        """ربط أحداث الجداول الزمنية"""
        self.view_schedule_button.clicked.connect(self.view_schedule)
        self.edit_schedule_button.clicked.connect(self.edit_schedule)
        self.print_schedule_button.clicked.connect(self.print_schedule)
        self.schedule_class_combo.currentTextChanged.connect(self.view_schedule)

    def load_classes_for_schedule(self):
        """تحميل الصفوف لاختيار الجدول"""
        try:
            classes = self.class_model.get_active_classes()
            self.schedule_class_combo.clear()

            for class_info in classes:
                self.schedule_class_combo.addItem(class_info['class_name'], class_info['class_id'])

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل الصفوف: {str(e)}")

    def view_schedule(self):
        """عرض الجدول الزمني للصف المحدد"""
        if self.schedule_class_combo.currentIndex() < 0:
            return

        class_id = self.schedule_class_combo.currentData()
        class_name = self.schedule_class_combo.currentText()

        # مسح الجدول الحالي
        for row in range(self.schedule_table.rowCount()):
            for col in range(self.schedule_table.columnCount()):
                self.schedule_table.item(row, col).setText("")

        # هنا يمكن إضافة منطق تحميل الجدول الفعلي من قاعدة البيانات
        # للآن سنعرض رسالة توضيحية
        QMessageBox.information(
            self,
            "الجدول الزمني",
            f"عرض الجدول الزمني للصف: {class_name}\n"
            "هذه الميزة ستكون متاحة قريباً مع إضافة جدول الجداول الزمنية"
        )

    def edit_schedule(self):
        """تعديل الجدول الزمني"""
        if self.schedule_class_combo.currentIndex() < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار صف أولاً")
            return

        class_id = self.schedule_class_combo.currentData()
        dialog = ScheduleDialog(class_id=class_id, parent=self)
        dialog.exec_()

    def print_schedule(self):
        """طباعة الجدول الزمني"""
        if self.schedule_class_combo.currentIndex() < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار صف أولاً")
            return

        class_name = self.schedule_class_combo.currentText()
        QMessageBox.information(
            self,
            "طباعة الجدول",
            f"سيتم طباعة الجدول الزمني للصف: {class_name}\n"
            "هذه الميزة ستكون متاحة في نظام التقارير"
        )
