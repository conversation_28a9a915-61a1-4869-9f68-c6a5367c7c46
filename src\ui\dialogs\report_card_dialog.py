#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة عرض كشف الدرجات
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QTableWidgetItem, QPushButton, QLabel, QFrame, 
                             QMessageBox, QHeaderView)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from src.models.student import Student


class ReportCardDialog(QDialog):
    """نافذة عرض كشف الدرجات"""
    
    def __init__(self, student_id=None, parent=None):
        super().__init__(parent)
        self.student_id = student_id
        self.student_model = Student()
        
        self.setup_ui()
        self.load_student_info()
        self.load_report_card()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة
        self.setWindowTitle("كشف الدرجات")
        self.setFixedSize(800, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # عنوان النافذة
        title_label = QLabel("كشف الدرجات")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # معلومات الطالب
        self.student_info_frame = QFrame()
        self.student_info_frame.setFrameStyle(QFrame.StyledPanel)
        student_info_layout = QVBoxLayout(self.student_info_frame)
        
        self.student_name_label = QLabel("اسم الطالب: ")
        self.student_number_label = QLabel("رقم الطالب: ")
        self.class_name_label = QLabel("الصف: ")
        self.academic_year_label = QLabel("السنة الدراسية: 2024-2025")
        
        info_style = """
            QLabel {
                font-size: 14px;
                color: #34495e;
                padding: 5px;
                margin: 2px;
            }
        """
        
        for label in [self.student_name_label, self.student_number_label, 
                     self.class_name_label, self.academic_year_label]:
            label.setStyleSheet(info_style)
            student_info_layout.addWidget(label)
        
        main_layout.addWidget(self.student_info_frame)
        
        # جدول الدرجات
        self.grades_table = QTableWidget()
        self.setup_grades_table()
        main_layout.addWidget(self.grades_table)
        
        # ملخص الدرجات
        summary_frame = QFrame()
        summary_frame.setFrameStyle(QFrame.StyledPanel)
        summary_layout = QHBoxLayout(summary_frame)
        
        self.total_average_label = QLabel("المعدل العام: 0.0%")
        self.total_average_label.setAlignment(Qt.AlignCenter)
        self.total_average_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #27ae60;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
            }
        """)
        
        self.overall_grade_label = QLabel("التقدير العام: غير محدد")
        self.overall_grade_label.setAlignment(Qt.AlignCenter)
        self.overall_grade_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #3498db;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
            }
        """)
        
        summary_layout.addWidget(self.total_average_label)
        summary_layout.addWidget(self.overall_grade_label)
        
        main_layout.addWidget(summary_frame)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        self.print_button = QPushButton("طباعة")
        self.print_button.setFixedHeight(40)
        self.print_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        self.export_button = QPushButton("تصدير PDF")
        self.export_button.setFixedHeight(40)
        self.export_button.setStyleSheet("""
            QPushButton {
                background-color: #e67e22;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
        """)
        
        self.close_button = QPushButton("إغلاق")
        self.close_button.setFixedHeight(40)
        self.close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.print_button)
        buttons_layout.addWidget(self.export_button)
        buttons_layout.addWidget(self.close_button)
        
        main_layout.addLayout(buttons_layout)
        
        # ربط الأحداث
        self.setup_connections()
        
    def setup_grades_table(self):
        """إعداد جدول الدرجات"""
        columns = [
            "المادة", "اختبار شهري", "اختبار نصف الفصل", "اختبار نهائي", 
            "الواجبات", "المشاركة", "المجموع", "النسبة", "التقدير"
        ]
        
        self.grades_table.setColumnCount(len(columns))
        self.grades_table.setHorizontalHeaderLabels(columns)
        
        # إعداد خصائص الجدول
        self.grades_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.grades_table.setAlternatingRowColors(True)
        self.grades_table.setEditTriggers(QTableWidget.NoEditTriggers)
        
        # تخصيص عرض الأعمدة
        header = self.grades_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        # تطبيق الأنماط
        self.grades_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
                font-size: 12px;
            }
            QTableWidget::item {
                padding: 8px;
                text-align: center;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
                font-size: 11px;
            }
        """)
        
    def setup_connections(self):
        """ربط الأحداث"""
        self.print_button.clicked.connect(self.print_report_card)
        self.export_button.clicked.connect(self.export_pdf)
        self.close_button.clicked.connect(self.accept)
        
    def load_student_info(self):
        """تحميل معلومات الطالب"""
        if not self.student_id:
            return
            
        try:
            # استعلام معلومات الطالب
            query = """
            SELECT s.*, c.class_name
            FROM students s
            LEFT JOIN classes c ON s.class_id = c.class_id
            WHERE s.student_id = ?
            """
            student = self.student_model.db_manager.fetch_one(query, (self.student_id,))
            
            if student:
                name = f"{student['first_name']} {student['last_name']}"
                self.student_name_label.setText(f"اسم الطالب: {name}")
                self.student_number_label.setText(f"رقم الطالب: {student['student_number']}")
                
                class_name = student['class_name'] if student['class_name'] else "غير محدد"
                self.class_name_label.setText(f"الصف: {class_name}")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل معلومات الطالب: {str(e)}")
            
    def load_report_card(self):
        """تحميل كشف الدرجات"""
        if not self.student_id:
            return
            
        try:
            # استعلام درجات الطالب
            query = """
            SELECT r.*, s.subject_name
            FROM results r
            JOIN subjects s ON r.subject_id = s.subject_id
            WHERE r.student_id = ?
            ORDER BY s.subject_name, r.exam_type
            """
            results = self.student_model.db_manager.fetch_all(query, (self.student_id,))
            
            self.populate_report_card(results)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل كشف الدرجات: {str(e)}")
            
    def populate_report_card(self, results):
        """ملء كشف الدرجات"""
        # تجميع النتائج حسب المادة
        subjects_data = {}
        
        for result in results:
            subject_name = result['subject_name']
            assessment_type = result['exam_type']
            percentage = result['percentage']

            if subject_name not in subjects_data:
                subjects_data[subject_name] = {}

            subjects_data[subject_name][assessment_type] = percentage
            
        # ملء الجدول
        self.grades_table.setRowCount(len(subjects_data))
        
        total_percentage = 0
        subjects_count = 0
        
        row = 0
        for subject_name, assessments in subjects_data.items():
            # اسم المادة
            self.grades_table.setItem(row, 0, QTableWidgetItem(subject_name))
            
            # الدرجات حسب نوع التقييم
            monthly = assessments.get('اختبار شهري', 0)
            midterm = assessments.get('اختبار نصف الفصل', 0)
            final = assessments.get('اختبار نهائي', 0)
            homework = assessments.get('واجب', 0)
            participation = assessments.get('مشاركة', 0)
            
            self.grades_table.setItem(row, 1, QTableWidgetItem(f"{monthly:.1f}%"))
            self.grades_table.setItem(row, 2, QTableWidgetItem(f"{midterm:.1f}%"))
            self.grades_table.setItem(row, 3, QTableWidgetItem(f"{final:.1f}%"))
            self.grades_table.setItem(row, 4, QTableWidgetItem(f"{homework:.1f}%"))
            self.grades_table.setItem(row, 5, QTableWidgetItem(f"{participation:.1f}%"))
            
            # حساب المجموع والنسبة والتقدير
            subject_total = (monthly + midterm + final + homework + participation) / 5
            grade_letter = self.get_grade_letter(subject_total)
            
            self.grades_table.setItem(row, 6, QTableWidgetItem(f"{subject_total:.1f}"))
            self.grades_table.setItem(row, 7, QTableWidgetItem(f"{subject_total:.1f}%"))
            self.grades_table.setItem(row, 8, QTableWidgetItem(grade_letter))
            
            total_percentage += subject_total
            subjects_count += 1
            row += 1
            
        # حساب المعدل العام
        if subjects_count > 0:
            overall_average = total_percentage / subjects_count
            overall_grade = self.get_grade_letter(overall_average)
            
            self.total_average_label.setText(f"المعدل العام: {overall_average:.1f}%")
            self.overall_grade_label.setText(f"التقدير العام: {overall_grade}")
            
    def get_grade_letter(self, percentage):
        """تحديد التقدير بناءً على النسبة المئوية"""
        if percentage >= 90:
            return "ممتاز"
        elif percentage >= 80:
            return "جيد جداً"
        elif percentage >= 70:
            return "جيد"
        elif percentage >= 60:
            return "مقبول"
        else:
            return "راسب"
            
    def print_report_card(self):
        """طباعة كشف الدرجات"""
        QMessageBox.information(
            self, 
            "طباعة كشف الدرجات", 
            "سيتم طباعة كشف الدرجات\n"
            "هذه الميزة ستكون متاحة في نظام التقارير"
        )
        
    def export_pdf(self):
        """تصدير كشف الدرجات إلى PDF"""
        QMessageBox.information(
            self, 
            "تصدير PDF", 
            "سيتم تصدير كشف الدرجات إلى ملف PDF\n"
            "هذه الميزة ستكون متاحة قريباً"
        )
