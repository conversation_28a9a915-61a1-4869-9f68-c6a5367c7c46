#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ويدجت إعدادات النظام
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QLineEdit, QPushButton, QLabel, QFrame, QMessageBox,
                             QComboBox, QCheckBox, QSpinBox, QTabWidget,
                             QGroupBox, QTextEdit, QFileDialog, QScrollArea,
                             QFontComboBox, QApplication)
from PyQt5.QtCore import Qt, QSettings, pyqtSignal
from PyQt5.QtGui import QFont

import json
import os


class SettingsWidget(QWidget):
    """ويدجت إعدادات النظام"""

    # إشارة تحديث الخط
    font_changed = pyqtSignal(QFont)

    def __init__(self):
        super().__init__()
        self.settings = QSettings("SchoolManagement", "Settings")
        self.setup_ui()
        self.load_settings()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # عنوان الإعدادات
        title_label = QLabel("إعدادات النظام")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title_label)

        # علامات التبويب
        self.tab_widget = QTabWidget()

        # تبويب الإعدادات العامة
        self.general_tab = QWidget()
        self.setup_general_tab()
        self.tab_widget.addTab(self.general_tab, "الإعدادات العامة")

        # تبويب إعدادات المدرسة
        self.school_tab = QWidget()
        self.setup_school_tab()
        self.tab_widget.addTab(self.school_tab, "معلومات المدرسة")

        # تبويب إعدادات النظام
        self.system_tab = QWidget()
        self.setup_system_tab()
        self.tab_widget.addTab(self.system_tab, "إعدادات النظام")

        # تبويب النسخ الاحتياطي
        self.backup_tab = QWidget()
        self.setup_backup_tab()
        self.tab_widget.addTab(self.backup_tab, "النسخ الاحتياطي")

        layout.addWidget(self.tab_widget)

        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()

        self.save_button = QPushButton("حفظ الإعدادات")
        self.save_button.setFixedHeight(40)
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        self.reset_button = QPushButton("إعادة تعيين")
        self.reset_button.setFixedHeight(40)
        self.reset_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)

        buttons_layout.addStretch()
        buttons_layout.addWidget(self.reset_button)
        buttons_layout.addWidget(self.save_button)

        layout.addLayout(buttons_layout)

        # ربط الأحداث
        self.setup_connections()

    def setup_general_tab(self):
        """إعداد تبويب الإعدادات العامة"""
        layout = QVBoxLayout(self.general_tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # إعدادات اللغة والمظهر
        appearance_group = QGroupBox("المظهر واللغة")
        appearance_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                background-color: white;
                border-radius: 3px;
            }
        """)
        appearance_layout = QFormLayout(appearance_group)
        appearance_layout.setSpacing(15)

        # اللغة
        self.language_combo = QComboBox()
        self.language_combo.addItems(["العربية", "English"])
        self.language_combo.setStyleSheet(self.get_input_style())
        appearance_layout.addRow("اللغة:", self.language_combo)

        # المظهر
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["فاتح", "داكن", "تلقائي"])
        self.theme_combo.setStyleSheet(self.get_input_style())
        appearance_layout.addRow("المظهر:", self.theme_combo)

        # نوع الخط
        self.font_family_combo = QFontComboBox()
        self.font_family_combo.setCurrentFont(QFont("Arial"))
        self.font_family_combo.setStyleSheet(self.get_input_style())
        appearance_layout.addRow("نوع الخط:", self.font_family_combo)

        # حجم الخط
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 32)
        self.font_size_spin.setValue(12)
        self.font_size_spin.setSuffix(" نقطة")
        self.font_size_spin.setStyleSheet(self.get_input_style())
        appearance_layout.addRow("حجم الخط:", self.font_size_spin)

        # معاينة الخط
        self.font_preview_label = QLabel("معاينة الخط - Sample Text - نص تجريبي")
        self.font_preview_label.setStyleSheet("""
            QLabel {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                padding: 10px;
                background-color: white;
                min-height: 40px;
            }
        """)
        appearance_layout.addRow("معاينة:", self.font_preview_label)

        # زر تطبيق الخط فوراً
        self.apply_font_button = QPushButton("تطبيق الخط الآن")
        self.apply_font_button.setFixedHeight(40)
        self.apply_font_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.apply_font_button.clicked.connect(self.apply_font_to_application)
        appearance_layout.addRow("", self.apply_font_button)

        layout.addWidget(appearance_group)

        # إعدادات التاريخ والوقت
        datetime_group = QGroupBox("التاريخ والوقت")
        datetime_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                background-color: white;
                border-radius: 3px;
            }
        """)
        datetime_layout = QFormLayout(datetime_group)
        datetime_layout.setSpacing(15)

        # تنسيق التاريخ
        self.date_format_combo = QComboBox()
        self.date_format_combo.addItems(["dd/MM/yyyy", "MM/dd/yyyy", "yyyy-MM-dd"])
        self.date_format_combo.setStyleSheet(self.get_input_style())
        datetime_layout.addRow("تنسيق التاريخ:", self.date_format_combo)

        # تنسيق الوقت
        self.time_format_combo = QComboBox()
        self.time_format_combo.addItems(["24 ساعة", "12 ساعة"])
        self.time_format_combo.setStyleSheet(self.get_input_style())
        datetime_layout.addRow("تنسيق الوقت:", self.time_format_combo)

        layout.addWidget(datetime_group)

        # إعدادات الإشعارات
        notifications_group = QGroupBox("الإشعارات")
        notifications_group.setStyleSheet(self.get_group_style())
        notifications_layout = QFormLayout(notifications_group)

        self.enable_notifications_check = QCheckBox("تفعيل الإشعارات")
        self.enable_notifications_check.setChecked(True)
        notifications_layout.addRow("", self.enable_notifications_check)

        self.sound_notifications_check = QCheckBox("إشعارات صوتية")
        notifications_layout.addRow("", self.sound_notifications_check)

        self.desktop_notifications_check = QCheckBox("إشعارات سطح المكتب")
        notifications_layout.addRow("", self.desktop_notifications_check)

        layout.addWidget(notifications_group)
        layout.addStretch()

    def setup_school_tab(self):
        """إعداد تبويب معلومات المدرسة"""
        layout = QVBoxLayout(self.school_tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # معلومات أساسية
        basic_info_group = QGroupBox("المعلومات الأساسية")
        basic_info_group.setStyleSheet(self.get_group_style())
        basic_info_layout = QFormLayout(basic_info_group)

        self.school_name_input = QLineEdit()
        self.school_name_input.setPlaceholderText("أدخل اسم المدرسة")
        basic_info_layout.addRow("اسم المدرسة:", self.school_name_input)

        self.school_address_input = QLineEdit()
        self.school_address_input.setPlaceholderText("أدخل عنوان المدرسة")
        basic_info_layout.addRow("العنوان:", self.school_address_input)

        self.school_phone_input = QLineEdit()
        self.school_phone_input.setPlaceholderText("أدخل رقم الهاتف")
        basic_info_layout.addRow("رقم الهاتف:", self.school_phone_input)

        self.school_email_input = QLineEdit()
        self.school_email_input.setPlaceholderText("أدخل البريد الإلكتروني")
        basic_info_layout.addRow("البريد الإلكتروني:", self.school_email_input)

        self.school_website_input = QLineEdit()
        self.school_website_input.setPlaceholderText("أدخل الموقع الإلكتروني")
        basic_info_layout.addRow("الموقع الإلكتروني:", self.school_website_input)

        layout.addWidget(basic_info_group)

        # معلومات إدارية
        admin_info_group = QGroupBox("المعلومات الإدارية")
        admin_info_group.setStyleSheet(self.get_group_style())
        admin_info_layout = QFormLayout(admin_info_group)

        self.principal_name_input = QLineEdit()
        self.principal_name_input.setPlaceholderText("أدخل اسم المدير")
        admin_info_layout.addRow("اسم المدير:", self.principal_name_input)

        self.license_number_input = QLineEdit()
        self.license_number_input.setPlaceholderText("أدخل رقم الترخيص")
        admin_info_layout.addRow("رقم الترخيص:", self.license_number_input)

        self.academic_year_input = QLineEdit()
        self.academic_year_input.setPlaceholderText("2024-2025")
        admin_info_layout.addRow("السنة الدراسية:", self.academic_year_input)

        layout.addWidget(admin_info_group)
        layout.addStretch()

    def setup_system_tab(self):
        """إعداد تبويب إعدادات النظام"""
        layout = QVBoxLayout(self.system_tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # إعدادات قاعدة البيانات
        database_group = QGroupBox("قاعدة البيانات")
        database_group.setStyleSheet(self.get_group_style())
        database_layout = QFormLayout(database_group)

        self.db_path_input = QLineEdit()
        self.db_path_input.setPlaceholderText("مسار قاعدة البيانات")
        self.db_path_input.setReadOnly(True)

        self.browse_db_button = QPushButton("تصفح")
        self.browse_db_button.setFixedWidth(80)

        db_path_layout = QHBoxLayout()
        db_path_layout.addWidget(self.db_path_input)
        db_path_layout.addWidget(self.browse_db_button)

        database_layout.addRow("مسار قاعدة البيانات:", db_path_layout)

        self.auto_backup_check = QCheckBox("نسخ احتياطي تلقائي")
        self.auto_backup_check.setChecked(True)
        database_layout.addRow("", self.auto_backup_check)

        layout.addWidget(database_group)

        # إعدادات الأمان
        security_group = QGroupBox("الأمان")
        security_group.setStyleSheet(self.get_group_style())
        security_layout = QFormLayout(security_group)

        self.session_timeout_spin = QSpinBox()
        self.session_timeout_spin.setRange(5, 120)
        self.session_timeout_spin.setValue(30)
        self.session_timeout_spin.setSuffix(" دقيقة")
        security_layout.addRow("انتهاء الجلسة:", self.session_timeout_spin)

        self.require_password_check = QCheckBox("طلب كلمة مرور عند البدء")
        security_layout.addRow("", self.require_password_check)

        self.log_activities_check = QCheckBox("تسجيل الأنشطة")
        self.log_activities_check.setChecked(True)
        security_layout.addRow("", self.log_activities_check)

        layout.addWidget(security_group)

        # إعدادات الأداء
        performance_group = QGroupBox("الأداء")
        performance_group.setStyleSheet(self.get_group_style())
        performance_layout = QFormLayout(performance_group)

        self.cache_size_spin = QSpinBox()
        self.cache_size_spin.setRange(10, 500)
        self.cache_size_spin.setValue(100)
        self.cache_size_spin.setSuffix(" ميجابايت")
        performance_layout.addRow("حجم التخزين المؤقت:", self.cache_size_spin)

        self.auto_save_check = QCheckBox("حفظ تلقائي")
        self.auto_save_check.setChecked(True)
        performance_layout.addRow("", self.auto_save_check)

        layout.addWidget(performance_group)
        layout.addStretch()

    def setup_backup_tab(self):
        """إعداد تبويب النسخ الاحتياطي"""
        layout = QVBoxLayout(self.backup_tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # إعدادات النسخ الاحتياطي
        backup_settings_group = QGroupBox("إعدادات النسخ الاحتياطي")
        backup_settings_group.setStyleSheet(self.get_group_style())
        backup_settings_layout = QFormLayout(backup_settings_group)

        self.backup_path_input = QLineEdit()
        self.backup_path_input.setPlaceholderText("مجلد النسخ الاحتياطي")

        self.browse_backup_button = QPushButton("تصفح")
        self.browse_backup_button.setFixedWidth(80)

        backup_path_layout = QHBoxLayout()
        backup_path_layout.addWidget(self.backup_path_input)
        backup_path_layout.addWidget(self.browse_backup_button)

        backup_settings_layout.addRow("مجلد النسخ الاحتياطي:", backup_path_layout)

        self.backup_frequency_combo = QComboBox()
        self.backup_frequency_combo.addItems(["يومي", "أسبوعي", "شهري", "يدوي"])
        backup_settings_layout.addRow("تكرار النسخ:", self.backup_frequency_combo)

        self.keep_backups_spin = QSpinBox()
        self.keep_backups_spin.setRange(1, 50)
        self.keep_backups_spin.setValue(10)
        self.keep_backups_spin.setSuffix(" نسخة")
        backup_settings_layout.addRow("عدد النسخ المحفوظة:", self.keep_backups_spin)

        layout.addWidget(backup_settings_group)

        # أزرار النسخ الاحتياطي
        backup_actions_group = QGroupBox("إجراءات النسخ الاحتياطي")
        backup_actions_group.setStyleSheet(self.get_group_style())
        backup_actions_layout = QVBoxLayout(backup_actions_group)

        self.create_backup_button = QPushButton("إنشاء نسخة احتياطية الآن")
        self.create_backup_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)

        self.restore_backup_button = QPushButton("استعادة من نسخة احتياطية")
        self.restore_backup_button.setStyleSheet("""
            QPushButton {
                background-color: #e67e22;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
        """)

        backup_actions_layout.addWidget(self.create_backup_button)
        backup_actions_layout.addWidget(self.restore_backup_button)

        layout.addWidget(backup_actions_group)
        layout.addStretch()

    def get_group_style(self):
        """الحصول على أنماط المجموعات"""
        return """
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """

    def setup_connections(self):
        """ربط الأحداث"""
        self.save_button.clicked.connect(self.save_settings)
        self.reset_button.clicked.connect(self.reset_settings)
        self.browse_db_button.clicked.connect(self.browse_database_path)
        self.browse_backup_button.clicked.connect(self.browse_backup_path)

        # ربط تحديث الخط
        self.font_family_combo.currentFontChanged.connect(self.update_font_preview)
        self.font_size_spin.valueChanged.connect(self.update_font_preview)
        self.create_backup_button.clicked.connect(self.create_backup)
        self.restore_backup_button.clicked.connect(self.restore_backup)

    def update_font_preview(self):
        """تحديث معاينة الخط"""
        try:
            font_family = self.font_family_combo.currentFont().family()
            font_size = self.font_size_spin.value()

            # إنشاء خط جديد
            font = QFont(font_family, font_size)

            # تطبيق الخط على معاينة
            self.font_preview_label.setFont(font)

            # تحديث النص ليشمل معلومات الخط
            self.font_preview_label.setText(
                f"معاينة الخط - {font_family} - {font_size}pt\n"
                f"Sample Text - نص تجريبي - 1234567890"
            )

        except Exception as e:
            print(f"خطأ في تحديث معاينة الخط: {e}")

    def apply_font_to_application(self):
        """تطبيق الخط على التطبيق بالكامل"""
        try:
            font_family = self.font_family_combo.currentFont().family()
            font_size = self.font_size_spin.value()

            # إنشاء خط جديد
            font = QFont(font_family, font_size)

            # تطبيق الخط على التطبيق بالكامل
            QApplication.instance().setFont(font)

            # إرسال إشارة تحديث الخط
            self.font_changed.emit(font)

            # حفظ إعدادات الخط
            self.settings.setValue("font_family", font_family)
            self.settings.setValue("font_size", font_size)

            print(f"تم تطبيق الخط: {font_family} - {font_size}pt")

        except Exception as e:
            print(f"خطأ في تطبيق الخط: {e}")

    def load_settings(self):
        """تحميل الإعدادات المحفوظة"""
        try:
            # الإعدادات العامة
            self.language_combo.setCurrentText(self.settings.value("language", "العربية"))
            self.theme_combo.setCurrentText(self.settings.value("theme", "فاتح"))

            # تحميل إعدادات الخط
            font_family = self.settings.value("font_family", "Arial")
            font_size = int(self.settings.value("font_size", 12))

            # تعيين نوع الخط
            font = QFont(font_family)
            self.font_family_combo.setCurrentFont(font)

            # تعيين حجم الخط
            self.font_size_spin.setValue(font_size)

            # تحديث معاينة الخط
            self.update_font_preview()

            # تطبيق الخط على التطبيق إذا كان محفوظ
            if self.settings.value("apply_font_on_startup", True, type=bool):
                self.apply_font_to_application()

            self.date_format_combo.setCurrentText(self.settings.value("date_format", "dd/MM/yyyy"))
            self.time_format_combo.setCurrentText(self.settings.value("time_format", "24 ساعة"))

            self.enable_notifications_check.setChecked(
                self.settings.value("enable_notifications", True, type=bool)
            )
            self.sound_notifications_check.setChecked(
                self.settings.value("sound_notifications", False, type=bool)
            )
            self.desktop_notifications_check.setChecked(
                self.settings.value("desktop_notifications", True, type=bool)
            )

            # معلومات المدرسة
            self.school_name_input.setText(self.settings.value("school_name", ""))
            self.school_address_input.setText(self.settings.value("school_address", ""))
            self.school_phone_input.setText(self.settings.value("school_phone", ""))
            self.school_email_input.setText(self.settings.value("school_email", ""))
            self.school_website_input.setText(self.settings.value("school_website", ""))
            self.principal_name_input.setText(self.settings.value("principal_name", ""))
            self.license_number_input.setText(self.settings.value("license_number", ""))
            self.academic_year_input.setText(self.settings.value("academic_year", "2024-2025"))

            # إعدادات النظام
            self.db_path_input.setText(self.settings.value("db_path", "data/school.db"))
            self.auto_backup_check.setChecked(
                self.settings.value("auto_backup", True, type=bool)
            )
            self.session_timeout_spin.setValue(int(self.settings.value("session_timeout", 30)))
            self.require_password_check.setChecked(
                self.settings.value("require_password", False, type=bool)
            )
            self.log_activities_check.setChecked(
                self.settings.value("log_activities", True, type=bool)
            )
            self.cache_size_spin.setValue(int(self.settings.value("cache_size", 100)))
            self.auto_save_check.setChecked(
                self.settings.value("auto_save", True, type=bool)
            )

            # إعدادات النسخ الاحتياطي
            self.backup_path_input.setText(self.settings.value("backup_path", "backups/"))
            self.backup_frequency_combo.setCurrentText(
                self.settings.value("backup_frequency", "أسبوعي")
            )
            self.keep_backups_spin.setValue(int(self.settings.value("keep_backups", 10)))

        except Exception as e:
            QMessageBox.warning(self, "تحذير", f"حدث خطأ في تحميل الإعدادات: {str(e)}")

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # الإعدادات العامة
            self.settings.setValue("language", self.language_combo.currentText())
            self.settings.setValue("theme", self.theme_combo.currentText())

            # حفظ إعدادات الخط
            self.settings.setValue("font_family", self.font_family_combo.currentFont().family())
            self.settings.setValue("font_size", self.font_size_spin.value())
            self.settings.setValue("apply_font_on_startup", True)

            # تطبيق الخط فوراً
            self.apply_font_to_application()

            self.settings.setValue("date_format", self.date_format_combo.currentText())
            self.settings.setValue("time_format", self.time_format_combo.currentText())

            self.settings.setValue("enable_notifications", self.enable_notifications_check.isChecked())
            self.settings.setValue("sound_notifications", self.sound_notifications_check.isChecked())
            self.settings.setValue("desktop_notifications", self.desktop_notifications_check.isChecked())

            # معلومات المدرسة
            self.settings.setValue("school_name", self.school_name_input.text())
            self.settings.setValue("school_address", self.school_address_input.text())
            self.settings.setValue("school_phone", self.school_phone_input.text())
            self.settings.setValue("school_email", self.school_email_input.text())
            self.settings.setValue("school_website", self.school_website_input.text())
            self.settings.setValue("principal_name", self.principal_name_input.text())
            self.settings.setValue("license_number", self.license_number_input.text())
            self.settings.setValue("academic_year", self.academic_year_input.text())

            # إعدادات النظام
            self.settings.setValue("db_path", self.db_path_input.text())
            self.settings.setValue("auto_backup", self.auto_backup_check.isChecked())
            self.settings.setValue("session_timeout", self.session_timeout_spin.value())
            self.settings.setValue("require_password", self.require_password_check.isChecked())
            self.settings.setValue("log_activities", self.log_activities_check.isChecked())
            self.settings.setValue("cache_size", self.cache_size_spin.value())
            self.settings.setValue("auto_save", self.auto_save_check.isChecked())

            # إعدادات النسخ الاحتياطي
            self.settings.setValue("backup_path", self.backup_path_input.text())
            self.settings.setValue("backup_frequency", self.backup_frequency_combo.currentText())
            self.settings.setValue("keep_backups", self.keep_backups_spin.value())

            # حفظ الإعدادات
            self.settings.sync()

            QMessageBox.information(self, "نجح", "تم حفظ الإعدادات بنجاح!")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ الإعدادات: {str(e)}")

    def reset_settings(self):
        """إعادة تعيين الإعدادات للقيم الافتراضية"""
        reply = QMessageBox.question(
            self,
            "تأكيد إعادة التعيين",
            "هل أنت متأكد من رغبتك في إعادة تعيين جميع الإعدادات للقيم الافتراضية؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # مسح جميع الإعدادات
                self.settings.clear()

                # إعادة تحميل القيم الافتراضية
                self.load_settings()

                QMessageBox.information(self, "نجح", "تم إعادة تعيين الإعدادات بنجاح!")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ في إعادة تعيين الإعدادات: {str(e)}")

    def browse_database_path(self):
        """تصفح مسار قاعدة البيانات"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختيار ملف قاعدة البيانات",
            "",
            "ملفات قاعدة البيانات (*.db *.sqlite)"
        )

        if file_path:
            self.db_path_input.setText(file_path)

    def browse_backup_path(self):
        """تصفح مجلد النسخ الاحتياطي"""
        folder_path = QFileDialog.getExistingDirectory(
            self,
            "اختيار مجلد النسخ الاحتياطي"
        )

        if folder_path:
            self.backup_path_input.setText(folder_path)

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            backup_path = self.backup_path_input.text()
            if not backup_path:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد مجلد النسخ الاحتياطي أولاً")
                return

            # إنشاء المجلد إذا لم يكن موجوداً
            os.makedirs(backup_path, exist_ok=True)

            # هنا يمكن إضافة منطق النسخ الاحتياطي الفعلي
            QMessageBox.information(
                self,
                "نسخة احتياطية",
                f"سيتم إنشاء نسخة احتياطية في المجلد:\n{backup_path}\n\n"
                "هذه الميزة ستكون متاحة قريباً مع تطوير نظام النسخ الاحتياطي"
            )

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في إنشاء النسخة الاحتياطية: {str(e)}")

    def restore_backup(self):
        """استعادة من نسخة احتياطية"""
        try:
            backup_file, _ = QFileDialog.getOpenFileName(
                self,
                "اختيار ملف النسخة الاحتياطية",
                self.backup_path_input.text(),
                "ملفات النسخ الاحتياطي (*.bak *.backup *.zip)"
            )

            if backup_file:
                reply = QMessageBox.question(
                    self,
                    "تأكيد الاستعادة",
                    f"هل أنت متأكد من رغبتك في استعادة النسخة الاحتياطية؟\n"
                    f"الملف: {backup_file}\n\n"
                    "تحذير: سيتم استبدال البيانات الحالية!",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    # هنا يمكن إضافة منطق الاستعادة الفعلي
                    QMessageBox.information(
                        self,
                        "استعادة النسخة الاحتياطية",
                        f"سيتم استعادة النسخة الاحتياطية من:\n{backup_file}\n\n"
                        "هذه الميزة ستكون متاحة قريباً مع تطوير نظام النسخ الاحتياطي"
                    )

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في استعادة النسخة الاحتياطية: {str(e)}")

    def get_setting(self, key, default_value=None):
        """الحصول على قيمة إعداد معين"""
        return self.settings.value(key, default_value)

    def set_setting(self, key, value):
        """تعيين قيمة إعداد معين"""
        self.settings.setValue(key, value)
        self.settings.sync()
