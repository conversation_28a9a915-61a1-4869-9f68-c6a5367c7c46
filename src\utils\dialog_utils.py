#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أدوات مساعدة للنوافذ والحوارات
"""

from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import QDialog
from src.utils.font_manager import get_font_stylesheet
from src.utils.icon_manager import set_window_icon


def setup_dialog_window(dialog, title, width=500, height=400, modal=True):
    """
    إعداد موحد لنوافذ الحوار لتجنب مشاكل التداخل
    
    Args:
        dialog: نافذة الحوار
        title: عنوان النافذة
        width: العرض
        height: الارتفاع
        modal: هل النافذة مودالية
    """
    # إعداد العنوان والحجم
    dialog.setWindowTitle(title)
    dialog.setFixedSize(width, height)
    
    # إعداد النافذة كمودالية
    dialog.setModal(modal)
    
    # إعدادات النافذة لضمان الظهور في المقدمة
    dialog.setWindowFlags(
        Qt.Dialog | 
        Qt.WindowTitleHint | 
        Qt.WindowCloseButtonHint |
        Qt.WindowStaysOnTopHint
    )
    
    # إعدادات إضافية
    dialog.setAttribute(Qt.WA_DeleteOnClose)
    dialog.setLayoutDirection(Qt.RightToLeft)
    
    # تعيين أيقونة النافذة
    set_window_icon(dialog)

    # رفع النافذة للمقدمة
    dialog.raise_()
    dialog.activateWindow()
    dialog.show()


def apply_dialog_styles(dialog):
    """
    تطبيق أنماط موحدة لجميع النوافذ
    
    Args:
        dialog: نافذة الحوار
    """
    dialog.setStyleSheet("""
        QDialog {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Arial;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 10px;
            background-color: white;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #495057;
        }
        QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QTextEdit {
            border: 2px solid #dee2e6;
            border-radius: 5px;
            padding: 8px;
            """ + get_font_stylesheet('input') + """
            background-color: white;
        }
        QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus, QTextEdit:focus {
            border-color: #007bff;
        }
        QPushButton {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            """ + get_font_stylesheet('button') + """
        }
        QPushButton:hover {
            background-color: #0056b3;
        }
        QPushButton:pressed {
            background-color: #004085;
        }
        QCheckBox {
            """ + get_font_stylesheet('label') + """
            spacing: 5px;
        }
        QCheckBox::indicator {
            width: 18px;
            height: 18px;
        }
        QCheckBox::indicator:unchecked {
            border: 2px solid #dee2e6;
            border-radius: 3px;
            background-color: white;
        }
        QCheckBox::indicator:checked {
            border: 2px solid #007bff;
            border-radius: 3px;
            background-color: #007bff;
            image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xMC42IDEuNEw0LjIgNy44TDEuNCA1IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
        }
        QLabel {
            color: #495057;
            """ + get_font_stylesheet('label') + """
        }
        QFrame {
            border: none;
        }
    """)


def center_dialog_on_parent(dialog, parent=None):
    """
    توسيط النافذة على النافذة الأب أو الشاشة
    
    Args:
        dialog: نافذة الحوار
        parent: النافذة الأب
    """
    if parent:
        # توسيط على النافذة الأب
        parent_geometry = parent.geometry()
        dialog_geometry = dialog.geometry()
        
        x = parent_geometry.x() + (parent_geometry.width() - dialog_geometry.width()) // 2
        y = parent_geometry.y() + (parent_geometry.height() - dialog_geometry.height()) // 2
        
        dialog.move(x, y)
    else:
        # توسيط على الشاشة
        from PyQt5.QtWidgets import QApplication
        screen = QApplication.desktop().screenGeometry()
        dialog_geometry = dialog.geometry()
        
        x = (screen.width() - dialog_geometry.width()) // 2
        y = (screen.height() - dialog_geometry.height()) // 2
        
        dialog.move(x, y)


def show_dialog_safely(dialog, parent=None):
    """
    عرض النافذة بشكل آمن مع ضمان الظهور في المقدمة
    
    Args:
        dialog: نافذة الحوار
        parent: النافذة الأب
    """
    # توسيط النافذة
    center_dialog_on_parent(dialog, parent)
    
    # رفع النافذة للمقدمة
    dialog.raise_()
    dialog.activateWindow()
    
    # إظهار النافذة
    dialog.show()
    
    # التأكد من التركيز
    dialog.setFocus()
    
    return dialog.exec_()


class BaseDialog(QDialog):
    """
    فئة أساسية للنوافذ مع إعدادات موحدة
    """
    
    def __init__(self, title="نافذة حوار", width=500, height=400, parent=None):
        super().__init__(parent)

        # إعداد النافذة
        setup_dialog_window(self, title, width, height)

        # تطبيق الأنماط
        apply_dialog_styles(self)

        # تعيين أيقونة النافذة
        set_window_icon(self)

        # توسيط النافذة
        center_dialog_on_parent(self, parent)
    
    def show_safely(self):
        """عرض النافذة بشكل آمن"""
        return show_dialog_safely(self, self.parent())
    
    def closeEvent(self, event):
        """التعامل مع إغلاق النافذة"""
        self.reject()
        event.accept()


def fix_dialog_z_order():
    """
    إصلاح ترتيب النوافذ (Z-order) لتجنب التداخل
    """
    from PyQt5.QtWidgets import QApplication
    
    # الحصول على جميع النوافذ المفتوحة
    app = QApplication.instance()
    if app:
        # رفع النوافذ المودالية للمقدمة
        for widget in app.allWidgets():
            if isinstance(widget, QDialog) and widget.isModal() and widget.isVisible():
                widget.raise_()
                widget.activateWindow()


def ensure_dialog_visibility(dialog):
    """
    ضمان رؤية النافذة وعدم اختفائها خلف النوافذ الأخرى
    
    Args:
        dialog: نافذة الحوار
    """
    # التأكد من أن النافذة مرئية
    if not dialog.isVisible():
        dialog.show()
    
    # رفع النافذة للمقدمة
    dialog.raise_()
    dialog.activateWindow()
    
    # إعطاء التركيز للنافذة
    dialog.setFocus()
    
    # إصلاح ترتيب النوافذ
    fix_dialog_z_order()
