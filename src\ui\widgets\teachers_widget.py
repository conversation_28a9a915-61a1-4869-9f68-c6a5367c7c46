#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ويدجت إدارة المعلمين والرواتب
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHB<PERSON>Layout, QTableWidget,
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                             QMessageBox, QHeaderView, QFrame, QTabWidget,
                             QComboBox, QDateEdit, QSpinBox, QDoubleSpinBox)
from PyQt5.QtCore import Qt, QDate

from src.models.teacher import Teacher
from src.models.currency import CurrencyModel
from src.ui.dialogs.teacher_dialog import TeacherDialog
from src.ui.dialogs.salary_dialog import SalaryDialog


class TeachersWidget(QWidget):
    """ويدجت إدارة المعلمين والرواتب"""

    def __init__(self):
        super().__init__()
        self.teacher_model = Teacher()
        self.currency_model = CurrencyModel(self.teacher_model.db_manager)
        self.currencies_cache = {}  # كاش للعملات لتحسين الأداء
        self.load_currencies_cache()
        self.setup_ui()
        self.load_teachers()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # علامات التبويب
        self.tab_widget = QTabWidget()

        # تبويب المعلمين
        self.teachers_tab = QWidget()
        self.setup_teachers_tab()
        self.tab_widget.addTab(self.teachers_tab, "المعلمين والموظفين")

        # تبويب الرواتب
        self.salaries_tab = QWidget()
        self.setup_salaries_tab()
        self.tab_widget.addTab(self.salaries_tab, "إدارة الرواتب")

        layout.addWidget(self.tab_widget)

    def load_currencies_cache(self):
        """تحميل كاش العملات لتحسين الأداء"""
        try:
            currencies = self.currency_model.get_active_currencies()
            self.currencies_cache = {currency['currency_id']: currency for currency in currencies}
        except Exception as e:
            print(f"خطأ في تحميل العملات: {e}")
            # إضافة عملة افتراضية في حالة الخطأ
            self.currencies_cache = {1: {'currency_id': 1, 'symbol': 'ر.س', 'currency_name': 'الريال السعودي'}}

    def get_currency_symbol(self, currency_id):
        """الحصول على رمز العملة من الكاش"""
        if currency_id in self.currencies_cache:
            return self.currencies_cache[currency_id]['symbol']
        return 'ر.س'  # العملة الافتراضية

    def format_salary_with_currency(self, salary, currency_id):
        """تنسيق الراتب مع رمز العملة"""
        if salary is None:
            salary = 0
        symbol = self.get_currency_symbol(currency_id)
        return f"{salary:,.2f} {symbol}"

    def refresh_currencies_and_reload(self):
        """تحديث كاش العملات وإعادة تحميل البيانات"""
        self.load_currencies_cache()
        self.load_teachers()

    def on_teacher_updated(self):
        """استدعاء عند تحديث بيانات معلم"""
        self.refresh_currencies_and_reload()

    def setup_teachers_tab(self):
        """إعداد تبويب المعلمين"""
        layout = QVBoxLayout(self.teachers_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)

        # شريط البحث والأزرار
        top_frame = QFrame()
        top_layout = QHBoxLayout(top_frame)

        # حقل البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("البحث عن معلم...")
        self.search_input.setFixedHeight(35)
        self.search_input.textChanged.connect(self.search_teachers)

        # أزرار الإجراءات
        self.add_teacher_button = QPushButton("إضافة معلم")
        self.edit_teacher_button = QPushButton("تعديل")
        self.delete_teacher_button = QPushButton("حذف")
        self.refresh_teachers_button = QPushButton("تحديث")

        # تطبيق الأنماط على الأزرار
        button_style = """
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """

        for button in [self.add_teacher_button, self.edit_teacher_button,
                      self.delete_teacher_button, self.refresh_teachers_button]:
            button.setStyleSheet(button_style)
            button.setFixedHeight(35)

        # تخصيص لون زر الحذف
        self.delete_teacher_button.setStyleSheet(
            button_style.replace("#3498db", "#e74c3c").replace("#2980b9", "#c0392b")
        )

        top_layout.addWidget(QLabel("البحث:"))
        top_layout.addWidget(self.search_input)
        top_layout.addStretch()
        top_layout.addWidget(self.add_teacher_button)
        top_layout.addWidget(self.edit_teacher_button)
        top_layout.addWidget(self.delete_teacher_button)
        top_layout.addWidget(self.refresh_teachers_button)

        layout.addWidget(top_frame)

        # جدول المعلمين
        self.teachers_table = QTableWidget()
        self.setup_teachers_table()
        layout.addWidget(self.teachers_table)

        # ربط الأحداث
        self.setup_teachers_connections()

    def setup_teachers_table(self):
        """إعداد جدول المعلمين"""
        columns = [
            "رقم الموظف", "الاسم الأول", "الاسم الأخير",
            "التخصص", "المنصب", "الراتب", "تاريخ التوظيف", "الحالة"
        ]

        self.teachers_table.setColumnCount(len(columns))
        self.teachers_table.setHorizontalHeaderLabels(columns)

        # إعداد خصائص الجدول
        self.teachers_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.teachers_table.setAlternatingRowColors(True)
        self.teachers_table.setSortingEnabled(True)

        # تخصيص عرض الأعمدة
        header = self.teachers_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)

        # تطبيق الأنماط
        self.teachers_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

    def setup_teachers_connections(self):
        """ربط أحداث المعلمين"""
        self.add_teacher_button.clicked.connect(self.add_teacher)
        self.edit_teacher_button.clicked.connect(self.edit_teacher)
        self.delete_teacher_button.clicked.connect(self.delete_teacher)
        self.refresh_teachers_button.clicked.connect(self.load_teachers)

    def load_teachers(self):
        """تحميل قائمة المعلمين"""
        try:
            teachers = self.teacher_model.get_all_teachers()
            self.populate_teachers_table(teachers)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل المعلمين: {str(e)}")

    def populate_teachers_table(self, teachers):
        """ملء جدول المعلمين بالبيانات"""
        self.teachers_table.setRowCount(len(teachers))

        for row, teacher in enumerate(teachers):
            try:
                # رقم الموظف
                self.teachers_table.setItem(row, 0, QTableWidgetItem(str(teacher['employee_number'] or '')))

                # الاسم الأول
                self.teachers_table.setItem(row, 1, QTableWidgetItem(str(teacher['first_name'] or '')))

                # الاسم الأخير
                self.teachers_table.setItem(row, 2, QTableWidgetItem(str(teacher['last_name'] or '')))

                # التخصص
                self.teachers_table.setItem(row, 3, QTableWidgetItem(str(teacher['specialization'] or '')))

                # المنصب
                self.teachers_table.setItem(row, 4, QTableWidgetItem(str(teacher['position'] or '')))

                # الراتب مع العملة
                salary = teacher['salary'] if teacher['salary'] else 0
                try:
                    currency_id = teacher.get('currency_id', 1)  # العملة الافتراضية إذا لم تكن محددة
                    salary_text = self.format_salary_with_currency(salary, currency_id)
                except (KeyError, TypeError):
                    # في حالة عدم وجود عمود currency_id، استخدم العرض القديم
                    salary_text = f"{salary:,.2f} ر.س"
                self.teachers_table.setItem(row, 5, QTableWidgetItem(salary_text))

                # تاريخ التوظيف
                self.teachers_table.setItem(row, 6, QTableWidgetItem(str(teacher['hire_date'] or '')))

                # الحالة
                status = teacher['status'] if 'status' in teacher else 'active'
                status_text = "نشط" if status == 'active' else "غير نشط"
                self.teachers_table.setItem(row, 7, QTableWidgetItem(status_text))

                # حفظ معرف المعلم في البيانات المخفية
                self.teachers_table.item(row, 0).setData(Qt.UserRole, teacher.get('teacher_id'))

            except Exception as e:
                print(f"خطأ في إضافة المعلم رقم {row}: {e}")

    def search_teachers(self):
        """البحث في المعلمين"""
        search_text = self.search_input.text().strip()

        if not search_text:
            self.load_teachers()
            return

        try:
            teachers = self.teacher_model.search_teachers(search_text)
            self.populate_teachers_table(teachers)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في البحث: {str(e)}")

    def add_teacher(self):
        """إضافة معلم جديد"""
        dialog = TeacherDialog(parent=self)
        dialog.teacher_saved.connect(self.on_teacher_updated)
        dialog.exec_()

    def edit_teacher(self):
        """تعديل معلم"""
        current_row = self.teachers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار معلم للتعديل")
            return

        # الحصول على معرف المعلم
        teacher_id = self.teachers_table.item(current_row, 0).data(Qt.UserRole)

        dialog = TeacherDialog(teacher_id=teacher_id, parent=self)
        dialog.teacher_saved.connect(self.on_teacher_updated)
        dialog.exec_()

    def delete_teacher(self):
        """حذف معلم"""
        current_row = self.teachers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار معلم للحذف")
            return

        # الحصول على معرف المعلم
        teacher_id = self.teachers_table.item(current_row, 0).data(Qt.UserRole)
        teacher_name = f"{self.teachers_table.item(current_row, 1).text()} {self.teachers_table.item(current_row, 2).text()}"

        # تأكيد الحذف
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل أنت متأكد من رغبتك في حذف المعلم: {teacher_name}؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                self.teacher_model.deactivate_teacher(teacher_id, "تم الحذف من الواجهة")
                QMessageBox.information(self, "نجح", "تم حذف المعلم بنجاح")
                self.load_teachers()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ في حذف المعلم: {str(e)}")

    def setup_salaries_tab(self):
        """إعداد تبويب الرواتب"""
        layout = QVBoxLayout(self.salaries_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)

        # شريط التحكم في الرواتب
        control_frame = QFrame()
        control_layout = QHBoxLayout(control_frame)

        # اختيار الشهر والسنة
        control_layout.addWidget(QLabel("الشهر:"))
        self.month_combo = QComboBox()
        months = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                 "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]
        self.month_combo.addItems(months)
        # تحديد الشهر والسنة الافتراضية بشكل منطقي
        from datetime import datetime
        current_date = datetime.now()

        # إذا كان التاريخ في المستقبل، استخدم التاريخ الحالي الفعلي
        if current_date.year > 2024:
            # استخدم ديسمبر 2024 كافتراضي
            default_month = 12
            default_year = 2024
        else:
            default_month = current_date.month
            default_year = current_date.year

        self.month_combo.setCurrentIndex(default_month - 1)

        control_layout.addWidget(self.month_combo)

        control_layout.addWidget(QLabel("السنة:"))
        self.year_spin = QSpinBox()
        self.year_spin.setRange(2020, 2030)
        self.year_spin.setValue(default_year)

        control_layout.addWidget(self.year_spin)

        # أزرار الرواتب
        self.load_salaries_button = QPushButton("تحميل الرواتب")
        self.add_salary_button = QPushButton("إضافة راتب")
        self.edit_salary_button = QPushButton("تعديل راتب")
        self.generate_payroll_button = QPushButton("إنشاء كشف رواتب")

        salary_button_style = """
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """

        for button in [self.load_salaries_button, self.add_salary_button,
                      self.edit_salary_button, self.generate_payroll_button]:
            button.setStyleSheet(salary_button_style)
            button.setFixedHeight(35)

        control_layout.addStretch()
        control_layout.addWidget(self.load_salaries_button)
        control_layout.addWidget(self.add_salary_button)
        control_layout.addWidget(self.edit_salary_button)
        control_layout.addWidget(self.generate_payroll_button)

        layout.addWidget(control_frame)

        # جدول الرواتب
        self.salaries_table = QTableWidget()
        self.setup_salaries_table()
        layout.addWidget(self.salaries_table)

        # ربط أحداث الرواتب
        self.setup_salaries_connections()

    def setup_salaries_table(self):
        """إعداد جدول الرواتب"""
        columns = [
            "اسم الموظف", "الراتب الأساسي", "البدلات", "الخصومات",
            "الإضافي", "إجمالي الراتب", "تاريخ الدفع", "الحالة"
        ]

        self.salaries_table.setColumnCount(len(columns))
        self.salaries_table.setHorizontalHeaderLabels(columns)

        # إعداد خصائص الجدول
        self.salaries_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.salaries_table.setAlternatingRowColors(True)
        self.salaries_table.setSortingEnabled(True)

        # تخصيص عرض الأعمدة
        header = self.salaries_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)

        # تطبيق الأنماط
        self.salaries_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
            }
            QTableWidget::item:selected {
                background-color: #27ae60;
                color: white;
            }
            QHeaderView::section {
                background-color: #2c3e50;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

    def setup_salaries_connections(self):
        """ربط أحداث الرواتب"""
        self.load_salaries_button.clicked.connect(self.load_salaries)
        self.add_salary_button.clicked.connect(self.add_salary)
        self.edit_salary_button.clicked.connect(self.edit_salary)
        self.generate_payroll_button.clicked.connect(self.generate_payroll)
        self.month_combo.currentTextChanged.connect(self.load_salaries)
        self.year_spin.valueChanged.connect(self.load_salaries)

    def load_salaries(self):
        """تحميل رواتب الشهر المحدد"""
        try:
            month = self.month_combo.currentIndex() + 1
            year = self.year_spin.value()

            # التحقق من صحة التاريخ المحدد
            from datetime import datetime
            current_date = datetime.now()

            # إذا كان تاريخ النظام في المستقبل، استخدم 2024 كمرجع
            reference_year = 2024 if current_date.year > 2024 else current_date.year

            # إذا كان التاريخ المحدد بعيد جداً في المستقبل، اعرض تحذير
            if year > reference_year + 1:
                QMessageBox.warning(
                    self,
                    "تحذير",
                    f"التاريخ المحدد ({month}/{year}) بعيد في المستقبل.\n"
                    "قد لا تجد رواتب مسجلة لهذا التاريخ."
                )

            # استعلام الرواتب من قاعدة البيانات
            query = """
            SELECT s.*, t.first_name, t.last_name
            FROM salaries s
            JOIN teachers t ON s.teacher_id = t.teacher_id
            WHERE s.month = ? AND s.year = ?
            ORDER BY t.first_name, t.last_name
            """
            salaries = self.teacher_model.db_manager.fetch_all(query, (month, year))
            self.populate_salaries_table(salaries)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل الرواتب: {str(e)}")

    def populate_salaries_table(self, salaries):
        """ملء جدول الرواتب بالبيانات"""
        self.salaries_table.setRowCount(len(salaries))

        for row, salary in enumerate(salaries):
            try:
                # اسم الموظف
                name = f"{salary['first_name']} {salary['last_name']}"
                self.salaries_table.setItem(row, 0, QTableWidgetItem(name))

                # الراتب الأساسي
                basic = salary['basic_salary'] if salary['basic_salary'] else 0
                self.salaries_table.setItem(row, 1, QTableWidgetItem(f"{basic:,.2f}"))

                # البدلات
                allowances = salary['allowances'] if salary['allowances'] else 0
                self.salaries_table.setItem(row, 2, QTableWidgetItem(f"{allowances:,.2f}"))

                # الخصومات
                deductions = salary['deductions'] if salary['deductions'] else 0
                self.salaries_table.setItem(row, 3, QTableWidgetItem(f"{deductions:,.2f}"))

                # الإضافي
                overtime = salary['overtime'] if salary['overtime'] else 0
                self.salaries_table.setItem(row, 4, QTableWidgetItem(f"{overtime:,.2f}"))

                # إجمالي الراتب
                total = salary['total_salary'] if salary['total_salary'] else 0
                self.salaries_table.setItem(row, 5, QTableWidgetItem(f"{total:,.2f}"))

                # تاريخ الدفع
                payment_date = salary['payment_date'] if salary['payment_date'] else "لم يدفع"
                self.salaries_table.setItem(row, 6, QTableWidgetItem(str(payment_date)))

                # الحالة
                status = salary['status'] if salary['status'] else 'pending'
                status_text = "مدفوع" if status == 'paid' else "معلق"
                self.salaries_table.setItem(row, 7, QTableWidgetItem(status_text))

                # حفظ معرف الراتب في البيانات المخفية
                self.salaries_table.item(row, 0).setData(Qt.UserRole, salary['salary_id'])

            except Exception as e:
                print(f"خطأ في إضافة الراتب رقم {row}: {e}")

    def add_salary(self):
        """إضافة راتب جديد"""
        dialog = SalaryDialog(parent=self)
        dialog.salary_saved.connect(self.load_salaries)
        dialog.exec_()

    def edit_salary(self):
        """تعديل راتب"""
        current_row = self.salaries_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار راتب للتعديل")
            return

        # الحصول على معرف الراتب
        salary_id = self.salaries_table.item(current_row, 0).data(Qt.UserRole)

        dialog = SalaryDialog(salary_id=salary_id, parent=self)
        dialog.salary_saved.connect(self.load_salaries)
        dialog.exec_()

    def generate_payroll(self):
        """إنشاء كشف رواتب"""
        month = self.month_combo.currentIndex() + 1
        year = self.year_spin.value()
        month_name = self.month_combo.currentText()

        # التحقق من عدم إنشاء كشوف رواتب للمستقبل
        from datetime import datetime
        current_date = datetime.now()

        # إذا كان التاريخ المحدد في المستقبل، اعرض تحذير
        if year > current_date.year or (year == current_date.year and month > current_date.month):
            # إذا كان تاريخ النظام في المستقبل، استخدم 2024 كمرجع
            reference_year = 2024 if current_date.year > 2024 else current_date.year
            reference_month = 12 if current_date.year > 2024 else current_date.month

            if year > reference_year or (year == reference_year and month > reference_month):
                QMessageBox.warning(
                    self,
                    "تحذير",
                    f"لا يمكن إنشاء كشف رواتب لشهر {month_name} {year}\n"
                    "لا يمكن إنشاء كشوف رواتب للأشهر المستقبلية.\n"
                    f"يرجى اختيار شهر وسنة حتى {reference_month}/{reference_year}"
                )
                return

        QMessageBox.information(
            self,
            "كشف الرواتب",
            f"سيتم إنشاء كشف رواتب شهر {month_name} {year}\n"
            "هذه الميزة ستكون متاحة قريباً في نظام التقارير"
        )
