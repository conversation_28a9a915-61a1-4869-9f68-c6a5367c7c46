#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للتطبيق
"""

import sys
import os

# إضافة مسار المشروع إلى sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_database():
    """اختبار قاعدة البيانات"""
    print("=== اختبار قاعدة البيانات ===")
    
    try:
        from src.database.db_manager import DatabaseManager
        db = DatabaseManager()
        db.initialize_database()
        print("✅ قاعدة البيانات تعمل بشكل صحيح")
        return True
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def test_models():
    """اختبار النماذج"""
    print("\n=== اختبار النماذج ===")
    
    try:
        from src.models.student import Student
        from src.models.teacher import Teacher
        from src.models.user import User
        
        # اختبار نموذج الطلاب
        student_model = Student()
        students = student_model.get_active_students()
        print(f"✅ نموذج الطلاب يعمل - عدد الطلاب: {len(students)}")
        
        # اختبار نموذج المعلمين
        teacher_model = Teacher()
        teachers = teacher_model.get_active_teachers()
        print(f"✅ نموذج المعلمين يعمل - عدد المعلمين: {len(teachers)}")
        
        # اختبار نموذج المستخدمين
        user_model = User()
        users = user_model.get_active_users()
        print(f"✅ نموذج المستخدمين يعمل - عدد المستخدمين: {len(users)}")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في النماذج: {e}")
        return False

def test_authentication():
    """اختبار المصادقة"""
    print("\n=== اختبار المصادقة ===")
    
    try:
        from src.models.user import User
        user_model = User()
        
        # اختبار تسجيل الدخول
        user = user_model.authenticate("admin", "admin123")
        if user:
            print("✅ تسجيل الدخول يعمل بشكل صحيح")
            return True
        else:
            print("❌ فشل في تسجيل الدخول")
            return False
    except Exception as e:
        print(f"❌ خطأ في المصادقة: {e}")
        return False

def test_statistics():
    """اختبار الإحصائيات"""
    print("\n=== اختبار الإحصائيات ===")
    
    try:
        from src.models.student import Student
        from src.models.teacher import Teacher
        
        student_model = Student()
        teacher_model = Teacher()
        
        # اختبار إحصائيات الطلاب
        student_stats = student_model.get_student_statistics()
        print(f"✅ إحصائيات الطلاب: {student_stats}")
        
        # اختبار إحصائيات المعلمين
        teacher_stats = teacher_model.get_teacher_statistics()
        print(f"✅ إحصائيات المعلمين: {teacher_stats}")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في الإحصائيات: {e}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبار برنامج إدارة المدارس")
    print("=" * 50)
    
    tests = [
        test_database,
        test_models,
        test_authentication,
        test_statistics
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبار: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! البرنامج جاهز للاستخدام")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
