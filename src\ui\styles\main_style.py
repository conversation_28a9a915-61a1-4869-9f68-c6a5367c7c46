#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أنماط التطبيق الرئيسية
يحتوي على جميع الأنماط المستخدمة في التطبيق
"""

from src.utils.config import Config
from src.utils.font_manager import get_font_stylesheet


class AppStyles:
    """فئة أنماط التطبيق"""
    
    # الألوان الأساسية
    PRIMARY_COLOR = Config.PRIMARY_COLOR
    SECONDARY_COLOR = Config.SECONDARY_COLOR
    SUCCESS_COLOR = Config.SUCCESS_COLOR
    WARNING_COLOR = Config.WARNING_COLOR
    DANGER_COLOR = Config.DANGER_COLOR
    LIGHT_COLOR = Config.LIGHT_COLOR
    DARK_COLOR = Config.DARK_COLOR
    
    @staticmethod
    def get_main_window_style():
        """أنماط النافذة الرئيسية"""
        return f"""
        QMainWindow {{
            background-color: {AppStyles.LIGHT_COLOR};
            font-family: '{Config.DEFAULT_FONT_FAMILY}';
            font-size: {Config.DEFAULT_FONT_SIZE}px;
        }}
        
        QFrame {{
            background-color: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }}
        
        QListWidget {{
            background-color: transparent;
            border: none;
            outline: none;
            font-size: {Config.DEFAULT_FONT_SIZE + 1}px;
        }}
        
        QListWidget::item {{
            padding: 12px 15px;
            margin: 2px 5px;
            border-radius: 6px;
            color: #495057;
            border: none;
        }}
        
        QListWidget::item:selected {{
            background-color: {AppStyles.PRIMARY_COLOR};
            color: white;
            font-weight: bold;
        }}
        
        QListWidget::item:hover {{
            background-color: #e9ecef;
        }}
        
        QListWidget::item:selected:hover {{
            background-color: {AppStyles.DARK_COLOR};
        }}
        """
    
    @staticmethod
    def get_button_style(color=None):
        """أنماط الأزرار"""
        if color is None:
            color = AppStyles.PRIMARY_COLOR
            
        hover_color = AppStyles.darken_color(color)
        
        return f"""
        QPushButton {{
            background-color: {color};
            color: white;
            border: none;
            border-radius: 6px;
            {get_font_stylesheet('button')}
            padding: 8px 16px;
            min-height: 30px;
        }}
        
        QPushButton:hover {{
            background-color: {hover_color};
        }}
        
        QPushButton:pressed {{
            background-color: {AppStyles.darken_color(color, 0.8)};
        }}
        
        QPushButton:disabled {{
            background-color: #bdc3c7;
            color: #7f8c8d;
        }}
        """
    
    @staticmethod
    def get_input_style():
        """أنماط حقول الإدخال"""
        return f"""
        QLineEdit, QTextEdit, QComboBox, QDateEdit, QSpinBox {{
            border: 2px solid #bdc3c7;
            border-radius: 5px;
            padding: 8px 12px;
            {get_font_stylesheet('input')}
            background-color: white;
            selection-background-color: {AppStyles.SECONDARY_COLOR};
        }}
        
        QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QDateEdit:focus, QSpinBox:focus {{
            border-color: {AppStyles.PRIMARY_COLOR};
            outline: none;
        }}
        
        QLineEdit:disabled, QTextEdit:disabled, QComboBox:disabled, QDateEdit:disabled, QSpinBox:disabled {{
            background-color: #f8f9fa;
            color: #6c757d;
            border-color: #dee2e6;
        }}
        
        QComboBox::drop-down {{
            border: none;
            width: 30px;
        }}
        
        QComboBox::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #495057;
            margin-right: 10px;
        }}
        """
    
    @staticmethod
    def get_table_style():
        """أنماط الجداول"""
        return f"""
        QTableWidget {{
            gridline-color: #dee2e6;
            background-color: white;
            alternate-background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            font-size: {Config.DEFAULT_FONT_SIZE}px;
        }}
        
        QTableWidget::item {{
            padding: 8px;
            border: none;
        }}
        
        QTableWidget::item:selected {{
            background-color: {AppStyles.PRIMARY_COLOR};
            color: white;
        }}
        
        QTableWidget::item:hover {{
            background-color: #e3f2fd;
        }}
        
        QHeaderView::section {{
            background-color: {AppStyles.DARK_COLOR};
            color: white;
            padding: 10px;
            border: none;
            font-weight: bold;
            font-size: {Config.DEFAULT_FONT_SIZE}px;
        }}
        
        QHeaderView::section:hover {{
            background-color: {AppStyles.PRIMARY_COLOR};
        }}
        """
    
    @staticmethod
    def get_dialog_style():
        """أنماط النوافذ المنبثقة"""
        return f"""
        QDialog {{
            background-color: {AppStyles.LIGHT_COLOR};
            font-family: '{Config.DEFAULT_FONT_FAMILY}';
        }}
        
        QTabWidget::pane {{
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            background-color: white;
        }}
        
        QTabWidget::tab-bar {{
            alignment: right;
        }}
        
        QTabBar::tab {{
            background-color: #e9ecef;
            color: #495057;
            padding: 10px 20px;
            margin-right: 2px;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
            font-weight: bold;
        }}
        
        QTabBar::tab:selected {{
            background-color: white;
            color: {AppStyles.PRIMARY_COLOR};
            border-bottom: 2px solid {AppStyles.PRIMARY_COLOR};
        }}
        
        QTabBar::tab:hover {{
            background-color: #dee2e6;
        }}
        """
    
    @staticmethod
    def get_card_style():
        """أنماط البطاقات"""
        return f"""
        .stat-card {{
            background-color: white;
            border: 2px solid {AppStyles.PRIMARY_COLOR};
            border-radius: 10px;
            padding: 15px;
        }}
        
        .stat-card:hover {{
            background-color: #f8f9fa;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }}
        """
    
    @staticmethod
    def darken_color(color, factor=0.9):
        """تغميق اللون"""
        color_map = {
            Config.PRIMARY_COLOR: "#1a252f" if factor < 0.9 else "#34495e",
            Config.SECONDARY_COLOR: "#2980b9" if factor < 0.9 else "#21618c", 
            Config.SUCCESS_COLOR: "#229954" if factor < 0.9 else "#1e8449",
            Config.WARNING_COLOR: "#d68910" if factor < 0.9 else "#b7950b",
            Config.DANGER_COLOR: "#c0392b" if factor < 0.9 else "#a93226"
        }
        return color_map.get(color, color)
    
    @staticmethod
    def get_complete_style():
        """الحصول على جميع الأنماط مجمعة"""
        return f"""
        {AppStyles.get_main_window_style()}
        {AppStyles.get_button_style()}
        {AppStyles.get_input_style()}
        {AppStyles.get_table_style()}
        {AppStyles.get_dialog_style()}
        
        /* أنماط إضافية */
        QLabel {{
            color: #2c3e50;
            {get_font_stylesheet('label')}
        }}
        
        QMessageBox {{
            background-color: white;
            {get_font_stylesheet('default')}
        }}
        
        QScrollBar:vertical {{
            background-color: #f8f9fa;
            width: 12px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:vertical {{
            background-color: #bdc3c7;
            border-radius: 6px;
            min-height: 20px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background-color: #95a5a6;
        }}
        """
