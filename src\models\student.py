#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نموذج الطلاب
يحتوي على جميع العمليات المتعلقة بإدارة الطلاب
"""

from datetime import datetime, date
from src.models.base_model import BaseModel


class Student(BaseModel):
    """نموذج الطلاب"""
    
    def __init__(self):
        super().__init__()
        self.table_name = "students"
        self.primary_key = "student_id"
        
        # الحقول المطلوبة
        self.required_fields = [
            'student_number', 'first_name', 'last_name', 
            'date_of_birth', 'gender', 'parent_name', 'parent_phone'
        ]
        
        # حقول البحث
        self.search_fields = [
            'student_number', 'first_name', 'last_name', 
            'national_id', 'parent_name', 'parent_phone'
        ]

    def generate_student_number(self):
        """توليد رقم طالب تلقائي"""
        try:
            # الحصول على آخر رقم طالب
            query = "SELECT student_number FROM students ORDER BY student_id DESC LIMIT 1"
            last_student = self.db_manager.fetch_one(query)

            if last_student and last_student['student_number']:
                # استخراج الرقم من آخر رقم طالب
                last_number = last_student['student_number']
                if last_number.startswith('STU'):
                    try:
                        number_part = int(last_number[3:])
                        new_number = number_part + 1
                    except ValueError:
                        new_number = 1
                else:
                    new_number = 1
            else:
                new_number = 1

            # تكوين رقم الطالب الجديد
            student_number = f"STU{new_number:06d}"  # مثال: STU000001

            # التحقق من عدم وجود الرقم مسبقاً
            while self.get_student_by_number(student_number):
                new_number += 1
                student_number = f"STU{new_number:06d}"

            return student_number

        except Exception as e:
            # في حالة الخطأ، استخدم timestamp
            import time
            timestamp = int(time.time())
            return f"STU{timestamp}"

    def add_student(self, student_data):
        """إضافة طالب جديد"""
        # توليد رقم الطالب تلقائياً إذا لم يتم تمريره
        if 'student_number' not in student_data or not student_data['student_number']:
            student_data['student_number'] = self.generate_student_number()

        # إزالة student_number من الحقول المطلوبة مؤقتاً للتحقق
        required_fields_temp = [field for field in self.required_fields if field != 'student_number']

        # التحقق من الحقول المطلوبة
        self.validate_required_fields(student_data, required_fields_temp)

        # التحقق من تفرد رقم الطالب
        if 'student_number' in student_data:
            self.validate_unique_field('student_number', student_data['student_number'])

        # التحقق من تفرد رقم الهوية إذا تم إدخاله
        if 'national_id' in student_data and student_data['national_id']:
            self.validate_unique_field('national_id', student_data['national_id'])

        # التحقق من صحة التاريخ
        if 'date_of_birth' in student_data:
            self.validate_date_of_birth(student_data['date_of_birth'])

        # إضافة تاريخ التسجيل إذا لم يكن موجوداً
        if 'enrollment_date' not in student_data:
            student_data['enrollment_date'] = date.today()

        # إضافة الحالة الافتراضية
        if 'status' not in student_data:
            student_data['status'] = 'active'

        return self.insert(student_data)
    
    def update_student(self, student_id, student_data):
        """تحديث بيانات طالب"""
        # التحقق من وجود الطالب
        existing_student = self.get_by_id(student_id)
        if not existing_student:
            raise ValueError("الطالب غير موجود")
        
        # التحقق من تفرد رقم الطالب
        if 'student_number' in student_data:
            self.validate_unique_field('student_number', student_data['student_number'], student_id)
        
        # التحقق من تفرد رقم الهوية
        if 'national_id' in student_data and student_data['national_id']:
            self.validate_unique_field('national_id', student_data['national_id'], student_id)
        
        # التحقق من صحة التاريخ
        if 'date_of_birth' in student_data:
            self.validate_date_of_birth(student_data['date_of_birth'])
        
        return self.update(student_id, student_data)
    
    def get_students_by_class(self, class_id):
        """جلب الطلاب حسب الصف"""
        return self.get_all("class_id = ? AND status = 'active'", (class_id,))
    
    def get_active_students(self):
        """جلب الطلاب النشطين فقط"""
        return self.get_all("status = 'active'")
    
    def search_students(self, search_term):
        """البحث في الطلاب"""
        return self.search(search_term, self.search_fields)
    
    def get_student_by_number(self, student_number):
        """جلب طالب برقم الطالب"""
        return self.db_manager.fetch_one(
            "SELECT * FROM students WHERE student_number = ?",
            (student_number,)
        )
    
    def get_student_by_national_id(self, national_id):
        """جلب طالب برقم الهوية"""
        return self.db_manager.fetch_one(
            "SELECT * FROM students WHERE national_id = ?",
            (national_id,)
        )
    
    def get_students_with_class_info(self):
        """جلب الطلاب مع معلومات الصف"""
        query = """
        SELECT s.*, c.class_name, c.grade_level
        FROM students s
        LEFT JOIN classes c ON s.class_id = c.class_id
        WHERE s.status = 'active'
        ORDER BY s.first_name, s.last_name
        """
        return self.db_manager.fetch_all(query)
    
    def transfer_student_to_class(self, student_id, new_class_id):
        """نقل طالب إلى صف آخر"""
        return self.update_student(student_id, {'class_id': new_class_id})
    
    def deactivate_student(self, student_id, reason=""):
        """إلغاء تفعيل طالب"""
        data = {
            'status': 'inactive',
            'notes': f"تم إلغاء التفعيل: {reason}" if reason else "تم إلغاء التفعيل"
        }
        return self.update_student(student_id, data)
    
    def reactivate_student(self, student_id):
        """إعادة تفعيل طالب"""
        return self.update_student(student_id, {'status': 'active'})
    
    def get_student_statistics(self):
        """إحصائيات الطلاب"""
        stats = {}
        
        # إجمالي الطلاب النشطين
        stats['total_active'] = self.count("status = 'active'")
        
        # إجمالي الطلاب غير النشطين
        stats['total_inactive'] = self.count("status = 'inactive'")
        
        # الطلاب حسب الجنس
        stats['male_students'] = self.count("gender = 'male' AND status = 'active'")
        stats['female_students'] = self.count("gender = 'female' AND status = 'active'")
        
        # الطلاب بدون صف
        stats['students_without_class'] = self.count("class_id IS NULL AND status = 'active'")
        
        return stats
    
    def validate_date_of_birth(self, date_of_birth):
        """التحقق من صحة تاريخ الميلاد"""
        if isinstance(date_of_birth, str):
            try:
                birth_date = datetime.strptime(date_of_birth, '%Y-%m-%d').date()
            except ValueError:
                raise ValueError("تنسيق تاريخ الميلاد غير صحيح. يجب أن يكون YYYY-MM-DD")
        else:
            birth_date = date_of_birth
        
        # التحقق من أن التاريخ ليس في المستقبل
        if birth_date > date.today():
            raise ValueError("تاريخ الميلاد لا يمكن أن يكون في المستقبل")
        
        # التحقق من أن العمر معقول (بين 3 و 25 سنة)
        age = (date.today() - birth_date).days // 365
        if age < 3 or age > 25:
            raise ValueError("عمر الطالب يجب أن يكون بين 3 و 25 سنة")
        
        return True

    def get_all_students(self):
        """الحصول على جميع الطلاب"""
        return self.get_all()

    def get_students_statistics(self):
        """الحصول على إحصائيات الطلاب"""
        try:
            stats = {}

            # إجمالي الطلاب
            stats['total_students'] = self.count()

            # الطلاب النشطون
            stats['active_students'] = self.count("status = 'active'")

            # الطلاب غير النشطين
            stats['inactive_students'] = self.count("status = 'inactive'")

            # الطلاب حسب الجنس
            male_count = self.count("gender = 'ذكر'")
            female_count = self.count("gender = 'أنثى'")
            stats['by_gender'] = {
                'male': male_count,
                'female': female_count
            }

            # الطلاب حسب الصف
            classes_query = """
            SELECT class_id, COUNT(*) as count
            FROM students
            WHERE class_id IS NOT NULL AND status = 'active'
            GROUP BY class_id
            """
            classes_result = self.db_manager.fetch_all(classes_query)
            stats['by_class'] = {row['class_id']: row['count'] for row in classes_result}

            # متوسط العمر
            age_query = """
            SELECT AVG(
                (julianday('now') - julianday(date_of_birth)) / 365.25
            ) as avg_age
            FROM students
            WHERE status = 'active' AND date_of_birth IS NOT NULL
            """
            age_result = self.db_manager.fetch_one(age_query)
            stats['average_age'] = round(age_result['avg_age'], 1) if age_result and age_result['avg_age'] else 0

            return stats
        except Exception as e:
            raise Exception(f"خطأ في جلب إحصائيات الطلاب: {str(e)}")

    def search_students(self, search_term):
        """البحث في الطلاب"""
        try:
            where_clause = """
            student_number LIKE ? OR first_name LIKE ? OR last_name LIKE ?
            OR national_id LIKE ? OR parent_name LIKE ? OR parent_phone LIKE ?
            """
            search_param = f"%{search_term}%"
            params = (search_param, search_param, search_param, search_param, search_param, search_param)
            return self.get_all(where_clause, params)
        except Exception as e:
            raise Exception(f"خطأ في البحث: {str(e)}")

    def get_students_by_class(self, class_id):
        """الحصول على طلاب صف معين"""
        try:
            return self.get_all("class_id = ? AND status = 'active'", (class_id,))
        except Exception as e:
            raise Exception(f"خطأ في جلب طلاب الصف: {str(e)}")

    def get_students_by_gender(self, gender):
        """الحصول على الطلاب حسب الجنس"""
        try:
            return self.get_all("gender = ? AND status = 'active'", (gender,))
        except Exception as e:
            raise Exception(f"خطأ في جلب الطلاب حسب الجنس: {str(e)}")

    def activate_student(self, student_id):
        """تفعيل طالب"""
        try:
            return self.update(student_id, {'status': 'active'})
        except Exception as e:
            raise Exception(f"خطأ في تفعيل الطالب: {str(e)}")

    def deactivate_student(self, student_id):
        """إلغاء تفعيل طالب"""
        try:
            return self.update(student_id, {'status': 'inactive'})
        except Exception as e:
            raise Exception(f"خطأ في إلغاء تفعيل الطالب: {str(e)}")

    def get_student_by_number(self, student_number):
        """الحصول على طالب برقم الطالب"""
        try:
            return self.get_all("student_number = ?", (student_number,))
        except Exception as e:
            raise Exception(f"خطأ في جلب الطالب: {str(e)}")

    def update_student_class(self, student_id, class_id):
        """تحديث صف الطالب"""
        try:
            return self.update(student_id, {'class_id': class_id})
        except Exception as e:
            raise Exception(f"خطأ في تحديث صف الطالب: {str(e)}")

    def get_students_without_class(self):
        """الحصول على الطلاب غير المسجلين في صف"""
        try:
            return self.get_all("class_id IS NULL AND status = 'active'")
        except Exception as e:
            raise Exception(f"خطأ في جلب الطلاب غير المسجلين: {str(e)}")

    def get_active_students(self):
        """الحصول على الطلاب النشطين"""
        try:
            return self.get_all("status = 'active'")
        except Exception as e:
            raise Exception(f"خطأ في جلب الطلاب النشطين: {str(e)}")
    
    def get_students_by_age_range(self, min_age, max_age):
        """جلب الطلاب حسب الفئة العمرية"""
        today = date.today()
        max_birth_date = date(today.year - min_age, today.month, today.day)
        min_birth_date = date(today.year - max_age, today.month, today.day)
        
        query = """
        SELECT * FROM students 
        WHERE date_of_birth BETWEEN ? AND ? 
        AND status = 'active'
        ORDER BY date_of_birth DESC
        """
        return self.db_manager.fetch_all(query, (min_birth_date, max_birth_date))
