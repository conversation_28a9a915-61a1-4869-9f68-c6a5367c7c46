#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج إدارة المدارس - School Management Pro
الملف الرئيسي لتشغيل التطبيق

المطور: Augment Agent
التاريخ: 2025
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QSplashScreen, QDialog
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap, QFont

# إضافة مسار المشروع إلى sys.path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.ui.main_window import MainWindow
from src.ui.login_window import LoginWindow
from src.database.db_manager import DatabaseManager
from src.utils.config import Config
from src.utils.font_manager import FontManager
from src.utils.icon_manager import IconManager


class SchoolManagementApp:
    """الفئة الرئيسية لتطبيق إدارة المدارس"""
    
    def __init__(self):
        self.app = None
        self.main_window = None
        self.login_window = None
        self.db_manager = None
        self.current_user = None
        
    def setup_application(self):
        """إعداد التطبيق الأساسي"""
        self.app = QApplication(sys.argv)
        
        # إعداد اللغة العربية والاتجاه من اليمين إلى اليسار
        self.app.setLayoutDirection(Qt.RightToLeft)
        
        # إعداد الخطوط باستخدام مدير الخطوط
        font_manager = FontManager()
        font_manager.setup_application_font(self.app)

        # إعداد الأيقونات
        icon_manager = IconManager()
        icon_manager.set_application_icon(self.app)
        
        # إعداد معلومات التطبيق
        self.app.setApplicationName("برنامج إدارة المدارس")
        self.app.setApplicationVersion("1.0.0")
        self.app.setOrganizationName("School Management Pro")
        
    def initialize_database(self):
        """تهيئة قاعدة البيانات"""
        try:
            self.db_manager = DatabaseManager()
            self.db_manager.initialize_database()

            # تحديث هيكل قاعدة البيانات
            self.db_manager.update_database_schema()

            print("تم تهيئة قاعدة البيانات بنجاح")
            return True
        except Exception as e:
            print(f"خطأ في تهيئة قاعدة البيانات: {e}")
            return False
            
    def show_splash_screen(self):
        """عرض شاشة البداية"""
        # إنشاء شاشة بداية بسيطة
        splash = QSplashScreen()
        splash.setFixedSize(400, 300)
        splash.setStyleSheet("""
            QSplashScreen {
                background-color: #2c3e50;
                color: white;
                font-size: 18px;
                font-weight: bold;
            }
        """)

        # عرض النص
        splash.showMessage("جاري تحميل برنامج إدارة المدارس...",
                          Qt.AlignCenter | Qt.AlignBottom, Qt.white)
        splash.show()

        # معالجة الأحداث لعرض الشاشة
        self.app.processEvents()

        # انتظار لثانيتين ثم عرض نافذة تسجيل الدخول
        QTimer.singleShot(2000, splash.close)
        QTimer.singleShot(2000, self.show_login_window)

        return splash

    def show_login_window(self):
        """عرض نافذة تسجيل الدخول"""
        try:
            self.login_window = LoginWindow()

            # ربط إشارة نجاح تسجيل الدخول
            self.login_window.login_successful.connect(self.on_login_successful)

            # عرض بيانات الدخول الافتراضية للاختبار
            self.login_window.show_default_credentials()

            # عرض النافذة
            if self.login_window.exec_() != QDialog.Accepted:
                # إذا تم إلغاء تسجيل الدخول، إغلاق التطبيق
                self.app.quit()

        except Exception as e:
            print(f"خطأ في عرض نافذة تسجيل الدخول: {e}")

    def on_login_successful(self, user):
        """عند نجاح تسجيل الدخول"""
        self.current_user = user
        self.show_main_window()
        
    def show_main_window(self):
        """عرض النافذة الرئيسية"""
        try:
            self.main_window = MainWindow()

            # تعيين المستخدم الحالي
            if self.current_user:
                self.main_window.set_current_user(self.current_user)

            # ربط إشارة تسجيل الخروج
            self.main_window.user_logged_out.connect(self.on_logout)

            self.main_window.show()
        except Exception as e:
            import traceback
            print(f"خطأ في عرض النافذة الرئيسية: {e}")
            print("تفاصيل الخطأ:")
            traceback.print_exc()

    def on_logout(self):
        """عند تسجيل الخروج"""
        self.current_user = None
        if self.main_window:
            self.main_window.close()
            self.main_window = None
        self.show_login_window()
            
    def run(self):
        """تشغيل التطبيق"""
        try:
            # إعداد التطبيق
            self.setup_application()
            
            # تهيئة قاعدة البيانات
            if not self.initialize_database():
                return 1
                
            # عرض شاشة البداية
            splash = self.show_splash_screen()
            
            # تشغيل التطبيق
            return self.app.exec_()
            
        except Exception as e:
            print(f"خطأ في تشغيل التطبيق: {e}")
            return 1


def main():
    """الدالة الرئيسية"""
    app = SchoolManagementApp()
    sys.exit(app.run())


if __name__ == "__main__":
    main()
