#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إضافة وتعديل الرسوم الدراسية
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QLineEdit, QPushButton, QLabel, QFrame, QMessageBox,
                             QComboBox, QDateEdit, QTextEdit, QDoubleSpinBox)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont

from src.models.student import Student
from src.models.currency import CurrencyModel


class FeeDialog(QDialog):
    """نافذة إضافة وتعديل الرسوم الدراسية"""
    
    # إشارة حفظ البيانات
    fee_saved = pyqtSignal()
    
    def __init__(self, fee_id=None, parent=None):
        super().__init__(parent)
        self.fee_id = fee_id
        self.student_model = Student()
        self.currency_model = CurrencyModel(self.student_model.db_manager)
        self.default_currency = None
        self.is_edit_mode = fee_id is not None

        self.load_default_currency()
        self.setup_ui()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.load_fee_data()
        else:
            self.load_students()

    def load_default_currency(self):
        """تحميل العملة الافتراضية"""
        try:
            self.default_currency = self.currency_model.get_base_currency()
            if not self.default_currency:
                # إذا لم توجد عملة افتراضية، استخدم الريال السعودي
                self.default_currency = {
                    'symbol': 'ر.س',
                    'currency_name': 'الريال السعودي'
                }
        except Exception as e:
            print(f"خطأ في تحميل العملة الافتراضية: {e}")
            self.default_currency = {
                'symbol': 'ر.س',
                'currency_name': 'الريال السعودي'
            }

    def get_currency_symbol(self):
        """الحصول على رمز العملة الافتراضية"""
        if self.default_currency:
            return self.default_currency.get('symbol', 'ر.س')
        return 'ر.س'

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة
        title = "تعديل رسوم" if self.is_edit_mode else "إضافة رسوم جديدة"
        self.setWindowTitle(title)
        self.setFixedSize(500, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # عنوان النافذة
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # نموذج البيانات
        form_frame = QFrame()
        form_frame.setFrameStyle(QFrame.StyledPanel)
        form_layout = QFormLayout(form_frame)
        form_layout.setSpacing(15)
        
        # اختيار الطالب
        self.student_combo = QComboBox()
        self.student_combo.setEnabled(not self.is_edit_mode)
        form_layout.addRow("الطالب *:", self.student_combo)
        
        # نوع الرسوم
        self.fee_type_combo = QComboBox()
        fee_types = [
            "رسوم دراسية", "رسوم تسجيل", "رسوم كتب", "رسوم نشاطات",
            "رسوم مختبر", "رسوم مكتبة", "رسوم نقل", "رسوم أخرى"
        ]
        self.fee_type_combo.addItems(fee_types)
        form_layout.addRow("نوع الرسوم *:", self.fee_type_combo)
        
        # المبلغ
        self.amount_input = QDoubleSpinBox()
        self.amount_input.setRange(0, 999999)
        currency_symbol = self.get_currency_symbol()
        self.amount_input.setSuffix(f" {currency_symbol}")
        self.amount_input.setValue(1000)
        form_layout.addRow("المبلغ *:", self.amount_input)
        
        # تاريخ الاستحقاق
        self.due_date_input = QDateEdit()
        self.due_date_input.setDate(QDate.currentDate().addDays(30))
        self.due_date_input.setCalendarPopup(True)
        form_layout.addRow("تاريخ الاستحقاق*:", self.due_date_input)
        
        # الخصم
        self.discount_input = QDoubleSpinBox()
        self.discount_input.setRange(0, 999999)
        self.discount_input.setSuffix(f" {currency_symbol}")
        self.discount_input.setValue(0)
        form_layout.addRow("الخصم:", self.discount_input)
        
        # السنة الدراسية
        self.academic_year_combo = QComboBox()
        self.academic_year_combo.addItems(["2024-2025", "2025-2026", "2026-2027"])
        form_layout.addRow("السنة الدراسية:", self.academic_year_combo)
        
        # الفصل الدراسي
        self.semester_combo = QComboBox()
        self.semester_combo.addItems(["الفصل الأول", "الفصل الثاني", "الفصل الصيفي"])
        form_layout.addRow("الفصل الدراسي:", self.semester_combo)
        
        # الحالة
        self.status_combo = QComboBox()
        self.status_combo.addItems(["معلق", "مدفوع", "متأخر"])
        form_layout.addRow("الحالة:", self.status_combo)
        
        # المبلغ النهائي (محسوب تلقائياً)
        self.final_amount_label = QLabel(f"0.00 {currency_symbol}")
        self.final_amount_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #27ae60;
                padding: 5px;
                background-color: #ecf0f1;
                border-radius: 3px;
            }
        """)
        form_layout.addRow("المبلغ النهائي:", self.final_amount_label)
        
        # ملاحظات
        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText("أدخل أي ملاحظات إضافية")
        self.notes_input.setMaximumHeight(80)
        form_layout.addRow("ملاحظات:", self.notes_input)
        
        main_layout.addWidget(form_frame)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        self.save_button = QPushButton("حفظ")
        self.save_button.setFixedHeight(40)
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setFixedHeight(40)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.save_button)
        
        main_layout.addLayout(buttons_layout)
        
    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        self.save_button.clicked.connect(self.save_fee)
        self.cancel_button.clicked.connect(self.reject)
        
        # ربط حساب المبلغ النهائي
        self.amount_input.valueChanged.connect(self.calculate_final_amount)
        self.discount_input.valueChanged.connect(self.calculate_final_amount)
        
    def load_students(self):
        """تحميل قائمة الطلاب"""
        try:
            students = self.student_model.get_active_students()
            self.student_combo.clear()
            
            for student in students:
                name = f"{student['first_name']} {student['last_name']} - {student['student_number']}"
                self.student_combo.addItem(name, student['student_id'])
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل الطلاب: {str(e)}")
            
    def calculate_final_amount(self):
        """حساب المبلغ النهائي"""
        amount = self.amount_input.value()
        discount = self.discount_input.value()

        final_amount = amount - discount
        currency_symbol = self.get_currency_symbol()
        self.final_amount_label.setText(f"{final_amount:,.2f} {currency_symbol}")
        
    def load_fee_data(self):
        """تحميل بيانات الرسوم للتعديل"""
        try:
            # استعلام بيانات الرسوم
            query = """
            SELECT f.*, s.first_name, s.last_name, s.student_number
            FROM fees f
            JOIN students s ON f.student_id = s.student_id
            WHERE f.fee_id = ?
            """
            fee = self.student_model.db_manager.fetch_one(query, (self.fee_id,))
            
            if not fee:
                QMessageBox.critical(self, "خطأ", "لم يتم العثور على الرسوم")
                self.reject()
                return
                
            # ملء الحقول بالبيانات
            student_name = f"{fee['first_name']} {fee['last_name']} - {fee['student_number']}"
            self.student_combo.addItem(student_name, fee['student_id'])
            self.student_combo.setCurrentIndex(0)
            
            # نوع الرسوم
            fee_type_index = self.fee_type_combo.findText(fee['fee_type'])
            if fee_type_index >= 0:
                self.fee_type_combo.setCurrentIndex(fee_type_index)
                
            self.amount_input.setValue(fee['amount'])
            
            # تاريخ الاستحقاق
            if fee['due_date']:
                date = QDate.fromString(str(fee['due_date']), "yyyy-MM-dd")
                self.due_date_input.setDate(date)
                
            self.discount_input.setValue(fee['discount'] or 0)
            
            # السنة الدراسية
            if fee['academic_year']:
                year_index = self.academic_year_combo.findText(fee['academic_year'])
                if year_index >= 0:
                    self.academic_year_combo.setCurrentIndex(year_index)
                    
            # الفصل الدراسي
            if fee['semester']:
                semester_index = self.semester_combo.findText(fee['semester'])
                if semester_index >= 0:
                    self.semester_combo.setCurrentIndex(semester_index)
                    
            # الحالة
            status_map = {"pending": "معلق", "paid": "مدفوع", "overdue": "متأخر"}
            status_text = status_map.get(fee['status'], "معلق")
            status_index = self.status_combo.findText(status_text)
            if status_index >= 0:
                self.status_combo.setCurrentIndex(status_index)
                
            self.notes_input.setPlainText(str(fee['notes'] or ''))
            
            # حساب المبلغ النهائي
            self.calculate_final_amount()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل بيانات الرسوم: {str(e)}")
            
    def save_fee(self):
        """حفظ بيانات الرسوم"""
        try:
            # التحقق من اختيار الطالب
            if self.student_combo.currentIndex() < 0:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار الطالب")
                return
                
            # جمع البيانات من النموذج
            student_id = self.student_combo.currentData()
            fee_type = self.fee_type_combo.currentText()
            amount = self.amount_input.value()
            due_date = self.due_date_input.date().toString("yyyy-MM-dd")
            discount = self.discount_input.value()
            academic_year = self.academic_year_combo.currentText()
            semester = self.semester_combo.currentText()
            
            status_map = {"معلق": "pending", "مدفوع": "paid", "متأخر": "overdue"}
            status = status_map.get(self.status_combo.currentText(), "pending")
            
            notes = self.notes_input.toPlainText().strip() or None
            
            # حفظ البيانات
            if self.is_edit_mode:
                # تحديث الرسوم
                query = """
                UPDATE fees SET 
                fee_type = ?, amount = ?, due_date = ?, discount = ?,
                academic_year = ?, semester = ?, status = ?, notes = ?
                WHERE fee_id = ?
                """
                params = (
                    fee_type, amount, due_date, discount,
                    academic_year, semester, status, notes, self.fee_id
                )
                self.student_model.db_manager.execute_query(query, params)
                QMessageBox.information(self, "نجح", "تم تحديث الرسوم بنجاح")
            else:
                # إضافة رسوم جديدة
                query = """
                INSERT INTO fees (student_id, fee_type, amount, due_date, discount,
                academic_year, semester, status, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                params = (
                    student_id, fee_type, amount, due_date, discount,
                    academic_year, semester, status, notes
                )
                self.student_model.db_manager.execute_query(query, params)
                QMessageBox.information(self, "نجح", "تم إضافة الرسوم بنجاح")
            
            # إرسال إشارة الحفظ
            self.fee_saved.emit()
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ البيانات: {str(e)}")
