#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ويدجت إدارة المستخدمين والصلاحيات
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                             QMessageBox, QHeaderView, QFrame, QComboBox,
                             QCheckBox, QGroupBox, QSplitter)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QIcon

from src.models.user import User
from src.ui.dialogs.user_dialog import UserDialog
from src.ui.dialogs.change_password_dialog import ChangePasswordDialog
from src.ui.dialogs.permissions_dialog import PermissionsDialog
from src.utils.config import Config


class UsersWidget(QWidget):
    """ويدجت إدارة المستخدمين والصلاحيات"""
    
    def __init__(self):
        super().__init__()
        self.user_model = User()
        self.current_user_id = None  # سيتم تعيينه من النافذة الرئيسية
        self.setup_ui()
        self.load_users()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # عنوان الصفحة
        title_label = QLabel("إدارة المستخدمين والصلاحيات")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # شريط البحث والأزرار
        top_frame = QFrame()
        top_layout = QHBoxLayout(top_frame)
        
        # حقل البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("البحث عن مستخدم...")
        self.search_input.setFixedHeight(35)
        self.search_input.textChanged.connect(self.search_users)
        
        # فلتر الدور
        top_layout.addWidget(QLabel("الدور:"))
        self.role_filter_combo = QComboBox()
        self.role_filter_combo.addItem("جميع الأدوار", "")
        for role_key, role_name in Config.USER_ROLES.items():
            self.role_filter_combo.addItem(role_name, role_key)
        self.role_filter_combo.currentTextChanged.connect(self.filter_users)
        
        # فلتر الحالة
        top_layout.addWidget(QLabel("الحالة:"))
        self.status_filter_combo = QComboBox()
        self.status_filter_combo.addItems(["الكل", "نشط", "غير نشط"])
        self.status_filter_combo.currentTextChanged.connect(self.filter_users)
        
        # أزرار الإجراءات
        self.add_user_button = QPushButton("إضافة مستخدم")
        self.edit_user_button = QPushButton("تعديل")
        self.change_password_button = QPushButton("تغيير كلمة المرور")
        self.permissions_button = QPushButton("الصلاحيات")
        self.toggle_status_button = QPushButton("تفعيل/إلغاء تفعيل")
        self.delete_user_button = QPushButton("حذف")
        self.refresh_button = QPushButton("تحديث")
        
        # تطبيق الأنماط على الأزرار
        button_style = """
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """
        
        for button in [self.add_user_button, self.edit_user_button, 
                      self.change_password_button, self.permissions_button,
                      self.toggle_status_button, self.refresh_button]:
            button.setStyleSheet(button_style)
            button.setFixedHeight(35)
        
        # تخصيص لون زر الحذف
        self.delete_user_button.setStyleSheet(
            button_style.replace("#3498db", "#e74c3c").replace("#2980b9", "#c0392b")
        )
        self.delete_user_button.setFixedHeight(35)
        
        top_layout.addWidget(QLabel("البحث:"))
        top_layout.addWidget(self.search_input)
        top_layout.addWidget(self.role_filter_combo)
        top_layout.addWidget(self.status_filter_combo)
        top_layout.addStretch()
        top_layout.addWidget(self.add_user_button)
        top_layout.addWidget(self.edit_user_button)
        top_layout.addWidget(self.change_password_button)
        top_layout.addWidget(self.permissions_button)
        top_layout.addWidget(self.toggle_status_button)
        top_layout.addWidget(self.delete_user_button)
        top_layout.addWidget(self.refresh_button)
        
        layout.addWidget(top_frame)
        
        # تقسيم الشاشة
        splitter = QSplitter(Qt.Horizontal)
        
        # جدول المستخدمين
        self.users_table = QTableWidget()
        self.setup_users_table()
        splitter.addWidget(self.users_table)
        
        # لوحة تفاصيل المستخدم
        self.details_panel = self.create_details_panel()
        splitter.addWidget(self.details_panel)
        
        # تعيين نسب التقسيم
        splitter.setSizes([700, 300])
        
        layout.addWidget(splitter)
        
        # ربط الأحداث
        self.setup_connections()
        
    def setup_users_table(self):
        """إعداد جدول المستخدمين"""
        columns = [
            "اسم المستخدم", "الاسم الكامل", "البريد الإلكتروني", 
            "الدور", "الحالة", "آخر تسجيل دخول", "تاريخ الإنشاء"
        ]
        
        self.users_table.setColumnCount(len(columns))
        self.users_table.setHorizontalHeaderLabels(columns)
        
        # إعداد خصائص الجدول
        self.users_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.users_table.setAlternatingRowColors(True)
        self.users_table.setSortingEnabled(True)
        
        # تخصيص عرض الأعمدة
        header = self.users_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        # تطبيق الأنماط
        self.users_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)
        
    def create_details_panel(self):
        """إنشاء لوحة تفاصيل المستخدم"""
        details_frame = QFrame()
        details_frame.setFrameStyle(QFrame.StyledPanel)
        details_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
        """)
        
        layout = QVBoxLayout(details_frame)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # عنوان اللوحة
        title_label = QLabel("تفاصيل المستخدم")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
                border-bottom: 2px solid #3498db;
            }
        """)
        layout.addWidget(title_label)
        
        # تفاصيل المستخدم
        self.user_details_label = QLabel("اختر مستخدماً لعرض التفاصيل")
        self.user_details_label.setWordWrap(True)
        self.user_details_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-size: 12px;
                padding: 10px;
            }
        """)
        layout.addWidget(self.user_details_label)
        
        # إحصائيات المستخدمين
        stats_group = QGroupBox("إحصائيات")
        stats_layout = QVBoxLayout(stats_group)
        
        self.stats_label = QLabel()
        self.stats_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                font-size: 11px;
                padding: 5px;
            }
        """)
        stats_layout.addWidget(self.stats_label)
        
        layout.addWidget(stats_group)
        layout.addStretch()
        
        return details_frame
        
    def setup_connections(self):
        """ربط الأحداث"""
        self.add_user_button.clicked.connect(self.add_user)
        self.edit_user_button.clicked.connect(self.edit_user)
        self.change_password_button.clicked.connect(self.change_password)
        self.permissions_button.clicked.connect(self.manage_permissions)
        self.toggle_status_button.clicked.connect(self.toggle_user_status)
        self.delete_user_button.clicked.connect(self.delete_user)
        self.refresh_button.clicked.connect(self.load_users)
        
        # ربط تحديد الصف
        self.users_table.itemSelectionChanged.connect(self.on_user_selected)
        
        # تحديث الإحصائيات كل 30 ثانية
        self.stats_timer = QTimer()
        self.stats_timer.timeout.connect(self.update_statistics)
        self.stats_timer.start(30000)  # 30 ثانية
        
    def load_users(self):
        """تحميل قائمة المستخدمين"""
        try:
            users = self.user_model.get_all()
            self.populate_users_table(users)
            self.update_statistics()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل المستخدمين: {str(e)}")
            
    def populate_users_table(self, users):
        """ملء جدول المستخدمين بالبيانات"""
        self.users_table.setRowCount(len(users))
        
        for row, user in enumerate(users):
            try:
                # اسم المستخدم
                self.users_table.setItem(row, 0, QTableWidgetItem(str(user['username'])))
                
                # الاسم الكامل
                full_name = f"{user['first_name']} {user['last_name']}"
                self.users_table.setItem(row, 1, QTableWidgetItem(full_name))
                
                # البريد الإلكتروني
                email = user['email'] if user['email'] else "غير محدد"
                self.users_table.setItem(row, 2, QTableWidgetItem(email))
                
                # الدور
                role_name = Config.USER_ROLES.get(user['role'], user['role'])
                self.users_table.setItem(row, 3, QTableWidgetItem(role_name))
                
                # الحالة
                status = "نشط" if user['is_active'] else "غير نشط"
                status_item = QTableWidgetItem(status)
                if user['is_active']:
                    status_item.setBackground(Qt.green)
                else:
                    status_item.setBackground(Qt.red)
                self.users_table.setItem(row, 4, status_item)
                
                # آخر تسجيل دخول
                last_login = user['last_login'] if user['last_login'] else "لم يسجل دخول"
                self.users_table.setItem(row, 5, QTableWidgetItem(str(last_login)))
                
                # تاريخ الإنشاء
                created_at = user['created_at'] if user['created_at'] else "غير محدد"
                self.users_table.setItem(row, 6, QTableWidgetItem(str(created_at)))
                
                # حفظ معرف المستخدم في البيانات المخفية
                self.users_table.item(row, 0).setData(Qt.UserRole, user.get('user_id'))

            except Exception as e:
                print(f"خطأ في إضافة المستخدم رقم {row}: {e}")
                
    def search_users(self):
        """البحث في المستخدمين"""
        search_text = self.search_input.text().strip()

        if not search_text:
            self.load_users()
            return

        try:
            # استخدام البحث في النموذج الأساسي
            where_clause = "username LIKE ? OR first_name LIKE ? OR last_name LIKE ? OR email LIKE ?"
            search_param = f"%{search_text}%"
            users = self.user_model.get_all(where_clause, (search_param, search_param, search_param, search_param))
            self.populate_users_table(users)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في البحث: {str(e)}")
            
    def filter_users(self):
        """فلترة المستخدمين"""
        try:
            role_filter = self.role_filter_combo.currentData()
            status_filter = self.status_filter_combo.currentText()
            
            # بناء الاستعلام
            conditions = []
            params = []
            
            if role_filter:
                conditions.append("role = ?")
                params.append(role_filter)
                
            if status_filter == "نشط":
                conditions.append("is_active = 1")
            elif status_filter == "غير نشط":
                conditions.append("is_active = 0")
            
            where_clause = " AND ".join(conditions) if conditions else ""
            users = self.user_model.get_all(where_clause, params)
            self.populate_users_table(users)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في الفلترة: {str(e)}")

    def on_user_selected(self):
        """عند تحديد مستخدم في الجدول"""
        current_row = self.users_table.currentRow()
        if current_row >= 0:
            user_id = self.users_table.item(current_row, 0).data(Qt.UserRole)
            self.show_user_details(user_id)

    def show_user_details(self, user_id):
        """عرض تفاصيل المستخدم"""
        try:
            user = self.user_model.get_by_id(user_id)
            if user:
                details = f"""
                <b>اسم المستخدم:</b> {user['username']}<br>
                <b>الاسم الكامل:</b> {user['first_name']} {user['last_name']}<br>
                <b>البريد الإلكتروني:</b> {user['email'] or 'غير محدد'}<br>
                <b>الدور:</b> {Config.USER_ROLES.get(user['role'], user['role'])}<br>
                <b>الحالة:</b> {'نشط' if user['is_active'] else 'غير نشط'}<br>
                <b>تاريخ الإنشاء:</b> {user['created_at'] or 'غير محدد'}<br>
                <b>آخر تسجيل دخول:</b> {user['last_login'] or 'لم يسجل دخول'}<br>
                <b>الصلاحيات:</b> {len(self.user_model.get_user_permissions(user_id))} صلاحية
                """
                self.user_details_label.setText(details)
            else:
                self.user_details_label.setText("لم يتم العثور على المستخدم")
        except Exception as e:
            self.user_details_label.setText(f"خطأ في تحميل التفاصيل: {str(e)}")

    def update_statistics(self):
        """تحديث إحصائيات المستخدمين"""
        try:
            stats = self.user_model.get_user_statistics()

            stats_text = f"""
            <b>المستخدمون النشطون:</b> {stats.get('total_active', 0)}<br>
            <b>المستخدمون غير النشطين:</b> {stats.get('total_inactive', 0)}<br>
            <br><b>حسب الدور:</b><br>
            """

            for role, count in stats.get('by_role', {}).items():
                role_name = Config.USER_ROLES.get(role, role)
                stats_text += f"• {role_name}: {count}<br>"

            self.stats_label.setText(stats_text)

        except Exception as e:
            self.stats_label.setText(f"خطأ في تحميل الإحصائيات: {str(e)}")

    def add_user(self):
        """إضافة مستخدم جديد"""
        dialog = UserDialog(parent=self)
        dialog.user_saved.connect(self.load_users)
        dialog.exec_()

    def edit_user(self):
        """تعديل مستخدم"""
        current_row = self.users_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مستخدم للتعديل")
            return

        user_id = self.users_table.item(current_row, 0).data(Qt.UserRole)

        # التحقق من الصلاحيات
        if not self.can_edit_user(user_id):
            QMessageBox.warning(self, "تحذير", "ليس لديك صلاحية لتعديل هذا المستخدم")
            return

        try:
            dialog = UserDialog(user_id=user_id, parent=self)
            dialog.user_saved.connect(self.load_users)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في فتح نافذة التعديل: {str(e)}")

    def change_password(self):
        """تغيير كلمة مرور المستخدم"""
        current_row = self.users_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مستخدم لتغيير كلمة مروره")
            return

        user_id = self.users_table.item(current_row, 0).data(Qt.UserRole)
        username = self.users_table.item(current_row, 0).text()

        # التحقق من الصلاحيات
        if not self.can_edit_user(user_id):
            QMessageBox.warning(self, "تحذير", "ليس لديك صلاحية لتغيير كلمة مرور هذا المستخدم")
            return

        dialog = ChangePasswordDialog(user_id=user_id, username=username, parent=self)
        dialog.exec_()

    def manage_permissions(self):
        """إدارة صلاحيات المستخدم"""
        current_row = self.users_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مستخدم لإدارة صلاحياته")
            return

        user_id = self.users_table.item(current_row, 0).data(Qt.UserRole)
        username = self.users_table.item(current_row, 0).text()

        # التحقق من الصلاحيات
        if not self.can_manage_permissions():
            QMessageBox.warning(self, "تحذير", "ليس لديك صلاحية لإدارة الصلاحيات")
            return

        dialog = PermissionsDialog(user_id=user_id, username=username, parent=self)
        dialog.permissions_updated.connect(self.load_users)
        dialog.exec_()

    def toggle_user_status(self):
        """تفعيل/إلغاء تفعيل المستخدم"""
        current_row = self.users_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مستخدم")
            return

        user_id = self.users_table.item(current_row, 0).data(Qt.UserRole)
        username = self.users_table.item(current_row, 0).text()
        current_status = self.users_table.item(current_row, 4).text()

        # التحقق من الصلاحيات
        if not self.can_edit_user(user_id):
            QMessageBox.warning(self, "تحذير", "ليس لديك صلاحية لتغيير حالة هذا المستخدم")
            return

        # منع المستخدم من إلغاء تفعيل نفسه
        if user_id == self.current_user_id:
            QMessageBox.warning(self, "تحذير", "لا يمكنك إلغاء تفعيل حسابك الخاص")
            return

        try:
            if current_status == "نشط":
                action = "إلغاء تفعيل"
                new_status = False
            else:
                action = "تفعيل"
                new_status = True

            reply = QMessageBox.question(
                self,
                f"تأكيد {action}",
                f"هل أنت متأكد من رغبتك في {action} المستخدم '{username}'؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                try:
                    if new_status:
                        self.user_model.update(user_id, {'is_active': True})
                    else:
                        self.user_model.update(user_id, {'is_active': False})

                    QMessageBox.information(self, "نجح", f"تم {action} المستخدم بنجاح")
                    self.load_users()
                except Exception as e:
                    QMessageBox.critical(self, "خطأ", f"حدث خطأ في {action} المستخدم: {str(e)}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في {action} المستخدم: {str(e)}")

    def delete_user(self):
        """حذف المستخدم"""
        current_row = self.users_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مستخدم للحذف")
            return

        user_id = self.users_table.item(current_row, 0).data(Qt.UserRole)
        username = self.users_table.item(current_row, 0).text()

        # التحقق من الصلاحيات
        if not self.can_delete_user():
            QMessageBox.warning(self, "تحذير", "ليس لديك صلاحية لحذف المستخدمين")
            return

        # منع المستخدم من حذف نفسه
        if user_id == self.current_user_id:
            QMessageBox.warning(self, "تحذير", "لا يمكنك حذف حسابك الخاص")
            return

        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل أنت متأكد من رغبتك في حذف المستخدم '{username}' نهائياً؟\n"
            "تحذير: هذا الإجراء لا يمكن التراجع عنه!",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                self.user_model.delete(user_id)
                QMessageBox.information(self, "نجح", "تم حذف المستخدم بنجاح")
                self.load_users()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ في حذف المستخدم: {str(e)}")

    def can_edit_user(self, user_id):
        """التحقق من صلاحية تعديل المستخدم"""
        if not self.current_user_id:
            return True  # في حالة عدم تعيين المستخدم الحالي

        # المدير العام يمكنه تعديل أي مستخدم
        if self.user_model.has_permission(self.current_user_id, 'manage_all_users'):
            return True

        # المستخدم يمكنه تعديل نفسه فقط
        return user_id == self.current_user_id

    def can_manage_permissions(self):
        """التحقق من صلاحية إدارة الصلاحيات"""
        if not self.current_user_id:
            return True

        return self.user_model.has_permission(self.current_user_id, 'manage_permissions')

    def can_delete_user(self):
        """التحقق من صلاحية حذف المستخدمين"""
        if not self.current_user_id:
            return True

        return self.user_model.has_permission(self.current_user_id, 'delete_users')

    def set_current_user(self, user_id):
        """تعيين المستخدم الحالي"""
        self.current_user_id = user_id
