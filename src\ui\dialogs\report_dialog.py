#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة عرض التقارير
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QTableWidgetItem, QPushButton, QLabel, QFrame,
                             QHeaderView, QMessageBox, QFileDialog, QTextEdit,
                             QSplitter, QGroupBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QPalette
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
import csv
import json
from datetime import datetime
from src.utils.dialog_utils import apply_dialog_styles, center_dialog_on_parent, ensure_dialog_visibility


class ReportDialog(QDialog):
    """نافذة عرض التقارير"""
    
    def __init__(self, report_data, parent=None):
        super().__init__(parent)
        self.report_data = report_data
        self.setup_ui()
        self.load_report_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(f"عرض التقرير - {self.report_data.get('title', 'تقرير')}")
        self.setModal(True)
        self.resize(1000, 700)

        # إعدادات النافذة لضمان الظهور في المقدمة
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint | Qt.WindowStaysOnTopHint)
        self.setAttribute(Qt.WA_DeleteOnClose)
        self.setLayoutDirection(Qt.RightToLeft)

        # توسيط النافذة
        center_dialog_on_parent(self, self.parent())
        
        # تطبيق الأنماط الموحدة
        apply_dialog_styles(self)

        # أنماط إضافية خاصة بهذه النافذة
        self.setStyleSheet(self.styleSheet() + """
            QDialog {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Tahoma, Arial;
            }
            QTableWidget {
                border: 1px solid #dee2e6;
                border-radius: 8px;
                background-color: white;
                gridline-color: #dee2e6;
                selection-background-color: #007bff;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #495057;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # عنوان التقرير
        title_label = QLabel(self.report_data.get('title', 'تقرير'))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #212529;
                padding: 15px;
                background-color: #e9ecef;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # معلومات التقرير
        info_frame = QFrame()
        info_layout = QHBoxLayout(info_frame)
        info_layout.setContentsMargins(0, 0, 0, 0)
        
        # تاريخ الإنشاء
        date_label = QLabel(f"تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
        date_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        
        # عدد السجلات
        record_count = len(self.report_data.get('data', []))
        count_label = QLabel(f"عدد السجلات: {record_count}")
        count_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        
        info_layout.addWidget(date_label)
        info_layout.addStretch()
        info_layout.addWidget(count_label)
        
        layout.addWidget(info_frame)
        
        # جدول البيانات
        self.report_table = QTableWidget()
        self.setup_report_table()
        layout.addWidget(self.report_table)
        
        # شريط الأدوات
        toolbar_frame = QFrame()
        toolbar_layout = QHBoxLayout(toolbar_frame)
        toolbar_layout.setContentsMargins(0, 10, 0, 0)
        
        # أزرار التصدير والطباعة
        self.export_csv_button = QPushButton("تصدير CSV")
        self.export_csv_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        
        self.export_json_button = QPushButton("تصدير JSON")
        self.export_json_button.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        
        self.print_button = QPushButton("طباعة")
        self.print_button.setStyleSheet("""
            QPushButton {
                background-color: #6f42c1;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
        """)
        
        self.close_button = QPushButton("إغلاق")
        self.close_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)
        
        toolbar_layout.addWidget(self.export_csv_button)
        toolbar_layout.addWidget(self.export_json_button)
        toolbar_layout.addWidget(self.print_button)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.close_button)
        
        layout.addWidget(toolbar_frame)
        
        # ربط الأحداث
        self.setup_connections()
        
    def setup_report_table(self):
        """إعداد جدول التقرير"""
        headers = self.report_data.get('headers', [])
        
        self.report_table.setColumnCount(len(headers))
        self.report_table.setHorizontalHeaderLabels(headers)
        
        # تنسيق الجدول
        header = self.report_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        header.setStyleSheet("""
            QHeaderView::section {
                background-color: #495057;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)
        
        self.report_table.setAlternatingRowColors(True)
        self.report_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.report_table.verticalHeader().setVisible(False)
        
    def load_report_data(self):
        """تحميل بيانات التقرير"""
        data = self.report_data.get('data', [])
        self.report_table.setRowCount(len(data))
        
        for row_index, row_data in enumerate(data):
            for col_index, cell_data in enumerate(row_data):
                item = QTableWidgetItem(str(cell_data))
                item.setFlags(item.flags() & ~Qt.ItemIsEditable)  # جعل الخلايا غير قابلة للتعديل
                self.report_table.setItem(row_index, col_index, item)
                
    def setup_connections(self):
        """ربط الأحداث"""
        self.export_csv_button.clicked.connect(self.export_to_csv)
        self.export_json_button.clicked.connect(self.export_to_json)
        self.print_button.clicked.connect(self.print_report)
        self.close_button.clicked.connect(self.accept)
        
    def export_to_csv(self):
        """تصدير التقرير إلى CSV"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ التقرير كـ CSV",
                f"{self.report_data.get('title', 'report')}_{datetime.now().strftime('%Y%m%d_%H%M')}.csv",
                "CSV Files (*.csv)"
            )
            
            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)
                    
                    # كتابة العناوين
                    headers = self.report_data.get('headers', [])
                    writer.writerow(headers)
                    
                    # كتابة البيانات
                    data = self.report_data.get('data', [])
                    for row in data:
                        writer.writerow(row)
                
                QMessageBox.information(self, "نجح", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تصدير التقرير: {str(e)}")
            
    def export_to_json(self):
        """تصدير التقرير إلى JSON"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ التقرير كـ JSON",
                f"{self.report_data.get('title', 'report')}_{datetime.now().strftime('%Y%m%d_%H%M')}.json",
                "JSON Files (*.json)"
            )
            
            if file_path:
                # إعداد البيانات للتصدير
                export_data = {
                    'title': self.report_data.get('title', ''),
                    'generated_at': datetime.now().isoformat(),
                    'headers': self.report_data.get('headers', []),
                    'data': self.report_data.get('data', []),
                    'record_count': len(self.report_data.get('data', []))
                }
                
                with open(file_path, 'w', encoding='utf-8') as jsonfile:
                    json.dump(export_data, jsonfile, ensure_ascii=False, indent=2)
                
                QMessageBox.information(self, "نجح", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تصدير التقرير: {str(e)}")
            
    def print_report(self):
        """طباعة التقرير"""
        try:
            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)
            printer.setOrientation(QPrinter.Landscape)
            
            print_dialog = QPrintDialog(printer, self)
            if print_dialog.exec_() == QPrintDialog.Accepted:
                # إنشاء محتوى HTML للطباعة
                html_content = self.generate_html_report()
                
                # طباعة المحتوى
                from PyQt5.QtGui import QTextDocument
                document = QTextDocument()
                document.setHtml(html_content)
                document.print_(printer)
                
                QMessageBox.information(self, "نجح", "تم إرسال التقرير للطباعة")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في طباعة التقرير: {str(e)}")
            
    def generate_html_report(self):
        """إنشاء تقرير HTML للطباعة"""
        html = f"""
        <html>
        <head>
            <meta charset="utf-8">
            <title>{self.report_data.get('title', 'تقرير')}</title>
            <style>
                body {{ font-family: Arial, sans-serif; direction: rtl; }}
                h1 {{ text-align: center; color: #333; }}
                table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
                th {{ background-color: #f2f2f2; font-weight: bold; }}
                tr:nth-child(even) {{ background-color: #f9f9f9; }}
                .info {{ text-align: center; color: #666; margin-bottom: 20px; }}
            </style>
        </head>
        <body>
            <h1>{self.report_data.get('title', 'تقرير')}</h1>
            <div class="info">
                تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M')} | 
                عدد السجلات: {len(self.report_data.get('data', []))}
            </div>
            <table>
                <thead>
                    <tr>
        """
        
        # إضافة العناوين
        for header in self.report_data.get('headers', []):
            html += f"<th>{header}</th>"
        
        html += """
                    </tr>
                </thead>
                <tbody>
        """
        
        # إضافة البيانات
        for row in self.report_data.get('data', []):
            html += "<tr>"
            for cell in row:
                html += f"<td>{cell}</td>"
            html += "</tr>"
        
        html += """
                </tbody>
            </table>
        </body>
        </html>
        """
        
        return html

    def showEvent(self, event):
        """عند إظهار النافذة"""
        super().showEvent(event)
        # ضمان الظهور في المقدمة
        ensure_dialog_visibility(self)

    def exec_(self):
        """تنفيذ النافذة مع ضمان الظهور الصحيح"""
        # ضمان الظهور في المقدمة
        ensure_dialog_visibility(self)
        return super().exec_()
