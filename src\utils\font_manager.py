#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الخطوط والنصوص
يوفر نظام موحد لإدارة الخطوط وأحجام النصوص في التطبيق
"""

import sys
import platform
from PyQt5.QtGui import QFont, QFontDatabase, QFontMetrics
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from src.utils.config import Config


class FontManager:
    """مدير الخطوط والنصوص"""
    
    _instance = None
    _fonts_loaded = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(FontManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        # لا نقوم بتحميل الخطوط في __init__ لتجنب مشاكل QApplication
        pass
    
    def load_system_fonts(self):
        """تحميل الخطوط المناسبة للنظام"""
        if self._fonts_loaded:
            return

        self.system = platform.system()
        
        # تحديد الخطوط المناسبة لكل نظام تشغيل
        if self.system == "Windows":
            self.arabic_fonts = ["Tahoma", "Arial Unicode MS", "Segoe UI", "Calibri"]
            self.english_fonts = ["Segoe UI", "Tahoma", "Arial", "Calibri"]
        elif self.system == "Darwin":  # macOS
            self.arabic_fonts = ["Al Bayan", "Baghdad", "Tahoma", "Arial Unicode MS"]
            self.english_fonts = ["SF Pro Display", "Helvetica Neue", "Arial"]
        else:  # Linux
            self.arabic_fonts = ["Noto Sans Arabic", "DejaVu Sans", "Liberation Sans", "Tahoma"]
            self.english_fonts = ["Ubuntu", "DejaVu Sans", "Liberation Sans", "Arial"]
        
        # العثور على أفضل خط متاح
        self.best_arabic_font = self.find_best_font(self.arabic_fonts)
        self.best_english_font = self.find_best_font(self.english_fonts)

        self._fonts_loaded = True
    
    def find_best_font(self, font_list):
        """العثور على أفضل خط متاح من القائمة"""
        font_db = QFontDatabase()
        available_fonts = font_db.families()
        
        for font in font_list:
            if font in available_fonts:
                return font
        
        # إذا لم يتم العثور على أي خط، استخدم الخط الافتراضي
        return font_list[0] if font_list else "Arial"
    
    def get_font(self, size=None, weight=QFont.Normal, family=None, arabic=True):
        """
        الحصول على خط مع المواصفات المحددة

        Args:
            size: حجم الخط (افتراضي من Config)
            weight: وزن الخط (عادي، عريض، إلخ)
            family: عائلة الخط (اختياري)
            arabic: هل النص عربي (لاختيار الخط المناسب)

        Returns:
            QFont: كائن الخط
        """
        # تحميل الخطوط إذا لم تكن محملة
        if not self._fonts_loaded:
            self.load_system_fonts()

        if size is None:
            size = Config.DEFAULT_FONT_SIZE

        if family is None:
            family = getattr(self, 'best_arabic_font', 'Tahoma') if arabic else getattr(self, 'best_english_font', 'Arial')
        
        font = QFont(family, size, weight)
        font.setStyleHint(QFont.SansSerif)
        
        # تحسينات للنصوص العربية
        if arabic:
            font.setHintingPreference(QFont.PreferFullHinting)
        
        return font
    
    def get_label_font(self, size=None, bold=False):
        """خط للتسميات (Labels)"""
        weight = QFont.Bold if bold else QFont.Normal
        if size is None:
            size = Config.LABEL_FONT_SIZE
        return self.get_font(size=size, weight=weight, arabic=True)
    
    def get_button_font(self, size=None, bold=True):
        """خط للأزرار"""
        weight = QFont.Bold if bold else QFont.Normal
        if size is None:
            size = Config.BUTTON_FONT_SIZE
        return self.get_font(size=size, weight=weight, arabic=True)
    
    def get_input_font(self, size=None):
        """خط لحقول الإدخال"""
        if size is None:
            size = Config.INPUT_FONT_SIZE
        return self.get_font(size=size, arabic=True)
    
    def get_header_font(self, size=None, bold=True):
        """خط للعناوين"""
        weight = QFont.Bold if bold else QFont.Normal
        if size is None:
            size = Config.HEADER_FONT_SIZE
        return self.get_font(size=size, weight=weight, arabic=True)
    
    def get_title_font(self, size=None, bold=True):
        """خط للعناوين الرئيسية"""
        weight = QFont.Bold if bold else QFont.Normal
        if size is None:
            size = Config.TITLE_FONT_SIZE
        return self.get_font(size=size, weight=weight, arabic=True)
    
    def get_small_font(self, size=None):
        """خط صغير للنصوص الثانوية"""
        if size is None:
            size = Config.SMALL_FONT_SIZE
        return self.get_font(size=size, arabic=True)
    
    def get_large_font(self, size=None, bold=False):
        """خط كبير للنصوص المهمة"""
        weight = QFont.Bold if bold else QFont.Normal
        if size is None:
            size = Config.LARGE_FONT_SIZE
        return self.get_font(size=size, weight=weight, arabic=True)
    
    def apply_font_to_widget(self, widget, font_type="default", size=None, bold=False):
        """
        تطبيق خط على ويدجت معين
        
        Args:
            widget: الويدجت المراد تطبيق الخط عليه
            font_type: نوع الخط (default, label, button, input, header, title, small, large)
            size: حجم الخط (اختياري)
            bold: هل الخط عريض
        """
        if font_type == "label":
            font = self.get_label_font(size, bold)
        elif font_type == "button":
            font = self.get_button_font(size, bold)
        elif font_type == "input":
            font = self.get_input_font(size)
        elif font_type == "header":
            font = self.get_header_font(size, bold)
        elif font_type == "title":
            font = self.get_title_font(size, bold)
        elif font_type == "small":
            font = self.get_small_font(size)
        elif font_type == "large":
            font = self.get_large_font(size, bold)
        else:
            font = self.get_font(size, QFont.Bold if bold else QFont.Normal)
        
        widget.setFont(font)
    
    def get_font_metrics(self, font):
        """الحصول على مقاييس الخط"""
        return QFontMetrics(font)
    
    def calculate_text_size(self, text, font):
        """حساب حجم النص بالبكسل"""
        metrics = self.get_font_metrics(font)
        return metrics.boundingRect(text).size()
    
    def setup_application_font(self, app):
        """إعداد الخط الافتراضي للتطبيق"""
        default_font = self.get_font(Config.DEFAULT_FONT_SIZE)
        app.setFont(default_font)
    
    def get_font_stylesheet(self, font_type="default", size=None, bold=False, color=None):
        """
        الحصول على CSS للخط
        
        Args:
            font_type: نوع الخط
            size: حجم الخط
            bold: هل الخط عريض
            color: لون النص
        
        Returns:
            str: CSS للخط
        """
        if font_type == "label":
            font = self.get_label_font(size, bold)
        elif font_type == "button":
            font = self.get_button_font(size, bold)
        elif font_type == "input":
            font = self.get_input_font(size)
        elif font_type == "header":
            font = self.get_header_font(size, bold)
        elif font_type == "title":
            font = self.get_title_font(size, bold)
        elif font_type == "small":
            font = self.get_small_font(size)
        elif font_type == "large":
            font = self.get_large_font(size, bold)
        else:
            font = self.get_font(size, QFont.Bold if bold else QFont.Normal)
        
        css = f"""
            font-family: '{font.family()}';
            font-size: {font.pointSize()}pt;
            font-weight: {'bold' if font.bold() else 'normal'};
        """
        
        if color:
            css += f"color: {color};"
        
        return css
    
    def get_available_fonts(self):
        """الحصول على قائمة بجميع الخطوط المتاحة"""
        font_db = QFontDatabase()
        return font_db.families()
    
    def is_font_available(self, font_name):
        """التحقق من توفر خط معين"""
        return font_name in self.get_available_fonts()


# إنشاء مثيل عام للاستخدام
font_manager = FontManager()


def get_font_manager():
    """الحصول على مدير الخطوط"""
    return font_manager


def apply_font_to_widget(widget, font_type="default", size=None, bold=False):
    """دالة مساعدة لتطبيق الخط على ويدجت"""
    font_manager.apply_font_to_widget(widget, font_type, size, bold)


def get_font_stylesheet(font_type="default", size=None, bold=False, color=None):
    """دالة مساعدة للحصول على CSS للخط"""
    return font_manager.get_font_stylesheet(font_type, size, bold, color)
