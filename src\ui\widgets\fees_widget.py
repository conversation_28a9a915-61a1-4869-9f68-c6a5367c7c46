#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ويدجت إدارة الرسوم الدراسية والمدفوعات
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                             QMessageBox, QHeaderView, QFrame, QTabWidget,
                             QComboBox, QDateEdit, QDoubleSpinBox, QTextEdit)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont

from src.models.student import Student
from src.models.currency import CurrencyModel
from src.ui.dialogs.fee_dialog import FeeDialog
from src.ui.dialogs.payment_dialog import PaymentDialog


class FeesWidget(QWidget):
    """ويدجت إدارة الرسوم الدراسية والمدفوعات"""

    def __init__(self):
        super().__init__()
        self.student_model = Student()
        self.currency_model = CurrencyModel(self.student_model.db_manager)
        self.default_currency = None
        self.setup_ui()
        self.load_default_currency()
        self.load_fees()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # علامات التبويب
        self.tab_widget = QTabWidget()

        # تبويب الرسوم الدراسية
        self.fees_tab = QWidget()
        self.setup_fees_tab()
        self.tab_widget.addTab(self.fees_tab, "الرسوم الدراسية")

        # تبويب المدفوعات
        self.payments_tab = QWidget()
        self.setup_payments_tab()
        self.tab_widget.addTab(self.payments_tab, "المدفوعات")

        # تبويب التقارير المالية
        self.reports_tab = QWidget()
        self.setup_reports_tab()
        self.tab_widget.addTab(self.reports_tab, "التقارير المالية")

        layout.addWidget(self.tab_widget)

    def load_default_currency(self):
        """تحميل العملة الافتراضية"""
        try:
            self.default_currency = self.currency_model.get_base_currency()
            if not self.default_currency:
                # إذا لم توجد عملة افتراضية، استخدم الريال السعودي
                self.default_currency = {
                    'symbol': 'ر.س',
                    'currency_name': 'الريال السعودي'
                }
        except Exception as e:
            print(f"خطأ في تحميل العملة الافتراضية: {e}")
            self.default_currency = {
                'symbol': 'ر.س',
                'currency_name': 'الريال السعودي'
            }

    def get_currency_symbol(self):
        """الحصول على رمز العملة الافتراضية"""
        if self.default_currency:
            return self.default_currency.get('symbol', 'ر.س')
        return 'ر.س'

    def refresh_currency_display(self):
        """تحديث عرض العملة في جميع أنحاء الواجهة"""
        # إعادة تحميل العملة الافتراضية
        self.load_default_currency()

        # إعادة تحميل البيانات لتحديث العرض
        self.load_fees()
        self.load_payments()
        self.load_financial_statistics()

    def setup_fees_tab(self):
        """إعداد تبويب الرسوم الدراسية"""
        layout = QVBoxLayout(self.fees_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)

        # شريط البحث والأزرار
        top_frame = QFrame()
        top_layout = QHBoxLayout(top_frame)

        # حقل البحث
        self.search_fees_input = QLineEdit()
        self.search_fees_input.setPlaceholderText("البحث عن طالب أو رسوم...")
        self.search_fees_input.setFixedHeight(35)
        self.search_fees_input.textChanged.connect(self.search_fees)

        # فلتر حالة الرسوم
        top_layout.addWidget(QLabel("الحالة:"))
        self.status_filter_combo = QComboBox()
        self.status_filter_combo.addItems(["الكل", "معلق", "مدفوع", "متأخر"])
        self.status_filter_combo.currentTextChanged.connect(self.filter_fees)

        # أزرار الإجراءات
        self.add_fee_button = QPushButton("إضافة رسوم")
        self.edit_fee_button = QPushButton("تعديل")
        self.delete_fee_button = QPushButton("حذف")
        self.refresh_fees_button = QPushButton("تحديث")

        # تطبيق الأنماط على الأزرار
        button_style = """
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """

        for button in [self.add_fee_button, self.edit_fee_button,
                      self.delete_fee_button, self.refresh_fees_button]:
            button.setStyleSheet(button_style)
            button.setFixedHeight(35)

        # تخصيص لون زر الحذف
        self.delete_fee_button.setStyleSheet(
            button_style.replace("#f39c12", "#e74c3c").replace("#e67e22", "#c0392b")
        )

        top_layout.addWidget(QLabel("البحث:"))
        top_layout.addWidget(self.search_fees_input)
        top_layout.addWidget(self.status_filter_combo)
        top_layout.addStretch()
        top_layout.addWidget(self.add_fee_button)
        top_layout.addWidget(self.edit_fee_button)
        top_layout.addWidget(self.delete_fee_button)
        top_layout.addWidget(self.refresh_fees_button)

        layout.addWidget(top_frame)

        # جدول الرسوم
        self.fees_table = QTableWidget()
        self.setup_fees_table()
        layout.addWidget(self.fees_table)

        # ربط الأحداث
        self.setup_fees_connections()

    def setup_fees_table(self):
        """إعداد جدول الرسوم الدراسية"""
        columns = [
            "اسم الطالب", "نوع الرسوم", "المبلغ", "تاريخ الاستحقاق",
            "الخصم", "المبلغ النهائي", "الحالة", "ملاحظات"
        ]

        self.fees_table.setColumnCount(len(columns))
        self.fees_table.setHorizontalHeaderLabels(columns)

        # إعداد خصائص الجدول
        self.fees_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.fees_table.setAlternatingRowColors(True)
        self.fees_table.setSortingEnabled(True)

        # تخصيص عرض الأعمدة
        header = self.fees_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)

        # تطبيق الأنماط
        self.fees_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
            }
            QTableWidget::item:selected {
                background-color: #f39c12;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

    def setup_fees_connections(self):
        """ربط أحداث الرسوم"""
        self.add_fee_button.clicked.connect(self.add_fee)
        self.edit_fee_button.clicked.connect(self.edit_fee)
        self.delete_fee_button.clicked.connect(self.delete_fee)
        self.refresh_fees_button.clicked.connect(self.load_fees)

    def load_fees(self):
        """تحميل قائمة الرسوم الدراسية"""
        try:
            query = """
            SELECT f.*, s.first_name, s.last_name, s.student_number
            FROM fees f
            JOIN students s ON f.student_id = s.student_id
            WHERE s.status = 'active'
            ORDER BY f.due_date DESC, s.first_name
            """
            fees = self.student_model.db_manager.fetch_all(query)
            self.populate_fees_table(fees)
            # تحديث الإحصائيات المالية
            if hasattr(self, 'total_due_label'):  # التأكد من وجود عناصر الإحصائيات
                self.load_financial_statistics()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل الرسوم: {str(e)}")

    def populate_fees_table(self, fees):
        """ملء جدول الرسوم بالبيانات"""
        self.fees_table.setRowCount(len(fees))

        for row, fee in enumerate(fees):
            try:
                # اسم الطالب
                student_name = f"{fee['first_name']} {fee['last_name']} ({fee['student_number']})"
                self.fees_table.setItem(row, 0, QTableWidgetItem(student_name))

                # نوع الرسوم
                self.fees_table.setItem(row, 1, QTableWidgetItem(str(fee['fee_type'])))

                # المبلغ
                amount = fee['amount'] if fee['amount'] else 0
                currency_symbol = self.get_currency_symbol()
                self.fees_table.setItem(row, 2, QTableWidgetItem(f"{amount:,.2f} {currency_symbol}"))

                # تاريخ الاستحقاق
                self.fees_table.setItem(row, 3, QTableWidgetItem(str(fee['due_date'])))

                # الخصم
                discount = fee['discount'] if fee['discount'] else 0
                self.fees_table.setItem(row, 4, QTableWidgetItem(f"{discount:,.2f} {currency_symbol}"))

                # المبلغ النهائي
                final_amount = amount - discount
                self.fees_table.setItem(row, 5, QTableWidgetItem(f"{final_amount:,.2f} {currency_symbol}"))

                # الحالة
                status = fee['status'] if fee['status'] else 'pending'
                status_text = {"pending": "معلق", "paid": "مدفوع", "overdue": "متأخر"}.get(status, status)
                status_item = QTableWidgetItem(status_text)

                # تلوين الحالة
                if status == 'paid':
                    status_item.setBackground(Qt.green)
                elif status == 'overdue':
                    status_item.setBackground(Qt.red)
                else:
                    status_item.setBackground(Qt.yellow)

                self.fees_table.setItem(row, 6, status_item)

                # ملاحظات
                notes = fee['notes'] if fee['notes'] else ""
                self.fees_table.setItem(row, 7, QTableWidgetItem(str(notes)))

                # حفظ معرف الرسوم في البيانات المخفية
                self.fees_table.item(row, 0).setData(Qt.UserRole, fee['fee_id'])

            except Exception as e:
                print(f"خطأ في إضافة الرسوم رقم {row}: {e}")

    def search_fees(self):
        """البحث في الرسوم"""
        search_text = self.search_fees_input.text().strip()

        if not search_text:
            self.load_fees()
            return

        try:
            query = """
            SELECT f.*, s.first_name, s.last_name, s.student_number
            FROM fees f
            JOIN students s ON f.student_id = s.student_id
            WHERE s.status = 'active' AND (
                s.first_name LIKE ? OR s.last_name LIKE ? OR
                s.student_number LIKE ? OR f.fee_type LIKE ?
            )
            ORDER BY f.due_date DESC, s.first_name
            """
            search_param = f"%{search_text}%"
            fees = self.student_model.db_manager.fetch_all(
                query, (search_param, search_param, search_param, search_param)
            )
            self.populate_fees_table(fees)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في البحث: {str(e)}")

    def filter_fees(self):
        """فلترة الرسوم حسب الحالة"""
        status_filter = self.status_filter_combo.currentText()

        if status_filter == "الكل":
            self.load_fees()
            return

        try:
            status_map = {"معلق": "pending", "مدفوع": "paid", "متأخر": "overdue"}
            status_value = status_map.get(status_filter, "pending")

            query = """
            SELECT f.*, s.first_name, s.last_name, s.student_number
            FROM fees f
            JOIN students s ON f.student_id = s.student_id
            WHERE s.status = 'active' AND f.status = ?
            ORDER BY f.due_date DESC, s.first_name
            """
            fees = self.student_model.db_manager.fetch_all(query, (status_value,))
            self.populate_fees_table(fees)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في الفلترة: {str(e)}")

    def add_fee(self):
        """إضافة رسوم جديدة"""
        dialog = FeeDialog(parent=self)
        dialog.fee_saved.connect(self.load_fees)
        dialog.fee_saved.connect(self.load_financial_statistics)  # تحديث الإحصائيات
        dialog.exec_()

    def edit_fee(self):
        """تعديل رسوم"""
        current_row = self.fees_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار رسوم للتعديل")
            return

        # الحصول على معرف الرسوم
        fee_id = self.fees_table.item(current_row, 0).data(Qt.UserRole)

        dialog = FeeDialog(fee_id=fee_id, parent=self)
        dialog.fee_saved.connect(self.load_fees)
        dialog.fee_saved.connect(self.load_financial_statistics)  # تحديث الإحصائيات
        dialog.exec_()

    def delete_fee(self):
        """حذف رسوم"""
        current_row = self.fees_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار رسوم للحذف")
            return

        # الحصول على معرف الرسوم
        fee_id = self.fees_table.item(current_row, 0).data(Qt.UserRole)
        student_name = self.fees_table.item(current_row, 0).text()
        fee_type = self.fees_table.item(current_row, 1).text()

        # تأكيد الحذف
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل أنت متأكد من رغبتك في حذف رسوم '{fee_type}' للطالب: {student_name}؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                query = "DELETE FROM fees WHERE fee_id = ?"
                self.student_model.db_manager.execute_query(query, (fee_id,))
                QMessageBox.information(self, "نجح", "تم حذف الرسوم بنجاح")
                self.load_fees()
                self.load_financial_statistics()  # تحديث الإحصائيات
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ في حذف الرسوم: {str(e)}")

    def setup_payments_tab(self):
        """إعداد تبويب المدفوعات"""
        layout = QVBoxLayout(self.payments_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)

        # شريط التحكم في المدفوعات
        payment_control_frame = QFrame()
        payment_control_layout = QHBoxLayout(payment_control_frame)

        # فلتر التاريخ
        payment_control_layout.addWidget(QLabel("من تاريخ:"))
        self.from_date_edit = QDateEdit()
        self.from_date_edit.setDate(QDate.currentDate().addMonths(-1))
        self.from_date_edit.setCalendarPopup(True)
        payment_control_layout.addWidget(self.from_date_edit)

        payment_control_layout.addWidget(QLabel("إلى تاريخ:"))
        self.to_date_edit = QDateEdit()
        self.to_date_edit.setDate(QDate.currentDate())
        self.to_date_edit.setCalendarPopup(True)
        payment_control_layout.addWidget(self.to_date_edit)

        # أزرار المدفوعات
        self.load_payments_button = QPushButton("تحميل المدفوعات")
        self.record_payment_button = QPushButton("تسجيل دفعة")
        self.print_receipt_button = QPushButton("طباعة إيصال")

        payment_button_style = """
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """

        for button in [self.load_payments_button, self.record_payment_button, self.print_receipt_button]:
            button.setStyleSheet(payment_button_style)
            button.setFixedHeight(35)

        payment_control_layout.addStretch()
        payment_control_layout.addWidget(self.load_payments_button)
        payment_control_layout.addWidget(self.record_payment_button)
        payment_control_layout.addWidget(self.print_receipt_button)

        layout.addWidget(payment_control_frame)

        # جدول المدفوعات
        self.payments_table = QTableWidget()
        self.setup_payments_table()
        layout.addWidget(self.payments_table)

        # ربط أحداث المدفوعات
        self.setup_payments_connections()

    def setup_payments_table(self):
        """إعداد جدول المدفوعات"""
        columns = [
            "اسم الطالب", "نوع الرسوم", "المبلغ المدفوع", "تاريخ الدفع",
            "طريقة الدفع", "رقم الإيصال", "ملاحظات"
        ]

        self.payments_table.setColumnCount(len(columns))
        self.payments_table.setHorizontalHeaderLabels(columns)

        # إعداد خصائص الجدول
        self.payments_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.payments_table.setAlternatingRowColors(True)
        self.payments_table.setSortingEnabled(True)

        # تخصيص عرض الأعمدة
        header = self.payments_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)

        # تطبيق الأنماط
        self.payments_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
            }
            QTableWidget::item:selected {
                background-color: #27ae60;
                color: white;
            }
            QHeaderView::section {
                background-color: #2c3e50;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

    def setup_payments_connections(self):
        """ربط أحداث المدفوعات"""
        self.load_payments_button.clicked.connect(self.load_payments)
        self.record_payment_button.clicked.connect(self.record_payment)
        self.print_receipt_button.clicked.connect(self.print_receipt)
        self.from_date_edit.dateChanged.connect(self.load_payments)
        self.to_date_edit.dateChanged.connect(self.load_payments)

    def load_payments(self):
        """تحميل المدفوعات"""
        try:
            from_date = self.from_date_edit.date().toString("yyyy-MM-dd")
            to_date = self.to_date_edit.date().toString("yyyy-MM-dd")

            query = """
            SELECT f.*, s.first_name, s.last_name, s.student_number
            FROM fees f
            JOIN students s ON f.student_id = s.student_id
            WHERE f.status = 'paid' AND f.payment_date BETWEEN ? AND ?
            ORDER BY f.payment_date DESC
            """
            payments = self.student_model.db_manager.fetch_all(query, (from_date, to_date))
            self.populate_payments_table(payments)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل المدفوعات: {str(e)}")

    def populate_payments_table(self, payments):
        """ملء جدول المدفوعات بالبيانات"""
        self.payments_table.setRowCount(len(payments))

        for row, payment in enumerate(payments):
            try:
                # اسم الطالب
                student_name = f"{payment['first_name']} {payment['last_name']} ({payment['student_number']})"
                self.payments_table.setItem(row, 0, QTableWidgetItem(student_name))

                # نوع الرسوم
                self.payments_table.setItem(row, 1, QTableWidgetItem(str(payment['fee_type'])))

                # المبلغ المدفوع
                amount = payment['amount'] - (payment['discount'] or 0)
                currency_symbol = self.get_currency_symbol()
                self.payments_table.setItem(row, 2, QTableWidgetItem(f"{amount:,.2f} {currency_symbol}"))

                # تاريخ الدفع
                self.payments_table.setItem(row, 3, QTableWidgetItem(str(payment['payment_date'])))

                # طريقة الدفع
                payment_method = payment['payment_method'] if payment['payment_method'] else "نقدي"
                self.payments_table.setItem(row, 4, QTableWidgetItem(payment_method))

                # رقم الإيصال
                receipt_number = f"REC-{payment['fee_id']:06d}"
                self.payments_table.setItem(row, 5, QTableWidgetItem(receipt_number))

                # ملاحظات
                notes = payment['notes'] if payment['notes'] else ""
                self.payments_table.setItem(row, 6, QTableWidgetItem(str(notes)))

                # حفظ معرف الرسوم في البيانات المخفية
                self.payments_table.item(row, 0).setData(Qt.UserRole, payment['fee_id'])

            except Exception as e:
                print(f"خطأ في إضافة المدفوعات رقم {row}: {e}")

    def record_payment(self):
        """تسجيل دفعة جديدة"""
        dialog = PaymentDialog(parent=self)
        dialog.payment_recorded.connect(self.load_payments)
        dialog.payment_recorded.connect(self.load_fees)  # تحديث جدول الرسوم
        dialog.payment_recorded.connect(self.load_financial_statistics)  # تحديث الإحصائيات
        dialog.exec_()

    def print_receipt(self):
        """طباعة إيصال"""
        current_row = self.payments_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مدفوعة لطباعة إيصالها")
            return

        receipt_number = self.payments_table.item(current_row, 5).text()
        student_name = self.payments_table.item(current_row, 0).text()

        self.print_payment_receipt(receipt_number, student_name)

    def print_payment_receipt(self, receipt_number, student_name):
        """طباعة إيصال الدفع"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
            from PyQt5.QtGui import QPainter, QFont
            from PyQt5.QtCore import QRect
            from datetime import datetime

            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)
            printer.setOrientation(QPrinter.Portrait)

            print_dialog = QPrintDialog(printer, self)
            if print_dialog.exec_() == QPrintDialog.Accepted:
                painter = QPainter(printer)

                # إعداد الخطوط
                title_font = QFont("Arial", 16, QFont.Bold)
                normal_font = QFont("Arial", 12)
                small_font = QFont("Arial", 10)

                # رسم الإيصال
                y_pos = 100

                # عنوان الإيصال
                painter.setFont(title_font)
                title_rect = QRect(0, y_pos, printer.width(), 50)
                painter.drawText(title_rect, Qt.AlignCenter, "إيصال دفع رسوم دراسية")
                y_pos += 80

                # معلومات الإيصال
                painter.setFont(normal_font)
                painter.drawText(100, y_pos, f"رقم الإيصال: {receipt_number}")
                y_pos += 40

                painter.drawText(100, y_pos, f"اسم الطالب: {student_name}")
                y_pos += 40

                current_date = datetime.now().strftime("%Y-%m-%d %H:%M")
                painter.drawText(100, y_pos, f"تاريخ الإصدار: {current_date}")
                y_pos += 40

                # خط فاصل
                painter.drawLine(100, y_pos, printer.width() - 100, y_pos)
                y_pos += 60

                # تفاصيل الدفع
                painter.drawText(100, y_pos, "تم استلام المبلغ بنجاح")
                y_pos += 40

                painter.drawText(100, y_pos, "شكراً لكم لثقتكم بنا")
                y_pos += 80

                # خط فاصل سفلي
                painter.drawLine(100, y_pos, printer.width() - 100, y_pos)
                y_pos += 40

                # معلومات المدرسة
                painter.setFont(small_font)
                painter.drawText(100, y_pos, "نظام إدارة المدارس - تم الإنشاء تلقائياً")

                painter.end()
                QMessageBox.information(self, "نجح", "تم طباعة الإيصال بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في طباعة الإيصال: {str(e)}")

    def setup_reports_tab(self):
        """إعداد تبويب التقارير المالية"""
        layout = QVBoxLayout(self.reports_tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # عنوان التقارير
        reports_title = QLabel("التقارير المالية")
        reports_title.setAlignment(Qt.AlignCenter)
        reports_title.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(reports_title)

        # إحصائيات سريعة
        stats_frame = QFrame()
        stats_frame.setFrameStyle(QFrame.StyledPanel)
        stats_layout = QHBoxLayout(stats_frame)

        # إجمالي الرسوم المستحقة
        currency_symbol = self.get_currency_symbol()
        self.total_due_label = QLabel(f"إجمالي المستحق\n0.00 {currency_symbol}")
        self.total_due_label.setAlignment(Qt.AlignCenter)
        self.total_due_label.setStyleSheet("""
            QLabel {
                background-color: #e74c3c;
                color: white;
                padding: 20px;
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
            }
        """)

        # إجمالي المدفوع
        self.total_paid_label = QLabel(f"إجمالي المدفوع\n0.00 {currency_symbol}")
        self.total_paid_label.setAlignment(Qt.AlignCenter)
        self.total_paid_label.setStyleSheet("""
            QLabel {
                background-color: #27ae60;
                color: white;
                padding: 20px;
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
            }
        """)

        # إجمالي المتأخر
        self.total_overdue_label = QLabel(f"إجمالي المتأخر\n0.00 {currency_symbol}")
        self.total_overdue_label.setAlignment(Qt.AlignCenter)
        self.total_overdue_label.setStyleSheet("""
            QLabel {
                background-color: #f39c12;
                color: white;
                padding: 20px;
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
            }
        """)

        stats_layout.addWidget(self.total_due_label)
        stats_layout.addWidget(self.total_paid_label)
        stats_layout.addWidget(self.total_overdue_label)

        layout.addWidget(stats_frame)

        # أزرار التقارير
        reports_buttons_frame = QFrame()
        reports_buttons_layout = QVBoxLayout(reports_buttons_frame)

        report_button_style = """
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 15px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """

        self.monthly_report_button = QPushButton("تقرير الرسوم الشهري")
        self.monthly_report_button.setStyleSheet(report_button_style)

        self.overdue_report_button = QPushButton("تقرير الرسوم المتأخرة")
        self.overdue_report_button.setStyleSheet(report_button_style)

        self.collection_report_button = QPushButton("تقرير التحصيل")
        self.collection_report_button.setStyleSheet(report_button_style)

        reports_buttons_layout.addWidget(self.monthly_report_button)
        reports_buttons_layout.addWidget(self.overdue_report_button)
        reports_buttons_layout.addWidget(self.collection_report_button)

        layout.addWidget(reports_buttons_frame)
        layout.addStretch()

        # ربط أحداث التقارير
        self.setup_reports_connections()

        # تحميل الإحصائيات
        self.load_financial_statistics()

    def setup_reports_connections(self):
        """ربط أحداث التقارير"""
        self.monthly_report_button.clicked.connect(self.generate_monthly_report)
        self.overdue_report_button.clicked.connect(self.generate_overdue_report)
        self.collection_report_button.clicked.connect(self.generate_collection_report)

    def load_financial_statistics(self):
        """تحميل الإحصائيات المالية"""
        try:
            # إجمالي المستحق
            total_due_query = """
            SELECT SUM(amount - COALESCE(discount, 0)) as total
            FROM fees WHERE status = 'pending'
            """
            total_due_result = self.student_model.db_manager.fetch_one(total_due_query)
            total_due = total_due_result['total'] if total_due_result['total'] else 0
            currency_symbol = self.get_currency_symbol()
            self.total_due_label.setText(f"إجمالي المستحق\n{total_due:,.2f} {currency_symbol}")

            # إجمالي المدفوع
            total_paid_query = """
            SELECT SUM(amount - COALESCE(discount, 0)) as total
            FROM fees WHERE status = 'paid'
            """
            total_paid_result = self.student_model.db_manager.fetch_one(total_paid_query)
            total_paid = total_paid_result['total'] if total_paid_result['total'] else 0
            self.total_paid_label.setText(f"إجمالي المدفوع\n{total_paid:,.2f} {currency_symbol}")

            # إجمالي المتأخر
            total_overdue_query = """
            SELECT SUM(amount - COALESCE(discount, 0)) as total
            FROM fees WHERE status = 'overdue'
            """
            total_overdue_result = self.student_model.db_manager.fetch_one(total_overdue_query)
            total_overdue = total_overdue_result['total'] if total_overdue_result['total'] else 0
            self.total_overdue_label.setText(f"إجمالي المتأخر\n{total_overdue:,.2f} {currency_symbol}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل الإحصائيات: {str(e)}")

    def generate_monthly_report(self):
        """إنشاء تقرير الرسوم الشهري"""
        try:
            # الحصول على الشهر والسنة الحاليين
            from datetime import datetime
            current_date = datetime.now()
            month = current_date.month
            year = current_date.year

            # استعلام الرسوم للشهر الحالي
            query = """
            SELECT f.*, s.first_name, s.last_name, s.student_number
            FROM fees f
            JOIN students s ON f.student_id = s.student_id
            WHERE strftime('%m', f.due_date) = ? AND strftime('%Y', f.due_date) = ?
            ORDER BY f.due_date, s.first_name
            """

            fees = self.student_model.db_manager.fetch_all(query, (f"{month:02d}", str(year)))

            if not fees:
                QMessageBox.information(
                    self,
                    "تقرير الرسوم الشهري",
                    f"لا توجد رسوم مستحقة للشهر {month}/{year}"
                )
                return

            # إنشاء التقرير
            self.show_monthly_report_dialog(fees, month, year)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في إنشاء التقرير: {str(e)}")

    def generate_overdue_report(self):
        """إنشاء تقرير الرسوم المتأخرة"""
        try:
            # استعلام الرسوم المتأخرة
            from datetime import datetime
            current_date = datetime.now().strftime('%Y-%m-%d')

            query = """
            SELECT f.*, s.first_name, s.last_name, s.student_number
            FROM fees f
            JOIN students s ON f.student_id = s.student_id
            WHERE f.status = 'pending' AND f.due_date < ?
            ORDER BY f.due_date, s.first_name
            """

            overdue_fees = self.student_model.db_manager.fetch_all(query, (current_date,))

            if not overdue_fees:
                QMessageBox.information(
                    self,
                    "تقرير الرسوم المتأخرة",
                    "لا توجد رسوم متأخرة حالياً"
                )
                return

            # إنشاء التقرير
            self.show_overdue_report_dialog(overdue_fees)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في إنشاء التقرير: {str(e)}")

    def generate_collection_report(self):
        """إنشاء تقرير التحصيل"""
        try:
            # استعلام المدفوعات للشهر الحالي
            from datetime import datetime
            current_date = datetime.now()
            month = current_date.month
            year = current_date.year

            # استعلام محسن للمدفوعات
            query = """
            SELECT f.*, s.first_name, s.last_name, s.student_number
            FROM fees f
            JOIN students s ON f.student_id = s.student_id
            WHERE f.status = 'paid'
            AND f.payment_date IS NOT NULL
            AND (
                strftime('%m', f.payment_date) = ? AND strftime('%Y', f.payment_date) = ?
                OR
                (f.payment_date LIKE ? OR f.payment_date LIKE ?)
            )
            ORDER BY f.payment_date DESC, s.first_name
            """

            # معاملات البحث المتعددة
            month_str = f"{month:02d}"
            year_str = str(year)
            date_pattern1 = f"{year}-{month_str}-%"
            date_pattern2 = f"{year_str}-{month_str}-%"

            collections = self.student_model.db_manager.fetch_all(
                query,
                (month_str, year_str, date_pattern1, date_pattern2)
            )

            if not collections:
                # إذا لم توجد مدفوعات للشهر الحالي، اعرض جميع المدفوعات
                fallback_query = """
                SELECT f.*, s.first_name, s.last_name, s.student_number
                FROM fees f
                JOIN students s ON f.student_id = s.student_id
                WHERE f.status = 'paid'
                AND f.payment_date IS NOT NULL
                ORDER BY f.payment_date DESC, s.first_name
                LIMIT 50
                """

                collections = self.student_model.db_manager.fetch_all(fallback_query)

                if not collections:
                    QMessageBox.information(
                        self,
                        "تقرير التحصيل",
                        "لا توجد مدفوعات مسجلة في النظام"
                    )
                    return
                else:
                    # إنشاء التقرير لجميع المدفوعات
                    self.show_collection_report_dialog(collections, "جميع", "الأشهر")
                    return

            # إنشاء التقرير للشهر الحالي
            self.show_collection_report_dialog(collections, month, year)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في إنشاء التقرير: {str(e)}")

    def show_monthly_report_dialog(self, fees, month, year):
        """عرض نافذة تقرير الرسوم الشهري"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem, QPushButton, QLabel

        dialog = QDialog(self)
        dialog.setWindowTitle(f"تقرير الرسوم الشهري - {month}/{year}")
        dialog.setModal(True)
        dialog.resize(800, 600)
        dialog.setLayoutDirection(Qt.RightToLeft)

        layout = QVBoxLayout(dialog)

        # عنوان التقرير
        title_label = QLabel(f"تقرير الرسوم الشهري - {month}/{year}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title_label)

        # جدول البيانات
        table = QTableWidget()
        table.setColumnCount(7)
        table.setHorizontalHeaderLabels([
            "رقم الطالب", "اسم الطالب", "نوع الرسوم", "المبلغ",
            "تاريخ الاستحقاق", "الحالة", "الخصم"
        ])

        table.setRowCount(len(fees))
        currency_symbol = self.get_currency_symbol()
        total_amount = 0

        for row, fee in enumerate(fees):
            table.setItem(row, 0, QTableWidgetItem(str(fee['student_number'])))
            table.setItem(row, 1, QTableWidgetItem(f"{fee['first_name']} {fee['last_name']}"))
            table.setItem(row, 2, QTableWidgetItem(str(fee['fee_type'])))

            amount = fee['amount'] - (fee['discount'] or 0)
            table.setItem(row, 3, QTableWidgetItem(f"{amount:,.2f} {currency_symbol}"))
            total_amount += amount

            table.setItem(row, 4, QTableWidgetItem(str(fee['due_date'])))

            status_text = {"pending": "معلق", "paid": "مدفوع", "overdue": "متأخر"}.get(fee['status'], fee['status'])
            table.setItem(row, 5, QTableWidgetItem(status_text))

            discount = fee['discount'] or 0
            table.setItem(row, 6, QTableWidgetItem(f"{discount:,.2f} {currency_symbol}"))

        table.resizeColumnsToContents()
        layout.addWidget(table)

        # إجمالي المبلغ
        total_label = QLabel(f"إجمالي المبلغ: {total_amount:,.2f} {currency_symbol}")
        total_label.setAlignment(Qt.AlignCenter)
        total_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #27ae60;
                padding: 10px;
                background-color: #d5f4e6;
                border-radius: 5px;
                margin: 10px;
            }
        """)
        layout.addWidget(total_label)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        print_button = QPushButton("طباعة")
        print_button.clicked.connect(lambda: self.print_report(table, f"تقرير الرسوم الشهري - {month}/{year}"))

        export_button = QPushButton("تصدير إلى Excel")
        export_button.clicked.connect(lambda: self.export_report_to_excel(fees, f"تقرير_الرسوم_الشهري_{month}_{year}"))

        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(dialog.accept)

        buttons_layout.addWidget(print_button)
        buttons_layout.addWidget(export_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(close_button)

        layout.addLayout(buttons_layout)

        dialog.exec_()

    def show_overdue_report_dialog(self, overdue_fees):
        """عرض نافذة تقرير الرسوم المتأخرة"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem, QPushButton, QLabel
        from datetime import datetime

        dialog = QDialog(self)
        dialog.setWindowTitle("تقرير الرسوم المتأخرة")
        dialog.setModal(True)
        dialog.resize(800, 600)
        dialog.setLayoutDirection(Qt.RightToLeft)

        layout = QVBoxLayout(dialog)

        # عنوان التقرير
        title_label = QLabel("تقرير الرسوم المتأخرة")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #e74c3c;
                padding: 15px;
                background-color: #fdf2f2;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title_label)

        # جدول البيانات
        table = QTableWidget()
        table.setColumnCount(8)
        table.setHorizontalHeaderLabels([
            "رقم الطالب", "اسم الطالب", "نوع الرسوم", "المبلغ",
            "تاريخ الاستحقاق", "أيام التأخير", "الخصم", "المبلغ النهائي"
        ])

        table.setRowCount(len(overdue_fees))
        currency_symbol = self.get_currency_symbol()
        total_overdue = 0
        current_date = datetime.now().date()

        for row, fee in enumerate(overdue_fees):
            table.setItem(row, 0, QTableWidgetItem(str(fee['student_number'])))
            table.setItem(row, 1, QTableWidgetItem(f"{fee['first_name']} {fee['last_name']}"))
            table.setItem(row, 2, QTableWidgetItem(str(fee['fee_type'])))

            amount = fee['amount']
            table.setItem(row, 3, QTableWidgetItem(f"{amount:,.2f} {currency_symbol}"))

            due_date_str = str(fee['due_date'])
            table.setItem(row, 4, QTableWidgetItem(due_date_str))

            # حساب أيام التأخير
            try:
                due_date = datetime.strptime(due_date_str, '%Y-%m-%d').date()
                days_overdue = (current_date - due_date).days
                table.setItem(row, 5, QTableWidgetItem(f"{days_overdue} يوم"))
            except:
                table.setItem(row, 5, QTableWidgetItem("غير محدد"))

            discount = fee['discount'] or 0
            table.setItem(row, 6, QTableWidgetItem(f"{discount:,.2f} {currency_symbol}"))

            final_amount = amount - discount
            table.setItem(row, 7, QTableWidgetItem(f"{final_amount:,.2f} {currency_symbol}"))
            total_overdue += final_amount

        table.resizeColumnsToContents()
        layout.addWidget(table)

        # إجمالي المبلغ المتأخر
        total_label = QLabel(f"إجمالي المبلغ المتأخر: {total_overdue:,.2f} {currency_symbol}")
        total_label.setAlignment(Qt.AlignCenter)
        total_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #e74c3c;
                padding: 10px;
                background-color: #fdf2f2;
                border-radius: 5px;
                margin: 10px;
            }
        """)
        layout.addWidget(total_label)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        print_button = QPushButton("طباعة")
        print_button.clicked.connect(lambda: self.print_report(table, "تقرير الرسوم المتأخرة"))

        export_button = QPushButton("تصدير إلى Excel")
        export_button.clicked.connect(lambda: self.export_report_to_excel(overdue_fees, "تقرير_الرسوم_المتأخرة"))

        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(dialog.accept)

        buttons_layout.addWidget(print_button)
        buttons_layout.addWidget(export_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(close_button)

        layout.addLayout(buttons_layout)

        dialog.exec_()

    def show_collection_report_dialog(self, collections, month, year):
        """عرض نافذة تقرير التحصيل"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem, QPushButton, QLabel

        dialog = QDialog(self)
        dialog.setWindowTitle(f"تقرير التحصيل - {month}/{year}")
        dialog.setModal(True)
        dialog.resize(800, 600)
        dialog.setLayoutDirection(Qt.RightToLeft)

        layout = QVBoxLayout(dialog)

        # عنوان التقرير
        title_label = QLabel(f"تقرير التحصيل - {month}/{year}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #27ae60;
                padding: 15px;
                background-color: #d5f4e6;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title_label)

        # جدول البيانات
        table = QTableWidget()
        table.setColumnCount(7)
        table.setHorizontalHeaderLabels([
            "رقم الطالب", "اسم الطالب", "نوع الرسوم", "المبلغ المدفوع",
            "تاريخ الدفع", "طريقة الدفع", "ملاحظات"
        ])

        table.setRowCount(len(collections))
        currency_symbol = self.get_currency_symbol()
        total_collected = 0

        for row, collection in enumerate(collections):
            table.setItem(row, 0, QTableWidgetItem(str(collection['student_number'])))
            table.setItem(row, 1, QTableWidgetItem(f"{collection['first_name']} {collection['last_name']}"))
            table.setItem(row, 2, QTableWidgetItem(str(collection['fee_type'])))

            amount_paid = collection['amount'] - (collection['discount'] or 0)
            table.setItem(row, 3, QTableWidgetItem(f"{amount_paid:,.2f} {currency_symbol}"))
            total_collected += amount_paid

            payment_date = collection.get('payment_date', 'غير محدد')
            table.setItem(row, 4, QTableWidgetItem(str(payment_date)))

            payment_method = collection.get('payment_method', 'نقدي')
            table.setItem(row, 5, QTableWidgetItem(str(payment_method)))

            notes = collection.get('notes', '')
            table.setItem(row, 6, QTableWidgetItem(str(notes)))

        table.resizeColumnsToContents()
        layout.addWidget(table)

        # إجمالي المبلغ المحصل
        total_label = QLabel(f"إجمالي المبلغ المحصل: {total_collected:,.2f} {currency_symbol}")
        total_label.setAlignment(Qt.AlignCenter)
        total_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #27ae60;
                padding: 10px;
                background-color: #d5f4e6;
                border-radius: 5px;
                margin: 10px;
            }
        """)
        layout.addWidget(total_label)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        print_button = QPushButton("طباعة")
        print_button.clicked.connect(lambda: self.print_report(table, f"تقرير التحصيل - {month}/{year}"))

        export_button = QPushButton("تصدير إلى Excel")
        export_button.clicked.connect(lambda: self.export_report_to_excel(collections, f"تقرير_التحصيل_{month}_{year}"))

        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(dialog.accept)

        buttons_layout.addWidget(print_button)
        buttons_layout.addWidget(export_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(close_button)

        layout.addLayout(buttons_layout)

        dialog.exec_()

    def print_report(self, table, title):
        """طباعة التقرير"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
            from PyQt5.QtGui import QPainter, QFont
            from PyQt5.QtCore import QRect

            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)
            printer.setOrientation(QPrinter.Portrait)

            print_dialog = QPrintDialog(printer, self)
            if print_dialog.exec_() == QPrintDialog.Accepted:
                painter = QPainter(printer)

                # إعداد الخط
                font = QFont("Arial", 12)
                painter.setFont(font)

                # طباعة العنوان
                title_rect = QRect(0, 0, printer.width(), 100)
                painter.drawText(title_rect, Qt.AlignCenter, title)

                # طباعة الجدول (مبسط)
                y_offset = 150
                row_height = 30

                # طباعة رؤوس الأعمدة
                for col in range(table.columnCount()):
                    header_text = table.horizontalHeaderItem(col).text()
                    x_offset = col * (printer.width() // table.columnCount())
                    painter.drawText(x_offset, y_offset, header_text)

                y_offset += row_height

                # طباعة البيانات
                for row in range(min(table.rowCount(), 20)):  # طباعة أول 20 صف فقط
                    for col in range(table.columnCount()):
                        item = table.item(row, col)
                        if item:
                            text = item.text()
                            x_offset = col * (printer.width() // table.columnCount())
                            painter.drawText(x_offset, y_offset, text[:20])  # قطع النص الطويل
                    y_offset += row_height

                painter.end()
                QMessageBox.information(self, "نجح", "تم إرسال التقرير للطباعة بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في الطباعة: {str(e)}")

    def export_report_to_excel(self, data, filename):
        """تصدير التقرير إلى Excel"""
        try:
            import csv
            import os
            from PyQt5.QtWidgets import QFileDialog

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ التقرير",
                f"{filename}.csv",
                "CSV Files (*.csv);;All Files (*)"
            )

            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة رؤوس الأعمدة
                    headers = [
                        "رقم الطالب", "الاسم الأول", "الاسم الأخير", "نوع الرسوم",
                        "المبلغ", "تاريخ الاستحقاق", "الحالة", "الخصم", "تاريخ الدفع"
                    ]
                    writer.writerow(headers)

                    # كتابة البيانات
                    currency_symbol = self.get_currency_symbol()
                    for item in data:
                        row = [
                            item.get('student_number', ''),
                            item.get('first_name', ''),
                            item.get('last_name', ''),
                            item.get('fee_type', ''),
                            f"{item.get('amount', 0):,.2f} {currency_symbol}",
                            item.get('due_date', ''),
                            item.get('status', ''),
                            f"{item.get('discount', 0):,.2f} {currency_symbol}",
                            item.get('payment_date', '')
                        ]
                        writer.writerow(row)

                QMessageBox.information(self, "نجح", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في التصدير: {str(e)}")
