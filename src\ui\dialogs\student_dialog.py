#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إضافة وتعديل الطلاب
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QLineEdit, QPushButton, QLabel, QFrame, QMessageBox,
                             QComboBox, QDateEdit, QTextEdit, QTabWidget, QWidget,
                             QGridLayout, QSpacerItem, QSizePolicy)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont

from src.models.student import Student
from src.models.base_model import BaseModel
from src.utils.ui_styles import UIStyles


class StudentDialog(QDialog):
    """نافذة إضافة وتعديل الطلاب"""
    
    # إشارة حفظ البيانات
    student_saved = pyqtSignal()
    
    def __init__(self, student_id=None, parent=None):
        super().__init__(parent)
        self.student_id = student_id
        self.student_model = Student()
        self.is_edit_mode = student_id is not None
        
        self.setup_ui()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.load_student_data()
            
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة
        title = "تعديل طالب" if self.is_edit_mode else "إضافة طالب جديد"
        self.setWindowTitle(title)
        self.setFixedSize(600, 700)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # عنوان النافذة
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # علامات التبويب
        self.tab_widget = QTabWidget()
        
        # تبويب البيانات الأساسية
        self.basic_tab = QWidget()
        self.setup_basic_tab()
        self.tab_widget.addTab(self.basic_tab, "البيانات الأساسية")
        
        # تبويب بيانات ولي الأمر
        self.parent_tab = QWidget()
        self.setup_parent_tab()
        self.tab_widget.addTab(self.parent_tab, "بيانات ولي الأمر")
        
        # تبويب معلومات إضافية
        self.additional_tab = QWidget()
        self.setup_additional_tab()
        self.tab_widget.addTab(self.additional_tab, "معلومات إضافية")
        
        main_layout.addWidget(self.tab_widget)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        self.save_button = QPushButton("حفظ")
        self.save_button.setFixedHeight(40)
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setFixedHeight(40)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.save_button)
        
        main_layout.addLayout(buttons_layout)
        
    def setup_basic_tab(self):
        """إعداد تبويب البيانات الأساسية"""
        layout = QFormLayout(self.basic_tab)
        layout.setSpacing(15)
        
        # رقم الطالب
        self.student_number_input = QLineEdit()
        self.student_number_input.setPlaceholderText("سيتم توليده تلقائياً")
        self.student_number_input.setReadOnly(True)
        self.student_number_input.setStyleSheet("background-color: #f0f0f0; color: #666;")
        layout.addRow("رقم الطالب:", self.student_number_input)
        
        # الاسم الأول
        self.first_name_input = QLineEdit()
        self.first_name_input.setPlaceholderText("أدخل الاسم الأول")
        layout.addRow("الاسم الأول *:", self.first_name_input)
        
        # الاسم الأخير
        self.last_name_input = QLineEdit()
        self.last_name_input.setPlaceholderText("أدخل الاسم الأخير")
        layout.addRow("الاسم الأخير *:", self.last_name_input)
        
        # تاريخ الميلاد
        self.date_of_birth_input = QDateEdit()
        self.date_of_birth_input.setDate(QDate.currentDate().addYears(-10))
        self.date_of_birth_input.setCalendarPopup(True)
        layout.addRow("تاريخ الميلاد *:", self.date_of_birth_input)
        
        # الجنس
        self.gender_combo = QComboBox()
        self.gender_combo.addItems(["ذكر", "أنثى"])
        layout.addRow("الجنس *:", self.gender_combo)
        
        # رقم الهوية
        self.national_id_input = QLineEdit()
        self.national_id_input.setPlaceholderText("أدخل رقم الهوية (اختياري)")
        layout.addRow("رقم الهوية:", self.national_id_input)
        
        # العنوان
        self.address_input = QLineEdit()
        self.address_input.setPlaceholderText("أدخل العنوان")
        layout.addRow("العنوان:", self.address_input)
        
        # رقم الهاتف
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("أدخل رقم الهاتف")
        layout.addRow("رقم الهاتف:", self.phone_input)
        
    def setup_parent_tab(self):
        """إعداد تبويب بيانات ولي الأمر"""
        layout = QFormLayout(self.parent_tab)
        layout.setSpacing(15)
        
        # اسم ولي الأمر
        self.parent_name_input = QLineEdit()
        self.parent_name_input.setPlaceholderText("أدخل اسم ولي الأمر")
        layout.addRow("اسم ولي الأمر *:", self.parent_name_input)
        
        # رقم هاتف ولي الأمر
        self.parent_phone_input = QLineEdit()
        self.parent_phone_input.setPlaceholderText("أدخل رقم هاتف ولي الأمر")
        layout.addRow("رقم هاتف ولي الأمر *:", self.parent_phone_input)
        
        # بريد ولي الأمر الإلكتروني
        self.parent_email_input = QLineEdit()
        self.parent_email_input.setPlaceholderText("أدخل البريد الإلكتروني (اختياري)")
        layout.addRow("البريد الإلكتروني:", self.parent_email_input)
        
    def setup_additional_tab(self):
        """إعداد تبويب المعلومات الإضافية"""
        layout = QFormLayout(self.additional_tab)
        layout.setSpacing(15)
        
        # تاريخ التسجيل
        self.enrollment_date_input = QDateEdit()
        self.enrollment_date_input.setDate(QDate.currentDate())
        self.enrollment_date_input.setCalendarPopup(True)
        layout.addRow("تاريخ التسجيل:", self.enrollment_date_input)
        
        # الحالة
        self.status_combo = QComboBox()
        self.status_combo.addItems(["نشط", "غير نشط"])
        layout.addRow("الحالة:", self.status_combo)
        
        # ملاحظات
        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText("أدخل أي ملاحظات إضافية")
        self.notes_input.setMaximumHeight(100)
        layout.addRow("ملاحظات:", self.notes_input)
        
    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        self.save_button.clicked.connect(self.save_student)
        self.cancel_button.clicked.connect(self.reject)
        
    def load_student_data(self):
        """تحميل بيانات الطالب للتعديل"""
        try:
            student = self.student_model.get_by_id(self.student_id)
            if not student:
                QMessageBox.critical(self, "خطأ", "لم يتم العثور على الطالب")
                self.reject()
                return
                
            # ملء الحقول بالبيانات
            self.student_number_input.setText(str(student['student_number']))
            self.first_name_input.setText(str(student['first_name']))
            self.last_name_input.setText(str(student['last_name']))
            
            # تاريخ الميلاد
            if student['date_of_birth']:
                date = QDate.fromString(str(student['date_of_birth']), "yyyy-MM-dd")
                self.date_of_birth_input.setDate(date)
                
            # الجنس
            gender_index = 0 if student['gender'] == 'male' else 1
            self.gender_combo.setCurrentIndex(gender_index)
            
            # باقي الحقول
            self.national_id_input.setText(str(student['national_id'] or ''))
            self.address_input.setText(str(student['address'] or ''))
            self.phone_input.setText(str(student['phone'] or ''))
            self.parent_name_input.setText(str(student['parent_name']))
            self.parent_phone_input.setText(str(student['parent_phone']))
            self.parent_email_input.setText(str(student['parent_email'] or ''))
            
            # تاريخ التسجيل
            if student['enrollment_date']:
                date = QDate.fromString(str(student['enrollment_date']), "yyyy-MM-dd")
                self.enrollment_date_input.setDate(date)
                
            # الحالة
            status_index = 0 if student['status'] == 'active' else 1
            self.status_combo.setCurrentIndex(status_index)
            
            self.notes_input.setPlainText(str(student['notes'] or ''))
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل بيانات الطالب: {str(e)}")
            
    def save_student(self):
        """حفظ بيانات الطالب"""
        try:
            # جمع البيانات من النموذج
            student_data = {
                'first_name': self.first_name_input.text().strip(),
                'last_name': self.last_name_input.text().strip(),
                'date_of_birth': self.date_of_birth_input.date().toString("yyyy-MM-dd"),
                'gender': 'male' if self.gender_combo.currentIndex() == 0 else 'female',
                'national_id': self.national_id_input.text().strip() or None,
                'address': self.address_input.text().strip() or None,
                'phone': self.phone_input.text().strip() or None,
                'parent_name': self.parent_name_input.text().strip(),
                'parent_phone': self.parent_phone_input.text().strip(),
                'parent_email': self.parent_email_input.text().strip() or None,
                'enrollment_date': self.enrollment_date_input.date().toString("yyyy-MM-dd"),
                'status': 'active' if self.status_combo.currentIndex() == 0 else 'inactive',
                'notes': self.notes_input.toPlainText().strip() or None
            }

            # إضافة رقم الطالب فقط في حالة التعديل
            if self.student_id and self.student_number_input.text().strip():
                student_data['student_number'] = self.student_number_input.text().strip()
            
            # التحقق من الحقول المطلوبة (رقم الطالب سيتم توليده تلقائياً)
            required_fields = ['first_name', 'last_name', 'parent_name', 'parent_phone']
            for field in required_fields:
                if not student_data[field]:
                    field_names = {
                        'first_name': 'الاسم الأول',
                        'last_name': 'الاسم الأخير',
                        'parent_name': 'اسم ولي الأمر',
                        'parent_phone': 'رقم هاتف ولي الأمر'
                    }
                    QMessageBox.warning(self, "تحذير", f"الحقل '{field_names.get(field, field)}' مطلوب")
                    return
            
            # حفظ البيانات
            if self.is_edit_mode:
                self.student_model.update_student(self.student_id, student_data)
                QMessageBox.information(self, "نجح", "تم تحديث بيانات الطالب بنجاح")
            else:
                self.student_model.add_student(student_data)
                QMessageBox.information(self, "نجح", "تم إضافة الطالب بنجاح")
            
            # إرسال إشارة الحفظ
            self.student_saved.emit()
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ البيانات: {str(e)}")
