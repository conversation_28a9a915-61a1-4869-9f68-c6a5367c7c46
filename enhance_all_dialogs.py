#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لتحسين جميع النوافذ والحوارات في التطبيق
"""

import os
import re

# قائمة ملفات النوافذ التي تحتاج تحسين
dialog_files = [
    "src/ui/dialogs/student_dialog.py",
    "src/ui/dialogs/teacher_dialog.py", 
    "src/ui/dialogs/subject_dialog.py",
    "src/ui/dialogs/class_dialog.py",
    "src/ui/dialogs/fee_dialog.py",
    "src/ui/dialogs/salary_dialog.py",
    "src/ui/dialogs/user_dialog.py",
    "src/ui/dialogs/currency_dialog.py",
    "src/ui/dialogs/grade_dialog.py",
    "src/ui/dialogs/payment_dialog.py",
    "src/ui/dialogs/report_dialog.py",
    "src/ui/dialogs/schedule_dialog.py",
    "src/ui/dialogs/change_password_dialog.py",
    "src/ui/dialogs/permissions_dialog.py",
    "src/ui/dialogs/report_card_dialog.py"
]

def enhance_dialog_file(file_path):
    """تحسين ملف نافذة واحد"""
    try:
        if not os.path.exists(file_path):
            print(f"الملف غير موجود: {file_path}")
            return False
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # التحقق من وجود التحسينات بالفعل
        if "from src.utils.ui_styles import UIStyles" in content:
            print(f"الملف محسن بالفعل: {file_path}")
            return True
        
        # إضافة الاستيرادات المطلوبة
        import_pattern = r"(from src\.models\.[^import]*import [^\n]*)"
        if re.search(import_pattern, content):
            content = re.sub(
                import_pattern,
                r"\1\nfrom src.utils.ui_styles import UIStyles\nfrom src.utils.dialog_enhancer import EnhancedDialog",
                content,
                count=1
            )
        
        # تغيير الوراثة من QDialog إلى EnhancedDialog
        content = re.sub(
            r"class (\w+Dialog)\(QDialog\):",
            r"class \1(EnhancedDialog):",
            content
        )
        
        # استبدال الأنماط القديمة بالأنماط المحسنة
        replacements = [
            # استبدال أنماط الليبلز
            (r'setStyleSheet\(\s*"""[^"]*QLabel\s*\{[^}]*font-size:\s*\d+px[^}]*\}[^"]*"""\s*\)', 
             'setStyleSheet(UIStyles.get_title_style())'),
            
            # استبدال أنماط حقول الإدخال
            (r'setStyleSheet\(\s*"""[^"]*QLineEdit[^"]*"""\s*\)', 
             'setStyleSheet(UIStyles.get_input_style())'),
            
            # استبدال أنماط الأزرار
            (r'setStyleSheet\(\s*"""[^"]*QPushButton\s*\{[^}]*background-color:\s*#27ae60[^}]*\}[^"]*"""\s*\)', 
             'setStyleSheet(UIStyles.get_button_style("#27ae60", "#229954"))'),
            
            (r'setStyleSheet\(\s*"""[^"]*QPushButton\s*\{[^}]*background-color:\s*#95a5a6[^}]*\}[^"]*"""\s*\)', 
             'setStyleSheet(UIStyles.get_button_style("#95a5a6", "#7f8c8d"))'),
            
            # استبدال أنماط صناديق الاختيار
            (r'setStyleSheet\(\s*"""[^"]*QCheckBox[^"]*"""\s*\)', 
             'setStyleSheet(UIStyles.get_checkbox_style())'),
        ]
        
        for pattern, replacement in replacements:
            content = re.sub(pattern, replacement, content, flags=re.DOTALL)
        
        # حفظ الملف المحدث
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"تم تحسين الملف: {file_path}")
        return True
        
    except Exception as e:
        print(f"خطأ في تحسين الملف {file_path}: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("بدء تحسين جميع نوافذ التطبيق...")
    
    enhanced_count = 0
    total_count = len(dialog_files)
    
    for file_path in dialog_files:
        if enhance_dialog_file(file_path):
            enhanced_count += 1
    
    print(f"\nتم الانتهاء من التحسين:")
    print(f"عدد الملفات المحسنة: {enhanced_count}")
    print(f"إجمالي الملفات: {total_count}")
    
    if enhanced_count == total_count:
        print("✅ تم تحسين جميع النوافذ بنجاح!")
    else:
        print(f"⚠️ تم تحسين {enhanced_count} من أصل {total_count} ملف")

if __name__ == "__main__":
    main()
