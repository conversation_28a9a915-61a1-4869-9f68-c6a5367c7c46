# برنامج إدارة المدارس - School Management Pro

## نظرة عامة
برنامج شامل لإدارة المدارس والمؤسسات التعليمية مطور باستخدام Python و PyQt5 مع دعم كامل للغة العربية.

## الميزات الرئيسية
- إدارة الطلاب والمعلمين والموظفين
- إدارة المواد الدراسية والصفوف والفصول
- إدارة الرسوم الدراسية والمدفوعات
- إدارة النتائج الدراسية والدرجات
- نظام تقارير شامل
- إدارة المستخدمين والصلاحيات
- دعم كامل للغة العربية مع RTL
- واجهة مستخدم حديثة ومتجاوبة

## متطلبات النظام
- Python 3.8 أو أحدث
- نظام التشغيل: Windows, macOS, أو Linux

## التثبيت
1. تأكد من تثبيت Python 3.8+
2. قم بتثبيت المتطلبات:
```bash
pip install -r requirements.txt
```

3. (اختياري) إنشاء بيانات وهمية للاختبار:
```bash
python generate_sample_data.py
```

4. اختبار النظام:
```bash
python test_app.py
```

## تشغيل البرنامج
```bash
python main.py
```

## نظام إدارة المستخدمين 👥

تم إضافة نظام شامل لإدارة المستخدمين والصلاحيات:

### المميزات الجديدة:
- ✅ إدارة كاملة للمستخدمين (إضافة، تعديل، حذف)
- ✅ نظام صلاحيات متقدم مع 6 أدوار مختلفة
- ✅ 70+ صلاحية مقسمة إلى 8 فئات
- ✅ واجهة مستخدم حديثة مع دعم كامل للعربية
- ✅ نظام أمان متقدم مع تشفير كلمات المرور
- ✅ قوالب صلاحيات جاهزة لكل دور

### الأدوار المتاحة:
1. **مدير عام** - جميع الصلاحيات
2. **مدير أكاديمي** - إدارة الطلاب والمعلمين والنتائج
3. **مدير مالي** - إدارة الرسوم والتقارير المالية
4. **معلم** - عرض الطلاب وإدخال النتائج
5. **سكرتير** - إدارة البيانات الأساسية
6. **مستخدم عادي** - صلاحيات محدودة

### تسجيل الدخول:
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

### اختبار نظام المستخدمين:
```bash
python test_users_system.py
```

للمزيد من التفاصيل، راجع: [دليل نظام المستخدمين](USERS_SYSTEM_README.md)

## هيكل المشروع

```
SchoolManagementPro/
├── main.py                    # نقطة البداية الرئيسية
├── requirements.txt           # متطلبات المشروع
├── README.md                 # ملف التوثيق
├── generate_sample_data.py   # إنشاء بيانات تجريبية
├── test_app.py              # اختبار النظام
├── data/                    # مجلد قاعدة البيانات
│   └── school.db           # ملف قاعدة البيانات
├── src/                     # الكود المصدري
│   ├── __init__.py
│   ├── database/            # إدارة قاعدة البيانات
│   │   ├── __init__.py
│   │   ├── db_manager.py    # مدير قاعدة البيانات
│   │   └── schema.sql       # هيكل قاعدة البيانات
│   ├── models/              # نماذج البيانات
│   │   ├── __init__.py
│   │   ├── student.py       # نموذج الطالب
│   │   ├── teacher.py       # نموذج المعلم
│   │   ├── subject.py       # نموذج المادة
│   │   └── class_model.py   # نموذج الصف
│   └── ui/                  # واجهة المستخدم
│       ├── __init__.py
│       ├── main_window.py   # النافذة الرئيسية
│       ├── widgets/         # عناصر الواجهة
│       │   ├── __init__.py
│       │   ├── students_widget.py
│       │   ├── teachers_widget.py
│       │   ├── subjects_widget.py
│       │   ├── fees_widget.py
│       │   ├── results_widget.py
│       │   ├── reports_widget.py
│       │   └── settings_widget.py
│       └── dialogs/         # نوافذ الحوار
│           ├── __init__.py
│           ├── student_dialog.py
│           ├── teacher_dialog.py
│           ├── subject_dialog.py
│           ├── fee_dialog.py
│           ├── payment_dialog.py
│           ├── grade_dialog.py
│           ├── report_card_dialog.py
│           └── schedule_dialog.py
└── assets/                  # الموارد والأيقونات
    └── icons/
```

## الوحدات الرئيسية

### 📚 إدارة الطلاب
- **تسجيل الطلاب**: إضافة طلاب جدد مع جميع البيانات الشخصية والأكاديمية
- **البحث والفلترة**: نظام بحث متقدم للعثور على الطلاب بسرعة
- **إدارة الصفوف**: تنظيم الطلاب في صفوف ومراحل دراسية
- **تتبع الحضور**: نظام متكامل لتسجيل الحضور والغياب

### 👨‍🏫 إدارة المعلمين
- **بيانات المعلمين**: إدارة شاملة لمعلومات المعلمين والموظفين
- **نظام الرواتب**: حساب وإدارة الرواتب والبدلات والخصومات
- **الجداول الزمنية**: تنظيم جداول المعلمين والحصص
- **تقييم الأداء**: نظام لتقييم ومتابعة أداء المعلمين

### 📖 إدارة المواد والجداول
- **المواد الدراسية**: إضافة وتعديل المواد مع الساعات المعتمدة
- **ربط المواد**: ربط المواد بالصفوف والمعلمين
- **الجداول الزمنية**: إنشاء وإدارة الجداول الدراسية
- **التوزيع الأكاديمي**: توزيع المناهج والمواد على الفصول

### 💰 إدارة الرسوم والمالية
- **أنواع الرسوم**: إدارة جميع أنواع الرسوم الدراسية
- **المدفوعات**: تسجيل ومتابعة المدفوعات
- **الخصومات**: نظام الخصومات والإعفاءات
- **التقارير المالية**: تقارير شاملة عن الوضع المالي

### 📊 إدارة النتائج والدرجات
- **إدخال الدرجات**: واجهة سهلة لإدخال درجات جميع أنواع التقييمات
- **حساب المعدلات**: حساب تلقائي للمعدلات والتقديرات
- **كشوف الدرجات**: إنشاء كشوف درجات فردية وجماعية
- **الإحصائيات**: تحليل الأداء الأكاديمي والإحصائيات

### 📈 نظام التقارير
- **تقارير الطلاب**: تقارير شاملة عن الطلاب والحضور
- **تقارير المعلمين**: تقارير الرواتب والأداء
- **التقارير المالية**: تحليل مالي مفصل
- **تقارير الأداء**: إحصائيات الأداء الأكاديمي

### ⚙️ إدارة الإعدادات
- **إعدادات النظام**: تخصيص إعدادات النظام العامة
- **معلومات المدرسة**: إدارة بيانات المدرسة الأساسية
- **النسخ الاحتياطي**: نظام النسخ الاحتياطي والاستعادة
- **المظهر**: تخصيص مظهر النظام واللغة

## التقنيات المستخدمة

- **Python 3.8+**: لغة البرمجة الأساسية
- **PyQt5**: مكتبة واجهة المستخدم الرسومية
- **SQLite**: قاعدة البيانات المحلية المدمجة
- **JSON**: تخزين الإعدادات والتكوينات
- **CSS**: تنسيق وتصميم الواجهة

## المميزات التقنية

- **دعم RTL**: دعم كامل للغة العربية مع اتجاه النص من اليمين لليسار
- **واجهة حديثة**: تصميم عصري ومتجاوب
- **أمان البيانات**: حماية البيانات مع نظام النسخ الاحتياطي
- **سهولة الاستخدام**: واجهة بديهية وسهلة التعلم
- **قابلية التوسع**: هيكل مرن يسمح بإضافة ميزات جديدة

## الاستخدام

### البدء السريع
1. شغل البرنامج باستخدام `python main.py`
2. ستظهر النافذة الرئيسية مع جميع الوحدات
3. ابدأ بإدخال بيانات المدرسة من تبويب "الإعدادات"
4. أضف الصفوف والمواد الدراسية
5. سجل المعلمين والطلاب
6. ابدأ في استخدام النظام

### نصائح للاستخدام
- استخدم البيانات التجريبية للتعرف على النظام
- قم بعمل نسخة احتياطية دورية من البيانات
- استخدم نظام البحث للعثور على البيانات بسرعة
- راجع التقارير بانتظام لمتابعة الأداء

## الدعم والمساعدة

### المشاكل الشائعة
- **مشكلة في قاعدة البيانات**: تأكد من وجود مجلد `data` ووجود صلاحيات الكتابة
- **خطأ في الواجهة**: تأكد من تثبيت PyQt5 بشكل صحيح
- **مشكلة في الخطوط العربية**: تأكد من وجود خطوط عربية في النظام

### طلب المساعدة
- راجع ملف التوثيق الكامل
- تحقق من الأمثلة والبيانات التجريبية
- تواصل مع فريق الدعم للمساعدة التقنية

## التطوير والمساهمة

### إضافة ميزات جديدة
1. ادرس هيكل المشروع الحالي
2. أنشئ فرع جديد للميزة
3. اتبع نمط التطوير المستخدم
4. اختبر الميزة بدقة
5. أرسل طلب دمج مع الوثائق

### معايير التطوير
- استخدم التعليقات باللغة العربية
- اتبع معايير PEP 8 للكود
- اكتب اختبارات للميزات الجديدة
- وثق التغييرات والإضافات

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. يمكنك استخدامه وتعديله بحرية مع الحفاظ على حقوق المؤلف.

---

**نظام إدارة المدارس - حل شامل ومتكامل للمؤسسات التعليمية**

## تشغيل البرنامج
```bash
python run.py
```

### بيانات الدخول الافتراضية
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

## هيكل المشروع
```
SchoolManagementPro/
├── src/                    # الكود المصدري الرئيسي
├── ui/                     # ملفات واجهة المستخدم
├── db/                     # قاعدة البيانات والنماذج
├── resources/              # الأيقونات والصور والخطوط
├── reports/                # قوالب التقارير
├── tests/                  # اختبارات الوحدة
├── docs/                   # التوثيق
├── requirements.txt        # متطلبات Python
└── README.md              # هذا الملف
```

## الترخيص
هذا البرنامج مطور لأغراض تعليمية وإدارية.

## استكشاف الأخطاء وإصلاحها

إذا واجهت أي مشاكل، راجع ملف `TROUBLESHOOTING.md` للحلول الشائعة.

### مشاكل شائعة:
- **خطأ في تسجيل الدخول:** تم إصلاحه في الإصدار الحالي
- **مشاكل في الخطوط العربية:** تأكد من تثبيت خطوط عربية على النظام
- **مشاكل في قاعدة البيانات:** احذف مجلد `db` وأعد تشغيل البرنامج

## الملفات المهمة
- `run.py` - ملف تشغيل البرنامج
- `test_app.py` - اختبار النظام
- `generate_sample_data.py` - إنشاء بيانات وهمية
- `TROUBLESHOOTING.md` - دليل حل المشاكل
- `docs/user_guide.md` - دليل المستخدم الشامل

## المطور
تم تطوير هذا البرنامج باستخدام Augment Agent
