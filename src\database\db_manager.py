#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات
يحتوي على جميع العمليات المتعلقة بقاعدة البيانات
"""

import sqlite3
import os
from datetime import datetime
from pathlib import Path
from src.utils.config import Config


class DatabaseManager:
    """فئة إدارة قاعدة البيانات"""
    
    def __init__(self):
        self.db_path = Config.DATABASE_PATH
        self.connection = None
        
    def get_connection(self):
        """الحصول على اتصال قاعدة البيانات"""
        if self.connection is None:
            Config.ensure_directories()
            self.connection = sqlite3.connect(
                str(self.db_path),
                check_same_thread=False
            )
            self.connection.row_factory = sqlite3.Row
            # تفعيل دعم المفاتيح الخارجية
            self.connection.execute("PRAGMA foreign_keys = ON")
        return self.connection
    
    def close_connection(self):
        """إغلاق اتصال قاعدة البيانات"""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def execute_query(self, query, params=None):
        """تنفيذ استعلام قاعدة البيانات"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            conn.commit()
            return cursor
        except Exception as e:
            conn.rollback()
            raise e
    
    def fetch_all(self, query, params=None):
        """جلب جميع النتائج من الاستعلام"""
        cursor = self.execute_query(query, params)
        rows = cursor.fetchall()
        # تحويل sqlite3.Row إلى قواميس
        return [dict(row) for row in rows] if rows else []

    def fetch_one(self, query, params=None):
        """جلب نتيجة واحدة من الاستعلام"""
        cursor = self.execute_query(query, params)
        row = cursor.fetchone()
        # تحويل sqlite3.Row إلى قاموس
        return dict(row) if row else None
    
    def initialize_database(self):
        """تهيئة قاعدة البيانات وإنشاء الجداول"""
        try:
            self.create_tables()
            self.create_default_data()
            print("تم إنشاء قاعدة البيانات بنجاح")
        except Exception as e:
            print(f"خطأ في إنشاء قاعدة البيانات: {e}")
            raise e
    
    def create_tables(self):
        """إنشاء جميع الجداول المطلوبة"""
        
        # جدول المستخدمين
        users_table = """
        CREATE TABLE IF NOT EXISTS users (
            user_id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            first_name TEXT NOT NULL,
            last_name TEXT NOT NULL,
            full_name TEXT,
            email TEXT,
            role TEXT NOT NULL DEFAULT 'user',
            permissions TEXT,
            is_active BOOLEAN DEFAULT 1,
            last_login DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        # جدول الصفوف الدراسية
        classes_table = """
        CREATE TABLE IF NOT EXISTS classes (
            class_id INTEGER PRIMARY KEY AUTOINCREMENT,
            class_name TEXT NOT NULL,
            grade_level TEXT NOT NULL,
            academic_year TEXT NOT NULL,
            capacity INTEGER DEFAULT 30,
            description TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        # جدول الطلاب
        students_table = """
        CREATE TABLE IF NOT EXISTS students (
            student_id INTEGER PRIMARY KEY AUTOINCREMENT,
            student_number TEXT UNIQUE NOT NULL,
            first_name TEXT NOT NULL,
            last_name TEXT NOT NULL,
            date_of_birth DATE NOT NULL,
            gender TEXT NOT NULL,
            national_id TEXT UNIQUE,
            address TEXT,
            phone TEXT,
            parent_name TEXT NOT NULL,
            parent_phone TEXT NOT NULL,
            parent_email TEXT,
            class_id INTEGER,
            enrollment_date DATE DEFAULT CURRENT_DATE,
            status TEXT DEFAULT 'active',
            photo_path TEXT,
            notes TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (class_id) REFERENCES classes (class_id)
        )
        """
        
        # جدول المعلمين والموظفين
        teachers_table = """
        CREATE TABLE IF NOT EXISTS teachers (
            teacher_id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_number TEXT UNIQUE NOT NULL,
            first_name TEXT NOT NULL,
            last_name TEXT NOT NULL,
            date_of_birth DATE,
            gender TEXT,
            national_id TEXT UNIQUE,
            address TEXT,
            phone TEXT NOT NULL,
            email TEXT,
            specialization TEXT,
            qualification TEXT,
            hire_date DATE NOT NULL,
            salary DECIMAL(10,2),
            currency_id INTEGER DEFAULT 1,
            position TEXT NOT NULL,
            department TEXT,
            status TEXT DEFAULT 'active',
            photo_path TEXT,
            notes TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (currency_id) REFERENCES currencies (currency_id)
        )
        """
        
        # جدول المواد الدراسية
        subjects_table = """
        CREATE TABLE IF NOT EXISTS subjects (
            subject_id INTEGER PRIMARY KEY AUTOINCREMENT,
            subject_code TEXT UNIQUE NOT NULL,
            subject_name TEXT NOT NULL,
            description TEXT,
            credit_hours INTEGER DEFAULT 1,
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        # جدول ربط المواد بالصفوف والمعلمين
        class_subjects_table = """
        CREATE TABLE IF NOT EXISTS class_subjects (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            class_id INTEGER NOT NULL,
            subject_id INTEGER NOT NULL,
            teacher_id INTEGER NOT NULL,
            academic_year TEXT NOT NULL,
            semester TEXT NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (class_id) REFERENCES classes (class_id),
            FOREIGN KEY (subject_id) REFERENCES subjects (subject_id),
            FOREIGN KEY (teacher_id) REFERENCES teachers (teacher_id),
            UNIQUE(class_id, subject_id, academic_year, semester)
        )
        """
        
        # جدول الرسوم الدراسية
        fees_table = """
        CREATE TABLE IF NOT EXISTS fees (
            fee_id INTEGER PRIMARY KEY AUTOINCREMENT,
            student_id INTEGER NOT NULL,
            fee_type TEXT NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            due_date DATE NOT NULL,
            status TEXT DEFAULT 'pending',
            payment_date DATE,
            payment_method TEXT,
            discount DECIMAL(10,2) DEFAULT 0,
            notes TEXT,
            academic_year TEXT NOT NULL,
            semester TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES students (student_id)
        )
        """

        # جدول النتائج والدرجات
        results_table = """
        CREATE TABLE IF NOT EXISTS results (
            result_id INTEGER PRIMARY KEY AUTOINCREMENT,
            student_id INTEGER NOT NULL,
            subject_id INTEGER NOT NULL,
            class_id INTEGER NOT NULL,
            exam_type TEXT NOT NULL,
            score DECIMAL(5,2) NOT NULL,
            max_score DECIMAL(5,2) NOT NULL,
            percentage DECIMAL(5,2) GENERATED ALWAYS AS ((score * 100.0) / max_score) STORED,
            exam_date DATE NOT NULL,
            academic_year TEXT NOT NULL,
            semester TEXT NOT NULL,
            notes TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES students (student_id),
            FOREIGN KEY (subject_id) REFERENCES subjects (subject_id),
            FOREIGN KEY (class_id) REFERENCES classes (class_id)
        )
        """

        # جدول الحضور والغياب
        attendance_table = """
        CREATE TABLE IF NOT EXISTS attendance (
            attendance_id INTEGER PRIMARY KEY AUTOINCREMENT,
            student_id INTEGER NOT NULL,
            class_id INTEGER NOT NULL,
            date DATE NOT NULL,
            status TEXT NOT NULL DEFAULT 'present',
            notes TEXT,
            recorded_by INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES students (student_id),
            FOREIGN KEY (class_id) REFERENCES classes (class_id),
            FOREIGN KEY (recorded_by) REFERENCES users (user_id),
            UNIQUE(student_id, date)
        )
        """

        # جدول العملات
        currencies_table = """
        CREATE TABLE IF NOT EXISTS currencies (
            currency_id INTEGER PRIMARY KEY AUTOINCREMENT,
            currency_code TEXT UNIQUE NOT NULL,
            currency_name TEXT NOT NULL,
            symbol TEXT NOT NULL,
            exchange_rate DECIMAL(10,4) DEFAULT 1.0,
            is_default BOOLEAN DEFAULT 0,
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        """

        # جدول الرواتب
        salaries_table = """
        CREATE TABLE IF NOT EXISTS salaries (
            salary_id INTEGER PRIMARY KEY AUTOINCREMENT,
            teacher_id INTEGER NOT NULL,
            month INTEGER NOT NULL,
            year INTEGER NOT NULL,
            basic_salary DECIMAL(10,2) NOT NULL,
            allowances DECIMAL(10,2) DEFAULT 0,
            deductions DECIMAL(10,2) DEFAULT 0,
            overtime DECIMAL(10,2) DEFAULT 0,
            total_salary DECIMAL(10,2) NOT NULL,
            currency_id INTEGER DEFAULT 1,
            payment_date DATE,
            status TEXT DEFAULT 'pending',
            notes TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (teacher_id) REFERENCES teachers (teacher_id),
            FOREIGN KEY (currency_id) REFERENCES currencies (currency_id),
            UNIQUE(teacher_id, month, year)
        )
        """

        # جدول الإعدادات
        settings_table = """
        CREATE TABLE IF NOT EXISTS settings (
            setting_id INTEGER PRIMARY KEY AUTOINCREMENT,
            setting_key TEXT UNIQUE NOT NULL,
            setting_value TEXT,
            description TEXT,
            category TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        """

        # جدول سجل النشاطات
        activity_log_table = """
        CREATE TABLE IF NOT EXISTS activity_log (
            log_id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            action TEXT NOT NULL,
            table_name TEXT,
            record_id INTEGER,
            old_values TEXT,
            new_values TEXT,
            ip_address TEXT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (user_id)
        )
        """

        # تنفيذ إنشاء الجداول
        tables = [
            users_table,
            currencies_table,
            classes_table,
            students_table,
            teachers_table,
            subjects_table,
            class_subjects_table,
            fees_table,
            results_table,
            attendance_table,
            salaries_table,
            settings_table,
            activity_log_table
        ]

        for table in tables:
            self.execute_query(table)

        # تحديث هيكل الجداول الموجودة
        self.update_existing_tables()
    
    def insert_default_currencies(self):
        """إدراج العملات الافتراضية"""
        try:
            # التحقق من وجود عملات
            existing_currencies = self.fetch_one("SELECT COUNT(*) as count FROM currencies")
            if existing_currencies and existing_currencies['count'] > 0:
                return

            # العملات الافتراضية
            default_currencies = [
                ('SAR', 'الريال السعودي', 'ر.س', 1.0, 1, 1),
                ('USD', 'الدولار الأمريكي', '$', 3.75, 0, 1),
                ('EUR', 'اليورو', '€', 4.10, 0, 1),
                ('GBP', 'الجنيه الإسترليني', '£', 4.70, 0, 1),
                ('AED', 'الدرهم الإماراتي', 'د.إ', 1.02, 0, 1),
                ('KWD', 'الدينار الكويتي', 'د.ك', 12.30, 0, 1),
                ('QAR', 'الريال القطري', 'ر.ق', 1.03, 0, 1),
                ('BHD', 'الدينار البحريني', 'د.ب', 9.95, 0, 1),
                ('OMR', 'الريال العماني', 'ر.ع', 9.75, 0, 1),
                ('JOD', 'الدينار الأردني', 'د.أ', 5.30, 0, 1),
                ('EGP', 'الجنيه المصري', 'ج.م', 0.12, 0, 1)
            ]

            for currency in default_currencies:
                self.execute_query("""
                    INSERT INTO currencies (currency_code, currency_name, symbol,
                                          exchange_rate, is_default, is_active)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, currency)

        except Exception as e:
            print(f"خطأ في إدراج العملات الافتراضية: {e}")

    def update_existing_tables(self):
        """تحديث هيكل الجداول الموجودة لإضافة الأعمدة المفقودة"""
        try:
            # التحقق من وجود عمود currency_id في جدول teachers
            cursor = self.connection.cursor()
            cursor.execute("PRAGMA table_info(teachers)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'currency_id' not in columns:
                print("إضافة عمود currency_id إلى جدول المعلمين...")
                self.execute_query("ALTER TABLE teachers ADD COLUMN currency_id INTEGER DEFAULT 1")
                print("تم إضافة عمود currency_id بنجاح")

            # التحقق من وجود عمود currency_id في جدول salaries
            cursor.execute("PRAGMA table_info(salaries)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'currency_id' not in columns:
                print("إضافة عمود currency_id إلى جدول الرواتب...")
                self.execute_query("ALTER TABLE salaries ADD COLUMN currency_id INTEGER DEFAULT 1")
                print("تم إضافة عمود currency_id إلى جدول الرواتب بنجاح")

            # التحقق من وجود عمود is_default في جدول currencies
            cursor.execute("PRAGMA table_info(currencies)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'is_default' not in columns:
                print("إضافة عمود is_default إلى جدول العملات...")
                self.execute_query("ALTER TABLE currencies ADD COLUMN is_default BOOLEAN DEFAULT 0")
                # تعيين الريال السعودي كعملة افتراضية
                self.execute_query("UPDATE currencies SET is_default = 1 WHERE currency_code = 'SAR'")
                print("تم إضافة عمود is_default إلى جدول العملات بنجاح")

        except Exception as e:
            print(f"خطأ في تحديث هيكل الجداول: {e}")

    def create_default_data(self):
        """إنشاء البيانات الافتراضية"""
        # إدراج العملات الافتراضية
        self.insert_default_currencies()

        # إنشاء مستخدم مدير افتراضي
        admin_exists = self.fetch_one(
            "SELECT user_id FROM users WHERE username = ?",
            ('admin',)
        )
        
        if not admin_exists:
            import bcrypt
            password = "admin123"
            password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())
            
            self.execute_query("""
                INSERT INTO users (username, password_hash, full_name, role, permissions)
                VALUES (?, ?, ?, ?, ?)
            """, (
                'admin',
                password_hash.decode('utf-8'),
                'مدير النظام',
                'admin',
                ','.join(Config.DEFAULT_ROLE_PERMISSIONS['admin'])
            ))
            
            print("تم إنشاء حساب المدير الافتراضي:")
            print("اسم المستخدم: admin")
            print("كلمة المرور: admin123")

    def update_database_schema(self):
        """تحديث هيكل قاعدة البيانات"""
        try:
            cursor = self.connection.cursor()

            # إضافة عمود percentage إلى جدول results إذا لم يكن موجوداً
            cursor.execute("PRAGMA table_info(results)")
            results_columns = [column[1] for column in cursor.fetchall()]

            if 'percentage' not in results_columns:
                print("إضافة عمود percentage إلى جدول results...")
                cursor.execute("ALTER TABLE results ADD COLUMN percentage DECIMAL(5,2)")

                # تحديث القيم الموجودة
                cursor.execute("""
                    UPDATE results
                    SET percentage = (score * 100.0) / max_score
                    WHERE max_score > 0
                """)

                self.connection.commit()
                print("تم تحديث جدول results بنجاح")

            # التحقق من أعمدة جدول المستخدمين
            cursor.execute("PRAGMA table_info(users)")
            users_columns = [column[1] for column in cursor.fetchall()]

            if 'first_name' not in users_columns:
                print("إضافة عمود first_name إلى جدول users...")
                cursor.execute("ALTER TABLE users ADD COLUMN first_name TEXT")
                self.connection.commit()

            if 'last_name' not in users_columns:
                print("إضافة عمود last_name إلى جدول users...")
                cursor.execute("ALTER TABLE users ADD COLUMN last_name TEXT")
                self.connection.commit()

            if 'full_name' not in users_columns:
                print("إضافة عمود full_name إلى جدول users...")
                cursor.execute("ALTER TABLE users ADD COLUMN full_name TEXT")
                self.connection.commit()

            return True
        except Exception as e:
            print(f"خطأ في تحديث هيكل قاعدة البيانات: {e}")
            return False
