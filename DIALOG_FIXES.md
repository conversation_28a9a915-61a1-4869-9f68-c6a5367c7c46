# إصلاح مشكلة تداخل النوافذ

## المشكلة
كانت هناك مشكلة في تداخل النوافذ المودالية (Modal Dialogs) حيث تظهر نافذة فوق أخرى بشكل غير صحيح، مما يسبب:
- عدم وضوح النافذة المطلوبة
- صعوبة في التفاعل مع النوافذ
- تجربة مستخدم سيئة

## الحلول المطبقة

### 1. إنشاء أدوات مساعدة موحدة (`src/utils/dialog_utils.py`)
- دالة `setup_dialog_window()` لإعداد النوافذ بشكل موحد
- دالة `apply_dialog_styles()` لتطبيق أنماط موحدة
- دالة `center_dialog_on_parent()` لتوسيط النوافذ
- دالة `ensure_dialog_visibility()` لضمان ظهور النوافذ في المقدمة
- فئة `BaseDialog` كفئة أساسية للنوافذ

### 2. إصلاح النوافذ المتأثرة

#### نافذة الصفوف (`src/ui/dialogs/class_dialog.py`)
- إضافة `Qt.WindowStaysOnTopHint` لضمان البقاء في المقدمة
- تطبيق الأنماط الموحدة
- إضافة دوال `showEvent()` و `exec_()` للتحكم في الظهور

#### نافذة المستخدمين (`src/ui/dialogs/user_dialog.py`)
- نفس الإصلاحات المطبقة على نافذة الصفوف
- توسيط النافذة على النافذة الأب

#### نافذة العملات (`src/ui/dialogs/currency_dialog.py`)
- تطبيق نفس الإصلاحات
- ضمان الظهور الصحيح

#### نافذة التقارير (`src/ui/dialogs/report_dialog.py`)
- إصلاح مشاكل الظهور
- تطبيق الأنماط الموحدة

### 3. الميزات الجديدة

#### إعدادات النوافذ المحسنة
```python
# إعدادات النافذة لضمان الظهور في المقدمة
self.setWindowFlags(
    Qt.Dialog | 
    Qt.WindowTitleHint | 
    Qt.WindowCloseButtonHint |
    Qt.WindowStaysOnTopHint
)
```

#### ضمان الظهور في المقدمة
```python
def ensure_dialog_visibility(dialog):
    """ضمان رؤية النافذة وعدم اختفائها خلف النوافذ الأخرى"""
    if not dialog.isVisible():
        dialog.show()
    
    dialog.raise_()
    dialog.activateWindow()
    dialog.setFocus()
    fix_dialog_z_order()
```

#### توسيط النوافذ
```python
def center_dialog_on_parent(dialog, parent=None):
    """توسيط النافذة على النافذة الأب أو الشاشة"""
    # توسيط تلقائي على النافذة الأب أو الشاشة
```

### 4. الأنماط الموحدة
- أنماط CSS موحدة لجميع النوافذ
- تحسين المظهر العام
- دعم اللغة العربية بشكل أفضل

## النتائج
- ✅ لا يوجد تداخل في النوافذ
- ✅ النوافذ تظهر في المقدمة دائماً
- ✅ تجربة مستخدم محسنة
- ✅ مظهر موحد لجميع النوافذ
- ✅ توسيط تلقائي للنوافذ

## كيفية الاستخدام

### للنوافذ الجديدة
```python
from src.utils.dialog_utils import BaseDialog

class MyDialog(BaseDialog):
    def __init__(self, parent=None):
        super().__init__("عنوان النافذة", 500, 400, parent)
        self.setup_ui()
    
    def setup_ui(self):
        # إعداد واجهة المستخدم
        pass

# عرض النافذة
dialog = MyDialog(parent_window)
result = dialog.show_safely()
```

### للنوافذ الموجودة
```python
from src.utils.dialog_utils import ensure_dialog_visibility

def showEvent(self, event):
    super().showEvent(event)
    ensure_dialog_visibility(self)

def exec_(self):
    ensure_dialog_visibility(self)
    return super().exec_()
```

## ملاحظات مهمة
- جميع النوافذ الآن تستخدم `Qt.WindowStaysOnTopHint`
- النوافذ تتوسط تلقائياً على النافذة الأب
- الأنماط موحدة عبر التطبيق
- دعم كامل للغة العربية (RTL)

## اختبار الإصلاحات
1. تشغيل التطبيق
2. فتح نافذة المستخدمين
3. محاولة فتح نافذة إضافة صف جديد
4. التأكد من عدم وجود تداخل
5. التأكد من ظهور النافذة الجديدة في المقدمة
