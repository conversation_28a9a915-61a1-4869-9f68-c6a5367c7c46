#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نموذج المعلمين
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.models.teacher import Teacher


def test_teacher_methods():
    """اختبار دوال نموذج المعلمين"""
    print("=== اختبار نموذج المعلمين ===")
    
    try:
        teacher_model = Teacher()
        
        # اختبار وجود الدوال
        print("اختبار وجود الدوال:")
        
        # get_all_teachers
        if hasattr(teacher_model, 'get_all_teachers'):
            print("✓ get_all_teachers موجودة")
            teachers = teacher_model.get_all_teachers()
            print(f"✓ تم جلب {len(teachers)} معلم")
        else:
            print("✗ get_all_teachers غير موجودة")
        
        # get_teachers_statistics
        if hasattr(teacher_model, 'get_teachers_statistics'):
            print("✓ get_teachers_statistics موجودة")
            stats = teacher_model.get_teachers_statistics()
            print(f"✓ إحصائيات المعلمين: {stats.get('active_teachers', 0)} نشط")
        else:
            print("✗ get_teachers_statistics غير موجودة")
        
        # get_active_teachers
        if hasattr(teacher_model, 'get_active_teachers'):
            print("✓ get_active_teachers موجودة")
            active_teachers = teacher_model.get_active_teachers()
            print(f"✓ المعلمون النشطون: {len(active_teachers)}")
        else:
            print("✗ get_active_teachers غير موجودة")
        
        # search_teachers
        if hasattr(teacher_model, 'search_teachers'):
            print("✓ search_teachers موجودة")
            search_results = teacher_model.search_teachers("test")
            print(f"✓ نتائج البحث: {len(search_results)}")
        else:
            print("✗ search_teachers غير موجودة")
        
        print("\n=== جميع الدوال تعمل بنجاح! ===")
        return True
        
    except Exception as e:
        print(f"✗ خطأ في اختبار نموذج المعلمين: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    test_teacher_methods()
