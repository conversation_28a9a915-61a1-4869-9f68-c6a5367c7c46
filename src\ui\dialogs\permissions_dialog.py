#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إدارة الصلاحيات
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTreeWidget,
                             QTreeWidgetItem, QPushButton, QLabel, QFrame, 
                             QMessageBox, QCheckBox, QGroupBox, QSplitter,
                             QTextEdit, QTabWidget, QWidget)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

from src.models.user import User
from src.utils.config import Config


class PermissionsDialog(QDialog):
    """نافذة إدارة الصلاحيات"""
    
    # إشارة تحديث الصلاحيات
    permissions_updated = pyqtSignal()
    
    def __init__(self, user_id=None, username="", parent=None):
        super().__init__(parent)
        self.user_id = user_id
        self.username = username
        self.user_model = User()
        
        self.setup_ui()
        self.setup_connections()
        self.load_user_permissions()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة
        title = f"إدارة صلاحيات المستخدم - {self.username}"
        self.setWindowTitle(title)
        self.setFixedSize(800, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # عنوان النافذة
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # علامات التبويب
        self.tab_widget = QTabWidget()
        
        # تبويب الصلاحيات الفردية
        self.individual_tab = QWidget()
        self.setup_individual_permissions_tab()
        self.tab_widget.addTab(self.individual_tab, "الصلاحيات الفردية")
        
        # تبويب الصلاحيات حسب المجموعة
        self.group_tab = QWidget()
        self.setup_group_permissions_tab()
        self.tab_widget.addTab(self.group_tab, "صلاحيات المجموعات")
        
        # تبويب الصلاحيات المتقدمة
        self.advanced_tab = QWidget()
        self.setup_advanced_permissions_tab()
        self.tab_widget.addTab(self.advanced_tab, "الصلاحيات المتقدمة")
        
        main_layout.addWidget(self.tab_widget)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        self.select_all_button = QPushButton("تحديد الكل")
        self.select_all_button.setStyleSheet(self.get_button_style("#3498db"))
        
        self.clear_all_button = QPushButton("إلغاء تحديد الكل")
        self.clear_all_button.setStyleSheet(self.get_button_style("#e67e22"))
        
        self.apply_template_button = QPushButton("تطبيق قالب")
        self.apply_template_button.setStyleSheet(self.get_button_style("#9b59b6"))
        
        self.save_button = QPushButton("حفظ الصلاحيات")
        self.save_button.setStyleSheet(self.get_button_style("#27ae60"))
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setStyleSheet(self.get_button_style("#95a5a6"))
        
        buttons_layout.addWidget(self.select_all_button)
        buttons_layout.addWidget(self.clear_all_button)
        buttons_layout.addWidget(self.apply_template_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.save_button)
        
        main_layout.addLayout(buttons_layout)
        
    def setup_individual_permissions_tab(self):
        """إعداد تبويب الصلاحيات الفردية"""
        layout = QVBoxLayout(self.individual_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)
        
        # تقسيم الشاشة
        splitter = QSplitter(Qt.Horizontal)
        
        # شجرة الصلاحيات
        self.permissions_tree = QTreeWidget()
        self.setup_permissions_tree()
        splitter.addWidget(self.permissions_tree)
        
        # لوحة تفاصيل الصلاحية
        details_frame = self.create_permission_details_panel()
        splitter.addWidget(details_frame)
        
        # تعيين نسب التقسيم
        splitter.setSizes([500, 300])
        
        layout.addWidget(splitter)
        
    def setup_permissions_tree(self):
        """إعداد شجرة الصلاحيات"""
        self.permissions_tree.setHeaderLabels(["الصلاحية", "الوصف"])
        self.permissions_tree.setRootIsDecorated(True)
        
        # تجميع الصلاحيات حسب الفئة
        permission_groups = {
            "إدارة الطلاب": ["view_students", "add_students", "edit_students", "delete_students"],
            "إدارة المعلمين": ["view_teachers", "add_teachers", "edit_teachers", "delete_teachers"],
            "إدارة المواد": ["view_subjects", "add_subjects", "edit_subjects", "delete_subjects"],
            "إدارة الرسوم": ["view_fees", "add_fees", "edit_fees", "delete_fees"],
            "إدارة النتائج": ["view_results", "add_results", "edit_results", "delete_results"],
            "التقارير": ["view_reports", "generate_reports", "export_reports"],
            "إدارة النظام": ["manage_users", "manage_permissions", "system_settings", "backup_restore"],
            "صلاحيات متقدمة": ["manage_all_users", "delete_users", "system_admin"]
        }
        
        self.permission_items = {}
        
        for group_name, permissions in permission_groups.items():
            # إنشاء عنصر المجموعة
            group_item = QTreeWidgetItem(self.permissions_tree)
            group_item.setText(0, group_name)
            group_item.setFlags(group_item.flags() | Qt.ItemIsTristate | Qt.ItemIsUserCheckable)
            group_item.setCheckState(0, Qt.Unchecked)
            
            # إضافة الصلاحيات للمجموعة
            for permission in permissions:
                if permission in Config.PERMISSIONS:
                    permission_item = QTreeWidgetItem(group_item)
                    permission_item.setText(0, Config.PERMISSIONS[permission])
                    permission_item.setText(1, self.get_permission_description(permission))
                    permission_item.setFlags(permission_item.flags() | Qt.ItemIsUserCheckable)
                    permission_item.setCheckState(0, Qt.Unchecked)
                    permission_item.setData(0, Qt.UserRole, permission)
                    
                    self.permission_items[permission] = permission_item
        
        # توسيع جميع المجموعات
        self.permissions_tree.expandAll()
        
        # تطبيق الأنماط
        self.permissions_tree.setStyleSheet("""
            QTreeWidget {
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
            }
            QTreeWidget::item {
                padding: 5px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTreeWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        
    def create_permission_details_panel(self):
        """إنشاء لوحة تفاصيل الصلاحية"""
        details_frame = QFrame()
        details_frame.setFrameStyle(QFrame.StyledPanel)
        details_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
        """)
        
        layout = QVBoxLayout(details_frame)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # عنوان اللوحة
        title_label = QLabel("تفاصيل الصلاحية")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
                border-bottom: 2px solid #3498db;
            }
        """)
        layout.addWidget(title_label)
        
        # تفاصيل الصلاحية
        self.permission_details_label = QLabel("اختر صلاحية لعرض التفاصيل")
        self.permission_details_label.setWordWrap(True)
        self.permission_details_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-size: 12px;
                padding: 10px;
            }
        """)
        layout.addWidget(self.permission_details_label)
        
        layout.addStretch()
        
        return details_frame
        
    def setup_group_permissions_tab(self):
        """إعداد تبويب صلاحيات المجموعات"""
        layout = QVBoxLayout(self.group_tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # قوالب الصلاحيات
        templates_group = QGroupBox("قوالب الصلاحيات")
        templates_group.setStyleSheet(self.get_group_style())
        templates_layout = QVBoxLayout(templates_group)
        
        # قالب المدير العام
        admin_button = QPushButton("مدير عام - جميع الصلاحيات")
        admin_button.clicked.connect(lambda: self.apply_permission_template("admin"))
        admin_button.setStyleSheet(self.get_template_button_style("#e74c3c"))
        templates_layout.addWidget(admin_button)
        
        # قالب المدير الأكاديمي
        academic_button = QPushButton("مدير أكاديمي - إدارة الطلاب والمعلمين والنتائج")
        academic_button.clicked.connect(lambda: self.apply_permission_template("academic"))
        academic_button.setStyleSheet(self.get_template_button_style("#3498db"))
        templates_layout.addWidget(academic_button)
        
        # قالب المدير المالي
        financial_button = QPushButton("مدير مالي - إدارة الرسوم والتقارير المالية")
        financial_button.clicked.connect(lambda: self.apply_permission_template("financial"))
        financial_button.setStyleSheet(self.get_template_button_style("#27ae60"))
        templates_layout.addWidget(financial_button)
        
        # قالب المعلم
        teacher_button = QPushButton("معلم - عرض الطلاب وإدخال النتائج")
        teacher_button.clicked.connect(lambda: self.apply_permission_template("teacher"))
        teacher_button.setStyleSheet(self.get_template_button_style("#f39c12"))
        templates_layout.addWidget(teacher_button)
        
        # قالب المستخدم العادي
        user_button = QPushButton("مستخدم عادي - صلاحيات محدودة")
        user_button.clicked.connect(lambda: self.apply_permission_template("user"))
        user_button.setStyleSheet(self.get_template_button_style("#95a5a6"))
        templates_layout.addWidget(user_button)
        
        layout.addWidget(templates_group)
        layout.addStretch()
        
    def setup_advanced_permissions_tab(self):
        """إعداد تبويب الصلاحيات المتقدمة"""
        layout = QVBoxLayout(self.advanced_tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # تحذير الصلاحيات المتقدمة
        warning_frame = QFrame()
        warning_frame.setStyleSheet("""
            QFrame {
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 5px;
                padding: 15px;
            }
        """)
        
        warning_layout = QVBoxLayout(warning_frame)
        warning_title = QLabel("⚠️ تحذير - صلاحيات متقدمة")
        warning_title.setStyleSheet("font-weight: bold; color: #856404; font-size: 14px;")
        warning_layout.addWidget(warning_title)
        
        warning_text = QLabel(
            "هذه الصلاحيات تمنح المستخدم تحكماً كاملاً في النظام. "
            "يرجى توخي الحذر عند منح هذه الصلاحيات."
        )
        warning_text.setWordWrap(True)
        warning_text.setStyleSheet("color: #856404;")
        warning_layout.addWidget(warning_text)
        
        layout.addWidget(warning_frame)
        
        # الصلاحيات المتقدمة
        advanced_group = QGroupBox("الصلاحيات المتقدمة")
        advanced_group.setStyleSheet(self.get_group_style())
        advanced_layout = QVBoxLayout(advanced_group)
        
        self.advanced_checkboxes = {}
        advanced_permissions = [
            ("manage_all_users", "إدارة جميع المستخدمين"),
            ("delete_users", "حذف المستخدمين"),
            ("system_admin", "مدير النظام"),
            ("backup_restore", "النسخ الاحتياطي والاستعادة"),
            ("system_settings", "إعدادات النظام"),
            ("database_access", "الوصول المباشر لقاعدة البيانات")
        ]
        
        for permission_key, permission_name in advanced_permissions:
            checkbox = QCheckBox(permission_name)
            checkbox.setObjectName(permission_key)
            checkbox.setStyleSheet("""
                QCheckBox {
                    font-size: 14px;
                    color: #2c3e50;
                    padding: 5px;
                }
                QCheckBox::indicator {
                    width: 18px;
                    height: 18px;
                }
                QCheckBox::indicator:checked {
                    background-color: #e74c3c;
                    border: 2px solid #e74c3c;
                }
            """)
            self.advanced_checkboxes[permission_key] = checkbox
            advanced_layout.addWidget(checkbox)
        
        layout.addWidget(advanced_group)
        layout.addStretch()
        
    def get_button_style(self, color):
        """الحصول على أنماط الأزرار"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
                padding: 8px 15px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
        """
        
    def get_template_button_style(self, color):
        """الحصول على أنماط أزرار القوالب"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                padding: 15px;
                text-align: left;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
        """
        
    def get_group_style(self):
        """الحصول على أنماط المجموعات"""
        return """
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """
        
    def darken_color(self, color):
        """تغميق اللون"""
        color_map = {
            "#3498db": "#2980b9",
            "#27ae60": "#229954",
            "#e74c3c": "#c0392b",
            "#f39c12": "#e67e22",
            "#9b59b6": "#8e44ad",
            "#95a5a6": "#7f8c8d",
            "#e67e22": "#d35400"
        }
        return color_map.get(color, color)
        
    def get_permission_description(self, permission):
        """الحصول على وصف الصلاحية"""
        descriptions = {
            "view_students": "عرض قائمة الطلاب ومعلوماتهم",
            "add_students": "إضافة طلاب جدد للنظام",
            "edit_students": "تعديل بيانات الطلاب الموجودين",
            "delete_students": "حذف الطلاب من النظام",
            "view_teachers": "عرض قائمة المعلمين ومعلوماتهم",
            "add_teachers": "إضافة معلمين جدد للنظام",
            "edit_teachers": "تعديل بيانات المعلمين",
            "delete_teachers": "حذف المعلمين من النظام",
            "manage_all_users": "إدارة جميع المستخدمين بدون قيود",
            "system_admin": "صلاحيات مدير النظام الكاملة"
        }
        return descriptions.get(permission, "وصف غير متوفر")
        
    def setup_connections(self):
        """ربط الأحداث"""
        self.select_all_button.clicked.connect(self.select_all_permissions)
        self.clear_all_button.clicked.connect(self.clear_all_permissions)
        self.save_button.clicked.connect(self.save_permissions)
        self.cancel_button.clicked.connect(self.reject)
        
        # ربط تحديد الصلاحية في الشجرة
        self.permissions_tree.itemClicked.connect(self.on_permission_selected)
        
    def load_user_permissions(self):
        """تحميل صلاحيات المستخدم الحالية"""
        try:
            user_permissions = self.user_model.get_user_permissions(self.user_id)
            
            # تحديد الصلاحيات في الشجرة
            for permission, item in self.permission_items.items():
                if permission in user_permissions:
                    item.setCheckState(0, Qt.Checked)
                    
            # تحديد الصلاحيات المتقدمة
            for permission, checkbox in self.advanced_checkboxes.items():
                checkbox.setChecked(permission in user_permissions)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل الصلاحيات: {str(e)}")
            
    def select_all_permissions(self):
        """تحديد جميع الصلاحيات"""
        for item in self.permission_items.values():
            item.setCheckState(0, Qt.Checked)
            
        for checkbox in self.advanced_checkboxes.values():
            checkbox.setChecked(True)
            
    def clear_all_permissions(self):
        """إلغاء تحديد جميع الصلاحيات"""
        for item in self.permission_items.values():
            item.setCheckState(0, Qt.Unchecked)
            
        for checkbox in self.advanced_checkboxes.values():
            checkbox.setChecked(False)
            
    def apply_permission_template(self, template_type):
        """تطبيق قالب صلاحيات"""
        # مسح جميع الصلاحيات أولاً
        self.clear_all_permissions()
        
        # تطبيق الصلاحيات حسب القالب
        template_permissions = Config.PERMISSION_TEMPLATES.get(template_type, [])
        
        for permission in template_permissions:
            if permission in self.permission_items:
                self.permission_items[permission].setCheckState(0, Qt.Checked)
            elif permission in self.advanced_checkboxes:
                self.advanced_checkboxes[permission].setChecked(True)
                
        QMessageBox.information(self, "تم", f"تم تطبيق قالب الصلاحيات بنجاح")
        
    def on_permission_selected(self, item, column):
        """عند تحديد صلاحية في الشجرة"""
        permission_key = item.data(0, Qt.UserRole)
        if permission_key:
            description = self.get_permission_description(permission_key)
            self.permission_details_label.setText(f"<b>{item.text(0)}</b><br><br>{description}")
            
    def save_permissions(self):
        """حفظ الصلاحيات"""
        try:
            # جمع الصلاحيات المحددة
            selected_permissions = []
            
            # من الشجرة
            for permission, item in self.permission_items.items():
                if item.checkState(0) == Qt.Checked:
                    selected_permissions.append(permission)
                    
            # من الصلاحيات المتقدمة
            for permission, checkbox in self.advanced_checkboxes.items():
                if checkbox.isChecked():
                    selected_permissions.append(permission)
            
            # تحديث صلاحيات المستخدم
            permissions_string = ','.join(selected_permissions)
            self.user_model.update(self.user_id, {'permissions': permissions_string})
            
            QMessageBox.information(self, "نجح", "تم حفظ الصلاحيات بنجاح")
            
            # إرسال إشارة التحديث
            self.permissions_updated.emit()
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ الصلاحيات: {str(e)}")
