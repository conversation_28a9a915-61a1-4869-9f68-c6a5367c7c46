# تحسينات الخطوط وأحجام النصوص

## المشكلة السابقة
كانت هناك مشاكل في:
- أحجام الخطوط غير متناسقة عبر التطبيق
- خطوط غير مناسبة للنصوص العربية
- تسميات (Labels) صغيرة وصعبة القراءة
- عدم وجود نظام موحد لإدارة الخطوط

## الحلول المطبقة

### 1. إنشاء مدير الخطوط (`src/utils/font_manager.py`)

#### الميزات الرئيسية:
- **اكتشاف تلقائي للخطوط**: يختار أفضل خط متاح حسب نظام التشغيل
- **دعم متعدد المنصات**: Windows, macOS, Linux
- **خطوط محسنة للعربية**: <PERSON><PERSON><PERSON>, Arial Unicode MS, Noto Sans Arabic
- **أحجام موحدة**: نظام موحد لأحجام الخطوط

#### أنواع الخطوط المدعومة:
```python
- default: الخط الافتراضي (11pt)
- label: خط التسميات (11pt)
- button: خط الأزرار (11pt, عريض)
- input: خط حقول الإدخال (11pt)
- header: خط العناوين (14pt, عريض)
- title: خط العناوين الرئيسية (16pt, عريض)
- small: خط صغير (9pt)
- large: خط كبير (13pt)
```

### 2. تحديث إعدادات الخطوط (`src/utils/config.py`)

```python
# إعدادات الخطوط المحسنة
DEFAULT_FONT_FAMILY = "Segoe UI"
ARABIC_FONT_FAMILY = "Tahoma"
DEFAULT_FONT_SIZE = 11
SMALL_FONT_SIZE = 9
MEDIUM_FONT_SIZE = 11
LARGE_FONT_SIZE = 13
HEADER_FONT_SIZE = 14
TITLE_FONT_SIZE = 16
LABEL_FONT_SIZE = 11
BUTTON_FONT_SIZE = 11
INPUT_FONT_SIZE = 11
```

### 3. تحديث الأنماط (`src/ui/styles/main_style.py`)

- استخدام مدير الخطوط في CSS
- أحجام خطوط محسنة للقراءة
- دعم أفضل للنصوص العربية

### 4. تحديث أدوات الحوار (`src/utils/dialog_utils.py`)

- خطوط موحدة لجميع النوافذ
- أحجام مناسبة للتسميات والأزرار
- تحسين قابلية القراءة

### 5. تحديث النوافذ الرئيسية

#### النافذة الرئيسية (`src/ui/main_window.py`):
- عنوان رئيسي بخط كبير وعريض
- شعار التطبيق بخط واضح
- تسميات محسنة

#### نافذة تسجيل الدخول (`src/ui/login_window.py`):
- عنوان واضح ومقروء
- حقول إدخال بخط مناسب
- أزرار بخط عريض

## الخطوط المدعومة حسب نظام التشغيل

### Windows:
- **العربية**: Tahoma, Arial Unicode MS, Segoe UI, Calibri
- **الإنجليزية**: Segoe UI, Tahoma, Arial, Calibri

### macOS:
- **العربية**: Al Bayan, Baghdad, Tahoma, Arial Unicode MS
- **الإنجليزية**: SF Pro Display, Helvetica Neue, Arial

### Linux:
- **العربية**: Noto Sans Arabic, DejaVu Sans, Liberation Sans, Tahoma
- **الإنجليزية**: Ubuntu, DejaVu Sans, Liberation Sans, Arial

## كيفية الاستخدام

### 1. تطبيق خط على ويدجت:
```python
from src.utils.font_manager import apply_font_to_widget

# تطبيق خط تسمية
apply_font_to_widget(label, "label", bold=True)

# تطبيق خط زر
apply_font_to_widget(button, "button")

# تطبيق خط عنوان
apply_font_to_widget(title_label, "title", bold=True)
```

### 2. الحصول على CSS للخط:
```python
from src.utils.font_manager import get_font_stylesheet

# CSS لخط التسمية
css = get_font_stylesheet('label', color='#2c3e50')

# CSS لخط الزر
css = get_font_stylesheet('button', bold=True)
```

### 3. الحصول على كائن خط:
```python
from src.utils.font_manager import FontManager

font_manager = FontManager()
header_font = font_manager.get_header_font(bold=True)
widget.setFont(header_font)
```

## التحسينات المطبقة

### ✅ **أحجام الخطوط**:
- زيادة الحجم الافتراضي من 10pt إلى 11pt
- عناوين أكبر وأوضح (14pt-16pt)
- تسميات مقروءة (11pt)

### ✅ **جودة الخطوط**:
- اختيار تلقائي لأفضل خط متاح
- دعم محسن للنصوص العربية
- خطوط واضحة على جميع الأنظمة

### ✅ **التناسق**:
- نظام موحد لجميع أنواع النصوص
- أحجام ثابتة عبر التطبيق
- أنماط متسقة

### ✅ **قابلية القراءة**:
- تباين أفضل للنصوص
- أحجام مناسبة للشاشات المختلفة
- خطوط عريضة للعناصر المهمة

## الاختبار والتحقق

### 1. اختبار الخطوط:
```python
from src.utils.font_manager import FontManager

fm = FontManager()
print("الخطوط المتاحة:", fm.get_available_fonts())
print("أفضل خط عربي:", fm.best_arabic_font)
print("أفضل خط إنجليزي:", fm.best_english_font)
```

### 2. اختبار الأحجام:
- تحقق من وضوح النصوص في جميع النوافذ
- تأكد من قابلية القراءة على شاشات مختلفة
- اختبر مع نصوص عربية وإنجليزية

## النتائج

### 🎯 **تحسينات ملحوظة**:
- **وضوح أفضل**: النصوص أكثر وضوحاً وسهولة في القراءة
- **تناسق كامل**: جميع النوافذ تستخدم نفس نظام الخطوط
- **دعم محسن للعربية**: خطوط مناسبة للنصوص العربية
- **مرونة عالية**: سهولة تغيير الخطوط والأحجام

### 📊 **مقارنة قبل وبعد**:
| العنصر | قبل | بعد |
|---------|-----|-----|
| الخط الافتراضي | Arial 10pt | Tahoma 11pt |
| التسميات | غير محدد | 11pt واضح |
| العناوين | 12pt | 14-16pt عريض |
| الأزرار | 11pt | 11pt عريض |
| حقول الإدخال | غير محدد | 11pt واضح |

## الصيانة والتطوير

### إضافة خطوط جديدة:
1. أضف الخط إلى قائمة الخطوط في `FontManager`
2. اختبر على أنظمة تشغيل مختلفة
3. حدث التوثيق

### تغيير الأحجام:
1. عدل القيم في `Config`
2. اختبر جميع النوافذ
3. تأكد من التناسق

### إضافة أنواع خطوط جديدة:
1. أضف الدالة في `FontManager`
2. أضف الدعم في `get_font_stylesheet`
3. وثق الاستخدام
