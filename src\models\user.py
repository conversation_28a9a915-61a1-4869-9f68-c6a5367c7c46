#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نموذج المستخدمين
يحتوي على جميع العمليات المتعلقة بإدارة المستخدمين والمصادقة
"""

import bcrypt
from datetime import datetime
from src.models.base_model import BaseModel
from src.utils.config import Config


class User(BaseModel):
    """نموذج المستخدمين"""
    
    def __init__(self):
        super().__init__()
        self.table_name = "users"
        self.primary_key = "user_id"
        
        # الحقول المطلوبة
        self.required_fields = [
            'username', 'password_hash', 'first_name', 'last_name', 'role'
        ]
        
        # حقول البحث
        self.search_fields = [
            'username', 'first_name', 'last_name', 'email', 'role'
        ]
    
    def authenticate(self, username, password):
        """مصادقة المستخدم"""
        user = self.get_user_by_username(username)
        
        if not user:
            return None
            
        if not user['is_active']:
            raise ValueError("الحساب غير مفعل")
        
        # التحقق من كلمة المرور
        if bcrypt.checkpw(password.encode('utf-8'), user['password_hash'].encode('utf-8')):
            # تحديث آخر تسجيل دخول
            self.update_last_login(user['user_id'])
            return user
        
        return None
    
    def create_user(self, user_data):
        """إنشاء مستخدم جديد"""
        # التحقق من الحقول المطلوبة
        self.validate_required_fields(user_data, self.required_fields)
        
        # التحقق من تفرد اسم المستخدم
        if 'username' in user_data:
            self.validate_unique_field('username', user_data['username'])
        
        # التحقق من تفرد البريد الإلكتروني إذا تم إدخاله
        if 'email' in user_data and user_data['email']:
            self.validate_unique_field('email', user_data['email'])
        
        # التحقق من صحة الدور
        if 'role' in user_data:
            self.validate_role(user_data['role'])
        
        # تشفير كلمة المرور
        if 'password' in user_data:
            user_data['password_hash'] = self.hash_password(user_data['password'])
            del user_data['password']  # حذف كلمة المرور غير المشفرة
        
        # إضافة الصلاحيات الافتراضية
        if 'permissions' not in user_data and 'role' in user_data:
            user_data['permissions'] = ','.join(
                Config.DEFAULT_ROLE_PERMISSIONS.get(user_data['role'], [])
            )
        
        # إضافة الحالة الافتراضية
        if 'is_active' not in user_data:
            user_data['is_active'] = True
        
        return self.insert(user_data)
    
    def update_user(self, user_id, user_data):
        """تحديث بيانات مستخدم"""
        # التحقق من وجود المستخدم
        existing_user = self.get_by_id(user_id)
        if not existing_user:
            raise ValueError("المستخدم غير موجود")
        
        # التحقق من تفرد اسم المستخدم
        if 'username' in user_data:
            self.validate_unique_field('username', user_data['username'], user_id)
        
        # التحقق من تفرد البريد الإلكتروني
        if 'email' in user_data and user_data['email']:
            self.validate_unique_field('email', user_data['email'], user_id)
        
        # التحقق من صحة الدور
        if 'role' in user_data:
            self.validate_role(user_data['role'])
        
        # تشفير كلمة المرور الجديدة إذا تم تغييرها
        if 'password' in user_data:
            user_data['password_hash'] = self.hash_password(user_data['password'])
            del user_data['password']
        
        return self.update(user_id, user_data)
    
    def get_user_by_username(self, username):
        """جلب مستخدم باسم المستخدم"""
        return self.db_manager.fetch_one(
            "SELECT * FROM users WHERE username = ?",
            (username,)
        )
    
    def get_active_users(self):
        """جلب المستخدمين النشطين فقط"""
        return self.get_all("is_active = 1")
    
    def get_users_by_role(self, role):
        """جلب المستخدمين حسب الدور"""
        return self.get_all("role = ? AND is_active = 1", (role,))
    
    def deactivate_user(self, user_id):
        """إلغاء تفعيل مستخدم"""
        return self.update_user(user_id, {'is_active': False})
    
    def activate_user(self, user_id):
        """تفعيل مستخدم"""
        return self.update_user(user_id, {'is_active': True})
    
    def change_password(self, user_id, old_password, new_password):
        """تغيير كلمة المرور"""
        user = self.get_by_id(user_id)
        if not user:
            raise ValueError("المستخدم غير موجود")
        
        # التحقق من كلمة المرور القديمة
        if not bcrypt.checkpw(old_password.encode('utf-8'), user['password_hash'].encode('utf-8')):
            raise ValueError("كلمة المرور القديمة غير صحيحة")
        
        # التحقق من قوة كلمة المرور الجديدة
        self.validate_password_strength(new_password)
        
        # تحديث كلمة المرور
        new_hash = self.hash_password(new_password)
        return self.update(user_id, {'password_hash': new_hash})
    
    def reset_password(self, user_id, new_password):
        """إعادة تعيين كلمة المرور (للمدير)"""
        self.validate_password_strength(new_password)
        new_hash = self.hash_password(new_password)
        return self.update(user_id, {'password_hash': new_hash})
    
    def update_last_login(self, user_id):
        """تحديث آخر تسجيل دخول"""
        return self.update(user_id, {'last_login': datetime.now()})
    
    def get_user_permissions(self, user_id):
        """جلب صلاحيات المستخدم"""
        user = self.get_by_id(user_id)
        if not user or not user['permissions']:
            return []
        
        return user['permissions'].split(',')
    
    def has_permission(self, user_id, permission):
        """التحقق من وجود صلاحية معينة للمستخدم"""
        permissions = self.get_user_permissions(user_id)
        return permission in permissions
    
    def update_permissions(self, user_id, permissions):
        """تحديث صلاحيات المستخدم"""
        permissions_str = ','.join(permissions) if permissions else ''
        return self.update(user_id, {'permissions': permissions_str})
    
    def get_user_statistics(self):
        """إحصائيات المستخدمين"""
        stats = {}
        
        # إجمالي المستخدمين النشطين
        stats['total_active'] = self.count("is_active = 1")
        
        # إجمالي المستخدمين غير النشطين
        stats['total_inactive'] = self.count("is_active = 0")
        
        # المستخدمين حسب الدور
        roles_query = """
        SELECT role, COUNT(*) as count 
        FROM users 
        WHERE is_active = 1 
        GROUP BY role
        """
        roles = self.db_manager.fetch_all(roles_query)
        stats['by_role'] = {role['role']: role['count'] for role in roles}
        
        return stats
    
    @staticmethod
    def hash_password(password):
        """تشفير كلمة المرور"""
        return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    def validate_role(self, role):
        """التحقق من صحة الدور"""
        if role not in Config.USER_ROLES:
            raise ValueError(f"الدور '{role}' غير صحيح")
        return True
    
    def validate_password_strength(self, password):
        """التحقق من قوة كلمة المرور"""
        if len(password) < Config.PASSWORD_MIN_LENGTH:
            raise ValueError(f"كلمة المرور يجب أن تكون {Config.PASSWORD_MIN_LENGTH} أحرف على الأقل")
        
        # يمكن إضافة المزيد من قواعد التحقق هنا
        return True

    def create_default_admin(self):
        """إنشاء مستخدم مدير افتراضي"""
        try:
            # التحقق من وجود مدير
            admin_count = self.count("role = 'admin'")

            if admin_count == 0:
                # إنشاء مدير افتراضي
                admin_data = {
                    'username': 'admin',
                    'password': 'admin123',
                    'first_name': 'مدير',
                    'last_name': 'النظام',
                    'email': '<EMAIL>',
                    'role': 'admin',
                    'permissions': ','.join(Config.DEFAULT_ROLE_PERMISSIONS['admin']),
                    'is_active': True
                }

                self.create_user(admin_data)
                return True
            return False
        except Exception as e:
            raise Exception(f"خطأ في إنشاء المدير الافتراضي: {str(e)}")

    def username_exists(self, username, exclude_user_id=None):
        """التحقق من وجود اسم المستخدم"""
        try:
            if exclude_user_id:
                count = self.count("username = ? AND user_id != ?", (username, exclude_user_id))
            else:
                count = self.count("username = ?", (username,))
            return count > 0
        except Exception:
            return False

    def email_exists(self, email, exclude_user_id=None):
        """التحقق من وجود البريد الإلكتروني"""
        try:
            if exclude_user_id:
                count = self.count("email = ? AND user_id != ?", (email, exclude_user_id))
            else:
                count = self.count("email = ?", (email,))
            return count > 0
        except Exception:
            return False

    def get_all_users(self):
        """الحصول على جميع المستخدمين"""
        try:
            query = """
            SELECT user_id, username, email, first_name, last_name, role,
                   permissions, is_active, created_at, last_login
            FROM users
            ORDER BY created_at DESC
            """
            return self.db_manager.fetch_all(query)
        except Exception as e:
            raise Exception(f"خطأ في جلب المستخدمين: {str(e)}")

    def search_users(self, search_term):
        """البحث في المستخدمين"""
        try:
            query = """
            SELECT user_id, username, email, first_name, last_name, role,
                   permissions, is_active, created_at, last_login
            FROM users
            WHERE username LIKE ? OR email LIKE ? OR first_name LIKE ? OR last_name LIKE ?
            ORDER BY first_name, last_name
            """
            search_param = f"%{search_term}%"
            return self.db_manager.fetch_all(query, (search_param, search_param, search_param, search_param))
        except Exception as e:
            raise Exception(f"خطأ في البحث: {str(e)}")

    def get_user_by_id(self, user_id):
        """الحصول على مستخدم بالمعرف"""
        try:
            query = """
            SELECT user_id, username, email, first_name, last_name, role,
                   permissions, is_active, created_at, last_login
            FROM users
            WHERE user_id = ?
            """
            return self.db_manager.fetch_one(query, (user_id,))
        except Exception as e:
            raise Exception(f"خطأ في جلب المستخدم: {str(e)}")

    def delete_user(self, user_id):
        """حذف المستخدم نهائياً"""
        try:
            query = "DELETE FROM users WHERE user_id = ?"
            return self.db_manager.execute_query(query, (user_id,))
        except Exception as e:
            raise Exception(f"خطأ في حذف المستخدم: {str(e)}")
