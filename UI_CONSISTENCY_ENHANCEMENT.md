# توحيد تنسيق شاشات التطبيق مع تصميم شاشة المعلم

## نظرة عامة
تم توحيد تنسيق شاشات إضافة المادة وإضافة الصف والإعدادات العامة لتكون متطابقة مع تصميم شاشة إضافة المعلم من ناحية الخط والتصميم والتخطيط.

## التصميم المرجعي (شاشة إضافة المعلم)

### الخصائص الأساسية:
- **حجم النافذة**: 600x700 بكسل
- **تخطيط التبويبات**: QTabWidget مع تبويبات متعددة
- **عنوان محسن**: خلفية رمادية فاتحة مع حدود مدورة
- **أنماط موحدة**: خطوط وألوان متسقة
- **أزرار محسنة**: ألوان مميزة للحفظ والإلغاء

### هيكل التصميم:
```
┌─────────────────────────────────────┐
│           عنوان النافذة            │ ← خلفية #ecf0f1
├─────────────────────────────────────┤
│ ┌─ تبويب 1 ─┐ ┌─ تبويب 2 ─┐      │
│ │            │ │            │      │
│ │   محتوى   │ │   محتوى   │      │
│ │  التبويب  │ │  التبويب  │      │
│ │            │ │            │      │
│ └────────────┘ └────────────┘      │
├─────────────────────────────────────┤
│              [إلغاء] [حفظ]          │ ← أزرار محسنة
└─────────────────────────────────────┘
```

## التحسينات المطبقة

### 1. **شاشة إضافة المادة الدراسية** 📚

#### قبل التحسين:
```python
# تصميم بسيط بدون تبويبات
self.setFixedSize(500, 500)
form_frame = QFrame()  # إطار واحد فقط
```

#### بعد التحسين:
```python
# تصميم متطابق مع شاشة المعلم
self.setFixedSize(600, 500)

# علامات التبويب
self.tab_widget = QTabWidget()

# تبويب البيانات الأساسية
self.basic_tab = QWidget()
self.setup_basic_tab()
self.tab_widget.addTab(self.basic_tab, "البيانات الأساسية")

# تبويب الإعدادات
self.settings_tab = QWidget()
self.setup_settings_tab()
self.tab_widget.addTab(self.settings_tab, "الإعدادات")
```

#### التبويبات الجديدة:
1. **البيانات الأساسية**:
   - رمز المادة
   - اسم المادة
   - الساعات المعتمدة
   - الوصف

2. **الإعدادات**:
   - حالة المادة (نشطة/غير نشطة)
   - نوع المادة (أساسية، اختيارية، متطلب جامعة، متطلب كلية)
   - المستوى (الأول، الثاني، الثالث، الرابع)
   - الفصل الدراسي (الأول، الثاني، الصيفي)

#### العنوان المحسن:
```python
title_label.setStyleSheet("""
    QLabel {
        font-size: 18px;
        font-weight: bold;
        color: #2c3e50;
        padding: 10px;
        background-color: #ecf0f1;  # ✅ نفس لون شاشة المعلم
        border-radius: 5px;
        margin-bottom: 10px;
    }
""")
```

#### الأزرار المحسنة:
```python
# زر الحفظ
self.save_button.setStyleSheet("""
    QPushButton {
        background-color: #27ae60;  # ✅ أخضر للحفظ
        color: white;
        border: none;
        border-radius: 5px;
        font-size: 14px;
        font-weight: bold;
        padding: 10px 20px;
    }
    QPushButton:hover {
        background-color: #229954;
    }
""")

# زر الإلغاء
self.cancel_button.setStyleSheet("""
    QPushButton {
        background-color: #95a5a6;  # ✅ رمادي للإلغاء
        color: white;
        border: none;
        border-radius: 5px;
        font-size: 14px;
        font-weight: bold;
        padding: 10px 20px;
    }
    QPushButton:hover {
        background-color: #7f8c8d;
    }
""")
```

### 2. **شاشة إضافة الصف** 🏫

#### التحسينات المطبقة:
- **حجم النافذة**: تم تغييره إلى 600x500 ليطابق شاشة المعلم
- **إضافة QTabWidget**: لتنظيم المحتوى في تبويبات
- **عنوان محسن**: بنفس تنسيق شاشة المعلم
- **استيراد UIStyles**: لاستخدام الأنماط الموحدة

#### الهيكل الجديد:
```python
# علامات التبويب
self.tab_widget = QTabWidget()

# تبويب البيانات الأساسية
self.basic_tab = QWidget()
self.setup_basic_tab()
self.tab_widget.addTab(self.basic_tab, "البيانات الأساسية")

# تبويب الإعدادات
self.settings_tab = QWidget()
self.setup_settings_tab()
self.tab_widget.addTab(self.settings_tab, "الإعدادات")
```

### 3. **شاشة الإعدادات العامة** ⚙️

#### التحسينات المطبقة:
- **أنماط QGroupBox محسنة**: لتطابق تصميم شاشة المعلم
- **حقول إدخال موحدة**: بنفس تنسيق شاشة المعلم
- **أزرار محسنة**: بنفس الألوان والأحجام
- **مسافات منتظمة**: تخطيط أكثر تنظيماً

#### أنماط QGroupBox المحسنة:
```python
QGroupBox {
    font-size: 16px;
    font-weight: bold;
    color: #2c3e50;
    border: 2px solid #bdc3c7;
    border-radius: 8px;
    margin-top: 15px;
    padding-top: 15px;
    background-color: white;
}
QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 10px 0 10px;
    background-color: white;
    border-radius: 3px;
}
```

#### حقول الإدخال المحسنة:
```python
# تطبيق نمط موحد على جميع حقول الإدخال
self.language_combo.setStyleSheet(self.get_input_style())
self.theme_combo.setStyleSheet(self.get_input_style())
self.font_family_combo.setStyleSheet(self.get_input_style())
self.font_size_spin.setStyleSheet(self.get_input_style())
```

#### زر تطبيق الخط المحسن:
```python
self.apply_font_button.setFixedHeight(40)  # ✅ نفس ارتفاع أزرار شاشة المعلم
self.apply_font_button.setStyleSheet("""
    QPushButton {
        background-color: #3498db;
        color: white;
        border: none;
        border-radius: 5px;
        font-size: 14px;           # ✅ نفس حجم خط شاشة المعلم
        font-weight: bold;
        padding: 10px 20px;       # ✅ نفس padding شاشة المعلم
    }
    QPushButton:hover {
        background-color: #2980b9;
    }
""")
```

## المعايير الموحدة

### 1. **أحجام النوافذ** 📐:
- **العرض**: 600 بكسل (موحد)
- **الارتفاع**: متغير حسب المحتوى (500-700)
- **الحد الأدنى**: 600x500 للنوافذ البسيطة
- **الحد الأقصى**: 600x700 للنوافذ المعقدة

### 2. **الألوان الموحدة** 🎨:
- **العنوان**: خلفية #ecf0f1، نص #2c3e50
- **زر الحفظ**: #27ae60 (أخضر)
- **زر الإلغاء**: #95a5a6 (رمادي)
- **زر التطبيق**: #3498db (أزرق)
- **الحدود**: #bdc3c7

### 3. **الخطوط الموحدة** 🔤:
- **العنوان**: 18px، bold
- **الأزرار**: 14px، bold
- **النصوص العادية**: 12px
- **عناوين المجموعات**: 16px، bold

### 4. **المسافات الموحدة** 📏:
- **هوامش النافذة**: 20px من جميع الجهات
- **مسافات العناصر**: 15px بين العناصر
- **padding الأزرار**: 10px 20px
- **ارتفاع الأزرار**: 40px

### 5. **التخطيط الموحد** 📋:
- **استخدام QTabWidget**: للنوافذ متعددة الأقسام
- **QFormLayout**: للنماذج
- **QGroupBox**: لتجميع العناصر المترابطة
- **أزرار في الأسفل**: مع محاذاة لليمين

## الفوائد المحققة

### 1. **تناسق المظهر** 🎨:
- ✅ **نفس التصميم**: جميع النوافذ تبدو متطابقة
- ✅ **ألوان موحدة**: نظام ألوان متسق عبر التطبيق
- ✅ **خطوط منتظمة**: أحجام وأنواع خطوط موحدة
- ✅ **مسافات متسقة**: تخطيط منتظم ومنظم

### 2. **تجربة مستخدم محسنة** 👤:
- ✅ **سهولة التنقل**: تصميم مألوف عبر النوافذ
- ✅ **وضوح الوظائف**: ألوان مميزة للأزرار
- ✅ **تنظيم أفضل**: تبويبات لتجميع المحتوى
- ✅ **قابلية قراءة عالية**: خطوط واضحة ومقروءة

### 3. **سهولة الصيانة** 🔧:
- ✅ **كود موحد**: نفس الأنماط عبر النوافذ
- ✅ **تحديث سهل**: تغيير واحد يؤثر على جميع النوافذ
- ✅ **إضافة نوافذ جديدة**: سهولة تطبيق نفس التصميم
- ✅ **توثيق واضح**: معايير محددة للتصميم

### 4. **الاحترافية** 💼:
- ✅ **مظهر احترافي**: تصميم متسق ومتقن
- ✅ **جودة عالية**: اهتمام بالتفاصيل
- ✅ **معايير صناعية**: اتباع أفضل الممارسات
- ✅ **انطباع إيجابي**: تجربة مستخدم ممتازة

## الملفات المحدثة

### الملفات المعدلة:
1. **`src/ui/dialogs/subject_dialog.py`**:
   - إضافة QTabWidget مع تبويبين
   - تحديث العنوان والأزرار
   - إضافة حقول جديدة في تبويب الإعدادات
   - توحيد الأنماط مع شاشة المعلم

2. **`src/ui/dialogs/class_dialog.py`**:
   - تحديث الاستيرادات لتشمل UIStyles
   - إضافة QTabWidget للتنظيم
   - توحيد حجم النافذة مع شاشة المعلم
   - تحديث العنوان ليطابق التصميم المرجعي

3. **`src/ui/widgets/settings_widget.py`**:
   - تحديث أنماط QGroupBox
   - توحيد أنماط حقول الإدخال
   - تحسين أزرار التطبيق
   - إضافة مسافات منتظمة

## النتيجة النهائية

**تم توحيد تنسيق جميع الشاشات المطلوبة بنجاح!**

- ✅ **شاشة إضافة المادة**: تصميم متطابق مع شاشة المعلم
- ✅ **شاشة إضافة الصف**: نفس التخطيط والأنماط
- ✅ **شاشة الإعدادات العامة**: أنماط محسنة وموحدة
- ✅ **تناسق شامل**: جميع النوافذ تتبع نفس المعايير

الآن يتمتع المستخدمون بـ:

- 🎨 **مظهر موحد ومتسق** عبر جميع شاشات التطبيق
- 👤 **تجربة مستخدم محسنة** مع تصميم مألوف
- 📖 **وضوح أكبر** في النصوص والعناصر
- ⚙️ **سهولة استخدام** مع تنظيم أفضل للمحتوى
- 💼 **مظهر احترافي** يعكس جودة التطبيق

**مثال على التحسين:**
- قبل: نوافذ بتصاميم مختلفة وأحجام متباينة
- بعد: نوافذ موحدة بنفس التصميم والألوان والخطوط

🎉🎨✨🚀
