#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لإنشاء المواد الدراسية الافتراضية
"""

import sys
import os

# إضافة مجلد src إلى المسار
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from models.subject import Subject

def create_default_subjects():
    """إنشاء المواد الدراسية الافتراضية"""
    
    subject_model = Subject()
    
    default_subjects = [
        {
            "subject_code": "MATH101",
            "subject_name": "الرياضيات",
            "credit_hours": 4,
            "description": "مادة الرياضيات الأساسية تشمل الجبر والهندسة"
        },
        {
            "subject_code": "ARAB101",
            "subject_name": "اللغة العربية",
            "credit_hours": 4,
            "description": "مادة اللغة العربية تشمل النحو والصرف والأدب"
        },
        {
            "subject_code": "ENG101",
            "subject_name": "اللغة الإنجليزية",
            "credit_hours": 3,
            "description": "مادة اللغة الإنجليزية الأساسية"
        },
        {
            "subject_code": "SCI101",
            "subject_name": "العلوم",
            "credit_hours": 3,
            "description": "مادة العلوم العامة تشمل الفيزياء والكيمياء والأحياء"
        },
        {
            "subject_code": "HIST101",
            "subject_name": "التاريخ",
            "credit_hours": 2,
            "description": "مادة التاريخ الإسلامي والعربي"
        },
        {
            "subject_code": "GEO101",
            "subject_name": "الجغرافيا",
            "credit_hours": 2,
            "description": "مادة الجغرافيا الطبيعية والبشرية"
        },
        {
            "subject_code": "REL101",
            "subject_name": "التربية الإسلامية",
            "credit_hours": 2,
            "description": "مادة التربية الإسلامية والقرآن الكريم"
        },
        {
            "subject_code": "COMP101",
            "subject_name": "الحاسوب",
            "credit_hours": 2,
            "description": "مادة الحاسوب وتقنية المعلومات"
        },
        {
            "subject_code": "PE101",
            "subject_name": "التربية البدنية",
            "credit_hours": 1,
            "description": "مادة التربية البدنية والرياضة"
        },
        {
            "subject_code": "ART101",
            "subject_name": "التربية الفنية",
            "credit_hours": 1,
            "description": "مادة التربية الفنية والرسم"
        }
    ]
    
    created_count = 0
    updated_count = 0
    
    print("جاري إنشاء المواد الدراسية...")
    print("=" * 50)
    
    for subject_data in default_subjects:
        try:
            # التحقق من وجود المادة
            existing = subject_model.get_subject_by_code(subject_data["subject_code"])
            
            if existing:
                print(f"✓ المادة موجودة مسبقاً: {subject_data['subject_name']}")
                updated_count += 1
            else:
                # إنشاء المادة الجديدة
                subject_model.add_subject(subject_data)
                print(f"✅ تم إنشاء المادة: {subject_data['subject_name']}")
                created_count += 1
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء المادة {subject_data['subject_name']}: {e}")
    
    print("=" * 50)
    print(f"تم إنشاء {created_count} مادة جديدة")
    print(f"توجد {updated_count} مادة مسبقاً")
    print(f"إجمالي المواد: {created_count + updated_count}")
    
    # عرض جميع المواد الموجودة
    print("\nالمواد الموجودة في النظام:")
    print("-" * 30)
    
    try:
        all_subjects = subject_model.get_all()
        for i, subject in enumerate(all_subjects, 1):
            status = "نشط" if subject.get('is_active', 1) else "غير نشط"
            print(f"{i}. {subject['subject_name']} ({subject['subject_code']}) - {status}")
    except Exception as e:
        print(f"خطأ في عرض المواد: {e}")

if __name__ == "__main__":
    try:
        create_default_subjects()
        print("\n🎉 تم إنشاء المواد الدراسية بنجاح!")
        print("\nيمكنك الآن تشغيل التطبيق ورؤية المواد في شاشة النتائج.")
        
    except Exception as e:
        print(f"❌ خطأ عام في إنشاء المواد: {e}")
        import traceback
        traceback.print_exc()
