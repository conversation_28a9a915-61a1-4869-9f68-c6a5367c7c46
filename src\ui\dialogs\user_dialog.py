#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إضافة وتعديل المستخدمين
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QLineEdit, QPushButton, QLabel, QFrame, QMessageBox,
                             QComboBox, QCheckBox, QTabWidget, QWidget, QTextEdit,
                             QGroupBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

from src.models.user import User
from src.utils.config import Config
from src.utils.dialog_utils import apply_dialog_styles, center_dialog_on_parent, ensure_dialog_visibility
from src.utils.icon_manager import get_icon


class UserDialog(QDialog):
    """نافذة إضافة وتعديل المستخدمين"""
    
    # إشارة حفظ البيانات
    user_saved = pyqtSignal()
    
    def __init__(self, user_id=None, parent=None):
        super().__init__(parent)
        self.user_id = user_id
        self.user_model = User()
        self.is_edit_mode = user_id is not None
        
        self.setup_ui()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.load_user_data()
            
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة
        title = "تعديل مستخدم" if self.is_edit_mode else "إضافة مستخدم جديد"
        self.setWindowTitle(title)
        self.setFixedSize(600, 700)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

        # إعدادات النافذة لضمان الظهور في المقدمة
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint | Qt.WindowStaysOnTopHint)
        self.setAttribute(Qt.WA_DeleteOnClose)

        # توسيط النافذة
        center_dialog_on_parent(self, self.parent())
        
        # تطبيق الأنماط الموحدة
        apply_dialog_styles(self)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # عنوان النافذة
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # علامات التبويب
        self.tab_widget = QTabWidget()
        
        # تبويب البيانات الأساسية
        self.basic_tab = QWidget()
        self.setup_basic_tab()
        self.tab_widget.addTab(self.basic_tab, "البيانات الأساسية")
        
        # تبويب الصلاحيات والأمان
        self.security_tab = QWidget()
        self.setup_security_tab()
        self.tab_widget.addTab(self.security_tab, "الصلاحيات والأمان")
        
        main_layout.addWidget(self.tab_widget)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        self.save_button = QPushButton("حفظ")
        self.save_button.setIcon(get_icon("save", 16))
        self.save_button.setFixedHeight(40)
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setIcon(get_icon("cancel", 16))
        self.cancel_button.setFixedHeight(40)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.save_button)
        
        main_layout.addLayout(buttons_layout)
        
    def setup_basic_tab(self):
        """إعداد تبويب البيانات الأساسية"""
        layout = QVBoxLayout(self.basic_tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # معلومات تسجيل الدخول
        login_group = QGroupBox("معلومات تسجيل الدخول")
        login_group.setStyleSheet(self.get_group_style())
        login_layout = QFormLayout(login_group)
        
        # اسم المستخدم
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        login_layout.addRow("اسم المستخدم *:", self.username_input)
        
        # كلمة المرور (فقط في وضع الإضافة)
        if not self.is_edit_mode:
            self.password_input = QLineEdit()
            self.password_input.setEchoMode(QLineEdit.Password)
            self.password_input.setPlaceholderText("أدخل كلمة المرور")
            login_layout.addRow("كلمة المرور *:", self.password_input)
            
            self.confirm_password_input = QLineEdit()
            self.confirm_password_input.setEchoMode(QLineEdit.Password)
            self.confirm_password_input.setPlaceholderText("تأكيد كلمة المرور")
            login_layout.addRow("تأكيد كلمة المرور *:", self.confirm_password_input)
        
        layout.addWidget(login_group)
        
        # المعلومات الشخصية
        personal_group = QGroupBox("المعلومات الشخصية")
        personal_group.setStyleSheet(self.get_group_style())
        personal_layout = QFormLayout(personal_group)
        
        # الاسم الأول
        self.first_name_input = QLineEdit()
        self.first_name_input.setPlaceholderText("أدخل الاسم الأول")
        personal_layout.addRow("الاسم الأول *:", self.first_name_input)
        
        # الاسم الأخير
        self.last_name_input = QLineEdit()
        self.last_name_input.setPlaceholderText("أدخل الاسم الأخير")
        personal_layout.addRow("الاسم الأخير *:", self.last_name_input)
        
        # البريد الإلكتروني
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("أدخل البريد الإلكتروني")
        personal_layout.addRow("البريد الإلكتروني:", self.email_input)
        
        layout.addWidget(personal_group)
        layout.addStretch()
        
    def setup_security_tab(self):
        """إعداد تبويب الصلاحيات والأمان"""
        layout = QVBoxLayout(self.security_tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # الدور والصلاحيات
        role_group = QGroupBox("الدور والصلاحيات")
        role_group.setStyleSheet(self.get_group_style())
        role_layout = QFormLayout(role_group)
        
        # الدور
        self.role_combo = QComboBox()
        for role_key, role_name in Config.USER_ROLES.items():
            self.role_combo.addItem(role_name, role_key)
        self.role_combo.currentTextChanged.connect(self.on_role_changed)
        role_layout.addRow("الدور *:", self.role_combo)
        
        layout.addWidget(role_group)
        
        # الصلاحيات المخصصة
        permissions_group = QGroupBox("الصلاحيات المخصصة")
        permissions_group.setStyleSheet(self.get_group_style())
        permissions_layout = QVBoxLayout(permissions_group)
        
        # إنشاء checkboxes للصلاحيات
        self.permission_checkboxes = {}
        for permission_key, permission_name in Config.PERMISSIONS.items():
            checkbox = QCheckBox(permission_name)
            checkbox.setObjectName(permission_key)
            self.permission_checkboxes[permission_key] = checkbox
            permissions_layout.addWidget(checkbox)
        
        layout.addWidget(permissions_group)
        
        # إعدادات الحساب
        account_group = QGroupBox("إعدادات الحساب")
        account_group.setStyleSheet(self.get_group_style())
        account_layout = QFormLayout(account_group)
        
        # حالة الحساب
        self.is_active_checkbox = QCheckBox("الحساب نشط")
        self.is_active_checkbox.setChecked(True)
        account_layout.addRow("", self.is_active_checkbox)
        
        layout.addWidget(account_group)
        layout.addStretch()
        
    def get_group_style(self):
        """الحصول على أنماط المجموعات"""
        return """
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """
        
    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        self.save_button.clicked.connect(self.save_user)
        self.cancel_button.clicked.connect(self.reject)
        
    def on_role_changed(self):
        """عند تغيير الدور"""
        role_key = self.role_combo.currentData()
        default_permissions = Config.DEFAULT_ROLE_PERMISSIONS.get(role_key, [])
        
        # إعادة تعيين جميع الصلاحيات
        for permission_key, checkbox in self.permission_checkboxes.items():
            checkbox.setChecked(permission_key in default_permissions)
            
    def load_user_data(self):
        """تحميل بيانات المستخدم للتعديل"""
        try:
            user = self.user_model.get_by_id(self.user_id)
            if not user:
                QMessageBox.critical(self, "خطأ", "لم يتم العثور على المستخدم")
                self.reject()
                return
                
            # ملء الحقول بالبيانات
            self.username_input.setText(str(user['username']))
            self.first_name_input.setText(str(user['first_name']))
            self.last_name_input.setText(str(user['last_name']))
            self.email_input.setText(str(user['email'] or ''))
            
            # تعيين الدور
            role_index = self.role_combo.findData(user['role'])
            if role_index >= 0:
                self.role_combo.setCurrentIndex(role_index)
                
            # تعيين الصلاحيات
            user_permissions = self.user_model.get_user_permissions(self.user_id)
            for permission_key, checkbox in self.permission_checkboxes.items():
                checkbox.setChecked(permission_key in user_permissions)
                
            # تعيين حالة الحساب
            self.is_active_checkbox.setChecked(bool(user['is_active']))
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل بيانات المستخدم: {str(e)}")
            
    def validate_input(self):
        """التحقق من صحة البيانات المدخلة"""
        # التحقق من الحقول المطلوبة
        if not self.username_input.text().strip():
            QMessageBox.warning(self, "تحذير", "اسم المستخدم مطلوب")
            self.username_input.setFocus()
            return False
            
        if not self.first_name_input.text().strip():
            QMessageBox.warning(self, "تحذير", "الاسم الأول مطلوب")
            self.first_name_input.setFocus()
            return False
            
        if not self.last_name_input.text().strip():
            QMessageBox.warning(self, "تحذير", "الاسم الأخير مطلوب")
            self.last_name_input.setFocus()
            return False
            
        # التحقق من كلمة المرور في وضع الإضافة
        if not self.is_edit_mode:
            if not self.password_input.text():
                QMessageBox.warning(self, "تحذير", "كلمة المرور مطلوبة")
                self.password_input.setFocus()
                return False
                
            if self.password_input.text() != self.confirm_password_input.text():
                QMessageBox.warning(self, "تحذير", "كلمة المرور وتأكيدها غير متطابقين")
                self.confirm_password_input.setFocus()
                return False
                
            if len(self.password_input.text()) < 6:
                QMessageBox.warning(self, "تحذير", "كلمة المرور يجب أن تكون 6 أحرف على الأقل")
                self.password_input.setFocus()
                return False
        
        # التحقق من البريد الإلكتروني
        email = self.email_input.text().strip()
        if email and '@' not in email:
            QMessageBox.warning(self, "تحذير", "البريد الإلكتروني غير صحيح")
            self.email_input.setFocus()
            return False
            
        # التحقق من تفرد اسم المستخدم
        username = self.username_input.text().strip()
        try:
            if self.user_id:
                # في حالة التعديل
                existing_user = self.user_model.get_all("username = ? AND user_id != ?", (username, self.user_id))
            else:
                # في حالة الإضافة
                existing_user = self.user_model.get_all("username = ?", (username,))

            if existing_user:
                QMessageBox.warning(self, "تحذير", "اسم المستخدم موجود مسبقاً")
                self.username_input.setFocus()
                return False
        except Exception:
            pass

        # التحقق من تفرد البريد الإلكتروني
        if email:
            try:
                if self.user_id:
                    # في حالة التعديل
                    existing_email = self.user_model.get_all("email = ? AND user_id != ?", (email, self.user_id))
                else:
                    # في حالة الإضافة
                    existing_email = self.user_model.get_all("email = ?", (email,))

                if existing_email:
                    QMessageBox.warning(self, "تحذير", "البريد الإلكتروني موجود مسبقاً")
                    self.email_input.setFocus()
                    return False
            except Exception:
                pass
            
        return True
        
    def save_user(self):
        """حفظ بيانات المستخدم"""
        if not self.validate_input():
            return
            
        try:
            # جمع البيانات من النموذج
            user_data = {
                'username': self.username_input.text().strip(),
                'first_name': self.first_name_input.text().strip(),
                'last_name': self.last_name_input.text().strip(),
                'email': self.email_input.text().strip() or None,
                'role': self.role_combo.currentData(),
                'is_active': self.is_active_checkbox.isChecked()
            }
            
            # إضافة كلمة المرور في وضع الإضافة
            if not self.is_edit_mode:
                user_data['password'] = self.password_input.text()
            
            # جمع الصلاحيات المحددة
            selected_permissions = []
            for permission_key, checkbox in self.permission_checkboxes.items():
                if checkbox.isChecked():
                    selected_permissions.append(permission_key)
            
            user_data['permissions'] = ','.join(selected_permissions)
            
            # حفظ البيانات
            if self.is_edit_mode:
                self.user_model.update(self.user_id, user_data)
                QMessageBox.information(self, "نجح", "تم تحديث المستخدم بنجاح")
            else:
                self.user_model.create_user(user_data)
                QMessageBox.information(self, "نجح", "تم إضافة المستخدم بنجاح")
            
            # إرسال إشارة الحفظ
            self.user_saved.emit()
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ البيانات: {str(e)}")

    def showEvent(self, event):
        """عند إظهار النافذة"""
        super().showEvent(event)
        # ضمان الظهور في المقدمة
        ensure_dialog_visibility(self)

    def exec_(self):
        """تنفيذ النافذة مع ضمان الظهور الصحيح"""
        # ضمان الظهور في المقدمة
        ensure_dialog_visibility(self)
        return super().exec_()
