#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ويدجت إدارة النتائج والدرجات الدراسية
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                             QMessageBox, QHeaderView, QFrame, QTabWidget,
                             QComboBox, QSpinBox, QDoubleSpinBox, QSplitter)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from src.models.student import Student
from src.models.subject import Subject
from src.models.class_model import ClassModel
from src.ui.dialogs.grade_dialog import GradeDialog
from src.ui.dialogs.report_card_dialog import ReportCardDialog


class ResultsWidget(QWidget):
    """ويدجت إدارة النتائج والدرجات الدراسية"""

    def __init__(self):
        super().__init__()
        self.student_model = Student()
        self.subject_model = Subject()
        self.class_model = ClassModel()
        self.setup_ui()
        self.load_classes()
        self.load_all_subjects()  # تحميل المواد عند بدء التشغيل

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # علامات التبويب
        self.tab_widget = QTabWidget()

        # تبويب إدخال الدرجات
        self.grades_tab = QWidget()
        self.setup_grades_tab()
        self.tab_widget.addTab(self.grades_tab, "إدخال الدرجات")

        # تبويب كشوف الدرجات
        self.reports_tab = QWidget()
        self.setup_reports_tab()
        self.tab_widget.addTab(self.reports_tab, "كشوف الدرجات")

        # تبويب الإحصائيات
        self.statistics_tab = QWidget()
        self.setup_statistics_tab()
        self.tab_widget.addTab(self.statistics_tab, "إحصائيات الأداء")

        layout.addWidget(self.tab_widget)

    def setup_grades_tab(self):
        """إعداد تبويب إدخال الدرجات"""
        layout = QVBoxLayout(self.grades_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)

        # شريط التحكم
        control_frame = QFrame()
        control_layout = QHBoxLayout(control_frame)

        # اختيار الصف
        control_layout.addWidget(QLabel("الصف:"))
        self.class_combo = QComboBox()
        control_layout.addWidget(self.class_combo)

        # اختيار المادة
        control_layout.addWidget(QLabel("المادة:"))
        self.subject_combo = QComboBox()
        control_layout.addWidget(self.subject_combo)

        # اختيار نوع التقييم
        control_layout.addWidget(QLabel("نوع التقييم:"))
        self.assessment_type_combo = QComboBox()
        assessment_types = ["اختبار شهري", "اختبار نصف الفصل", "اختبار نهائي",
                           "واجب", "مشروع", "مشاركة", "أخرى"]
        self.assessment_type_combo.addItems(assessment_types)
        control_layout.addWidget(self.assessment_type_combo)

        # الدرجة الكاملة
        control_layout.addWidget(QLabel("الدرجة الكاملة:"))
        self.max_score_spin = QSpinBox()
        self.max_score_spin.setRange(1, 100)
        self.max_score_spin.setValue(20)
        control_layout.addWidget(self.max_score_spin)

        # أزرار التحكم
        self.load_students_button = QPushButton("تحميل الطلاب")
        self.save_grades_button = QPushButton("حفظ الدرجات")
        self.clear_grades_button = QPushButton("مسح الدرجات")

        grade_button_style = """
            QPushButton {
                background-color: #8e44ad;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7d3c98;
            }
        """

        for button in [self.load_students_button, self.save_grades_button, self.clear_grades_button]:
            button.setStyleSheet(grade_button_style)
            button.setFixedHeight(35)

        control_layout.addStretch()
        control_layout.addWidget(self.load_students_button)
        control_layout.addWidget(self.save_grades_button)
        control_layout.addWidget(self.clear_grades_button)

        layout.addWidget(control_frame)

        # جدول الدرجات
        self.grades_table = QTableWidget()
        self.setup_grades_table()
        layout.addWidget(self.grades_table)

        # ربط الأحداث
        self.setup_grades_connections()

    def setup_grades_table(self):
        """إعداد جدول الدرجات"""
        columns = [
            "رقم الطالب", "اسم الطالب", "الدرجة", "النسبة المئوية",
            "التقدير", "ملاحظات"
        ]

        self.grades_table.setColumnCount(len(columns))
        self.grades_table.setHorizontalHeaderLabels(columns)

        # إعداد خصائص الجدول
        self.grades_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.grades_table.setAlternatingRowColors(True)

        # تخصيص عرض الأعمدة
        header = self.grades_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)

        # تطبيق الأنماط
        self.grades_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
            }
            QTableWidget::item:selected {
                background-color: #8e44ad;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

    def setup_grades_connections(self):
        """ربط أحداث الدرجات"""
        self.load_students_button.clicked.connect(self.load_students_for_grading)
        self.save_grades_button.clicked.connect(self.save_grades)
        self.clear_grades_button.clicked.connect(self.clear_grades)
        self.class_combo.currentTextChanged.connect(self.load_subjects_for_class)
        self.grades_table.itemChanged.connect(self.calculate_grade_percentage)

    def load_classes(self):
        """تحميل الصفوف الدراسية"""
        try:
            classes = self.class_model.get_active_classes()
            self.class_combo.clear()

            for class_info in classes:
                self.class_combo.addItem(class_info['class_name'], class_info['class_id'])

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل الصفوف: {str(e)}")

    def load_subjects_for_class(self):
        """تحميل المواد للصف المحدد"""
        try:
            self.subject_combo.clear()

            if self.class_combo.currentIndex() < 0:
                # إذا لم يتم اختيار صف، اعرض جميع المواد
                self.load_all_subjects()
                return

            class_id = self.class_combo.currentData()

            # أولاً: محاولة الحصول على المواد المرتبطة بالصف
            query = """
            SELECT DISTINCT s.subject_id, s.subject_name
            FROM subjects s
            JOIN class_subjects cs ON s.subject_id = cs.subject_id
            WHERE cs.class_id = ? AND s.is_active = 1
            ORDER BY s.subject_name
            """
            subjects = self.subject_model.db_manager.fetch_all(query, (class_id,))

            if subjects:
                # إذا وجدت مواد مرتبطة بالصف
                for subject in subjects:
                    self.subject_combo.addItem(subject['subject_name'], subject['subject_id'])
            else:
                # إذا لم توجد مواد مرتبطة، اعرض جميع المواد المتاحة
                self.load_all_subjects()

        except Exception as e:
            # في حالة الخطأ، اعرض جميع المواد
            self.load_all_subjects()
            print(f"خطأ في تحميل المواد للصف: {str(e)}")

    def load_all_subjects(self):
        """تحميل جميع المواد المتاحة"""
        try:
            # استعلام جميع المواد النشطة
            query = """
            SELECT subject_id, subject_name
            FROM subjects
            WHERE is_active = 1
            ORDER BY subject_name
            """
            subjects = self.subject_model.db_manager.fetch_all(query)

            self.subject_combo.clear()
            if subjects:
                for subject in subjects:
                    self.subject_combo.addItem(subject['subject_name'], subject['subject_id'])
            else:
                # إذا لم توجد مواد، أنشئ مواد افتراضية
                self.create_default_subjects()
                # ثم حاول تحميلها مرة أخرى
                subjects = self.subject_model.db_manager.fetch_all(query)
                if subjects:
                    for subject in subjects:
                        self.subject_combo.addItem(subject['subject_name'], subject['subject_id'])
                else:
                    self.subject_combo.addItem("لا توجد مواد متاحة", None)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل المواد: {str(e)}")
            # إضافة مواد افتراضية في حالة الخطأ
            self.add_fallback_subjects()

    def create_default_subjects(self):
        """إنشاء مواد دراسية افتراضية"""
        try:
            default_subjects = [
                {"subject_code": "MATH101", "subject_name": "الرياضيات", "credit_hours": 4, "description": "مادة الرياضيات الأساسية"},
                {"subject_code": "ARAB101", "subject_name": "اللغة العربية", "credit_hours": 4, "description": "مادة اللغة العربية"},
                {"subject_code": "ENG101", "subject_name": "اللغة الإنجليزية", "credit_hours": 3, "description": "مادة اللغة الإنجليزية"},
                {"subject_code": "SCI101", "subject_name": "العلوم", "credit_hours": 3, "description": "مادة العلوم العامة"},
                {"subject_code": "HIST101", "subject_name": "التاريخ", "credit_hours": 2, "description": "مادة التاريخ"},
                {"subject_code": "GEO101", "subject_name": "الجغرافيا", "credit_hours": 2, "description": "مادة الجغرافيا"},
                {"subject_code": "REL101", "subject_name": "التربية الإسلامية", "credit_hours": 2, "description": "مادة التربية الإسلامية"},
                {"subject_code": "COMP101", "subject_name": "الحاسوب", "credit_hours": 2, "description": "مادة الحاسوب"}
            ]

            for subject_data in default_subjects:
                try:
                    # التحقق من عدم وجود المادة مسبقاً
                    existing = self.subject_model.get_subject_by_code(subject_data["subject_code"])
                    if not existing:
                        self.subject_model.add_subject(subject_data)
                        print(f"تم إنشاء المادة: {subject_data['subject_name']}")
                except Exception as e:
                    print(f"خطأ في إنشاء المادة {subject_data['subject_name']}: {e}")

        except Exception as e:
            print(f"خطأ في إنشاء المواد الافتراضية: {e}")

    def add_fallback_subjects(self):
        """إضافة مواد احتياطية للعرض فقط"""
        fallback_subjects = [
            ("الرياضيات", 1),
            ("اللغة العربية", 2),
            ("اللغة الإنجليزية", 3),
            ("العلوم", 4),
            ("التاريخ", 5),
            ("الجغرافيا", 6)
        ]

        self.subject_combo.clear()
        for subject_name, subject_id in fallback_subjects:
            self.subject_combo.addItem(subject_name, subject_id)

    def load_students_for_grading(self):
        """تحميل طلاب الصف للدرجات"""
        if self.class_combo.currentIndex() < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار صف أولاً")
            return

        try:
            class_id = self.class_combo.currentData()

            # استعلام طلاب الصف
            query = """
            SELECT student_id, student_number, first_name, last_name
            FROM students
            WHERE class_id = ? AND status = 'active'
            ORDER BY student_number
            """
            students = self.student_model.db_manager.fetch_all(query, (class_id,))

            self.populate_grades_table(students)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل الطلاب: {str(e)}")

    def populate_grades_table(self, students):
        """ملء جدول الدرجات بالطلاب"""
        self.grades_table.setRowCount(len(students))

        for row, student in enumerate(students):
            try:
                # رقم الطالب
                number_item = QTableWidgetItem(str(student['student_number']))
                number_item.setFlags(Qt.ItemIsEnabled)  # غير قابل للتعديل
                self.grades_table.setItem(row, 0, number_item)

                # اسم الطالب
                name = f"{student['first_name']} {student['last_name']}"
                name_item = QTableWidgetItem(name)
                name_item.setFlags(Qt.ItemIsEnabled)  # غير قابل للتعديل
                self.grades_table.setItem(row, 1, name_item)

                # الدرجة (قابلة للتعديل)
                grade_item = QTableWidgetItem("")
                self.grades_table.setItem(row, 2, grade_item)

                # النسبة المئوية (محسوبة تلقائياً)
                percentage_item = QTableWidgetItem("")
                percentage_item.setFlags(Qt.ItemIsEnabled)
                self.grades_table.setItem(row, 3, percentage_item)

                # التقدير (محسوب تلقائياً)
                grade_letter_item = QTableWidgetItem("")
                grade_letter_item.setFlags(Qt.ItemIsEnabled)
                self.grades_table.setItem(row, 4, grade_letter_item)

                # ملاحظات
                notes_item = QTableWidgetItem("")
                self.grades_table.setItem(row, 5, notes_item)

                # حفظ معرف الطالب في البيانات المخفية
                number_item.setData(Qt.UserRole, student['student_id'])

            except Exception as e:
                print(f"خطأ في إضافة الطالب رقم {row}: {e}")

    def calculate_grade_percentage(self, item):
        """حساب النسبة المئوية والتقدير عند تغيير الدرجة"""
        if item.column() != 2:  # فقط عند تغيير عمود الدرجة
            return

        try:
            grade_text = item.text().strip()
            if not grade_text:
                return

            grade = float(grade_text)
            max_score = self.max_score_spin.value()

            # التحقق من صحة الدرجة
            if grade < 0 or grade > max_score:
                QMessageBox.warning(self, "تحذير", f"الدرجة يجب أن تكون بين 0 و {max_score}")
                item.setText("")
                return

            # حساب النسبة المئوية
            percentage = (grade / max_score) * 100

            # تحديد التقدير
            grade_letter = self.get_grade_letter(percentage)

            # تحديث الجدول
            row = item.row()
            self.grades_table.item(row, 3).setText(f"{percentage:.1f}%")
            self.grades_table.item(row, 4).setText(grade_letter)

        except ValueError:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رقم صحيح للدرجة")
            item.setText("")

    def get_grade_letter(self, percentage):
        """تحديد التقدير بناءً على النسبة المئوية"""
        if percentage >= 90:
            return "ممتاز"
        elif percentage >= 80:
            return "جيد جداً"
        elif percentage >= 70:
            return "جيد"
        elif percentage >= 60:
            return "مقبول"
        else:
            return "راسب"

    def save_grades(self):
        """حفظ الدرجات"""
        if self.class_combo.currentIndex() < 0 or self.subject_combo.currentIndex() < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار الصف والمادة أولاً")
            return

        try:
            class_id = self.class_combo.currentData()
            subject_id = self.subject_combo.currentData()
            assessment_type = self.assessment_type_combo.currentText()
            max_score = self.max_score_spin.value()

            saved_count = 0

            for row in range(self.grades_table.rowCount()):
                student_id = self.grades_table.item(row, 0).data(Qt.UserRole)
                grade_text = self.grades_table.item(row, 2).text().strip()
                notes = self.grades_table.item(row, 5).text().strip()

                if grade_text:  # فقط إذا كانت هناك درجة
                    grade = float(grade_text)
                    percentage = (grade / max_score) * 100

                    # التحقق من وجود درجة سابقة
                    existing_query = """
                    SELECT result_id FROM results
                    WHERE student_id = ? AND subject_id = ? AND exam_type = ?
                    """
                    existing = self.student_model.db_manager.fetch_one(
                        existing_query, (student_id, subject_id, assessment_type)
                    )

                    if existing:
                        # تحديث الدرجة الموجودة
                        update_query = """
                        UPDATE results SET
                        score = ?, max_score = ?, percentage = ?, notes = ?
                        WHERE result_id = ?
                        """
                        params = (grade, max_score, percentage, notes, existing['result_id'])
                        self.student_model.db_manager.execute_query(update_query, params)
                    else:
                        # إدراج درجة جديدة
                        insert_query = """
                        INSERT INTO results (student_id, subject_id, exam_type,
                        score, max_score, percentage, notes, class_id, exam_date, academic_year, semester)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, DATE('now'), '2024-2025', 'الفصل الأول')
                        """
                        params = (student_id, subject_id, assessment_type,
                                grade, max_score, percentage, notes, class_id)
                        self.student_model.db_manager.execute_query(insert_query, params)

                    saved_count += 1

            QMessageBox.information(self, "نجح", f"تم حفظ {saved_count} درجة بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ الدرجات: {str(e)}")

    def clear_grades(self):
        """مسح الدرجات من الجدول"""
        reply = QMessageBox.question(
            self,
            "تأكيد المسح",
            "هل أنت متأكد من رغبتك في مسح جميع الدرجات؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            for row in range(self.grades_table.rowCount()):
                self.grades_table.item(row, 2).setText("")  # الدرجة
                self.grades_table.item(row, 3).setText("")  # النسبة
                self.grades_table.item(row, 4).setText("")  # التقدير
                self.grades_table.item(row, 5).setText("")  # ملاحظات

    def setup_reports_tab(self):
        """إعداد تبويب كشوف الدرجات"""
        layout = QVBoxLayout(self.reports_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)

        # شريط التحكم في التقارير
        reports_control_frame = QFrame()
        reports_control_layout = QHBoxLayout(reports_control_frame)

        # اختيار الطالب
        reports_control_layout.addWidget(QLabel("الطالب:"))
        self.student_combo = QComboBox()
        self.load_all_students()
        reports_control_layout.addWidget(self.student_combo)

        # اختيار الفصل الدراسي
        reports_control_layout.addWidget(QLabel("الفصل الدراسي:"))
        self.semester_combo = QComboBox()
        self.semester_combo.addItems(["الفصل الأول", "الفصل الثاني", "الفصل الصيفي"])
        reports_control_layout.addWidget(self.semester_combo)

        # أزرار التقارير
        self.view_report_card_button = QPushButton("عرض كشف الدرجات")
        self.print_report_card_button = QPushButton("طباعة كشف الدرجات")
        self.export_grades_button = QPushButton("تصدير الدرجات")

        report_button_style = """
            QPushButton {
                background-color: #2980b9;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1f618d;
            }
        """

        for button in [self.view_report_card_button, self.print_report_card_button, self.export_grades_button]:
            button.setStyleSheet(report_button_style)
            button.setFixedHeight(35)

        reports_control_layout.addStretch()
        reports_control_layout.addWidget(self.view_report_card_button)
        reports_control_layout.addWidget(self.print_report_card_button)
        reports_control_layout.addWidget(self.export_grades_button)

        layout.addWidget(reports_control_frame)

        # جدول كشف الدرجات
        self.report_card_table = QTableWidget()
        self.setup_report_card_table()
        layout.addWidget(self.report_card_table)

        # ربط أحداث التقارير
        self.setup_reports_connections()

    def setup_report_card_table(self):
        """إعداد جدول كشف الدرجات"""
        columns = [
            "المادة", "اختبار شهري", "اختبار نصف الفصل", "اختبار نهائي",
            "الواجبات", "المشاركة", "المجموع", "النسبة", "التقدير"
        ]

        self.report_card_table.setColumnCount(len(columns))
        self.report_card_table.setHorizontalHeaderLabels(columns)

        # إعداد خصائص الجدول
        self.report_card_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.report_card_table.setAlternatingRowColors(True)

        # تخصيص عرض الأعمدة
        header = self.report_card_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)

        # تطبيق الأنماط
        self.report_card_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
            }
            QTableWidget::item:selected {
                background-color: #2980b9;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

    def setup_reports_connections(self):
        """ربط أحداث التقارير"""
        self.view_report_card_button.clicked.connect(self.view_report_card)
        self.print_report_card_button.clicked.connect(self.print_report_card)
        self.export_grades_button.clicked.connect(self.export_grades)
        self.student_combo.currentTextChanged.connect(self.view_report_card)

    def load_all_students(self):
        """تحميل جميع الطلاب النشطين"""
        try:
            students = self.student_model.get_active_students()
            self.student_combo.clear()

            for student in students:
                name = f"{student['first_name']} {student['last_name']} - {student['student_number']}"
                self.student_combo.addItem(name, student['student_id'])

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل الطلاب: {str(e)}")

    def view_report_card(self):
        """عرض كشف درجات الطالب"""
        if self.student_combo.currentIndex() < 0:
            return

        try:
            student_id = self.student_combo.currentData()

            # استعلام درجات الطالب
            query = """
            SELECT r.*, s.subject_name
            FROM results r
            JOIN subjects s ON r.subject_id = s.subject_id
            WHERE r.student_id = ?
            ORDER BY s.subject_name, r.exam_type
            """
            results = self.student_model.db_manager.fetch_all(query, (student_id,))

            self.populate_report_card_table(results)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل كشف الدرجات: {str(e)}")

    def populate_report_card_table(self, results):
        """ملء جدول كشف الدرجات"""
        # تجميع النتائج حسب المادة
        subjects_data = {}

        for result in results:
            subject_name = result['subject_name']
            assessment_type = result['exam_type']
            score = result['score']

            if subject_name not in subjects_data:
                subjects_data[subject_name] = {}

            subjects_data[subject_name][assessment_type] = score

        # ملء الجدول
        self.report_card_table.setRowCount(len(subjects_data))

        row = 0
        for subject_name, assessments in subjects_data.items():
            # اسم المادة
            self.report_card_table.setItem(row, 0, QTableWidgetItem(subject_name))

            # الدرجات حسب نوع التقييم
            monthly_score = assessments.get('اختبار شهري', 0)
            midterm_score = assessments.get('اختبار نصف الفصل', 0)
            final_score = assessments.get('اختبار نهائي', 0)
            homework_score = assessments.get('واجب', 0)
            participation_score = assessments.get('مشاركة', 0)

            self.report_card_table.setItem(row, 1, QTableWidgetItem(str(monthly_score)))
            self.report_card_table.setItem(row, 2, QTableWidgetItem(str(midterm_score)))
            self.report_card_table.setItem(row, 3, QTableWidgetItem(str(final_score)))
            self.report_card_table.setItem(row, 4, QTableWidgetItem(str(homework_score)))
            self.report_card_table.setItem(row, 5, QTableWidgetItem(str(participation_score)))

            # حساب المجموع والنسبة والتقدير
            total_score = monthly_score + midterm_score + final_score + homework_score + participation_score
            max_total = 100  # افتراضي
            percentage = (total_score / max_total) * 100 if max_total > 0 else 0
            grade_letter = self.get_grade_letter(percentage)

            self.report_card_table.setItem(row, 6, QTableWidgetItem(f"{total_score:.1f}"))
            self.report_card_table.setItem(row, 7, QTableWidgetItem(f"{percentage:.1f}%"))
            self.report_card_table.setItem(row, 8, QTableWidgetItem(grade_letter))

            row += 1

    def print_report_card(self):
        """طباعة كشف الدرجات"""
        if self.student_combo.currentIndex() < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار طالب أولاً")
            return

        student_name = self.student_combo.currentText()
        student_id = self.student_combo.currentData()

        if not student_id:
            QMessageBox.warning(self, "تحذير", "لم يتم العثور على بيانات الطالب")
            return

        self.print_student_transcript(student_id, student_name)

    def print_student_transcript(self, student_id, student_name):
        """طباعة كشف درجات الطالب"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
            from PyQt5.QtGui import QPainter, QFont
            from PyQt5.QtCore import QRect
            from datetime import datetime

            # الحصول على درجات الطالب
            query = """
            SELECT s.subject_name, r.score as grade, r.exam_type, r.exam_date
            FROM results r
            JOIN subjects s ON r.subject_id = s.subject_id
            WHERE r.student_id = ?
            ORDER BY s.subject_name, r.exam_date
            """

            grades = self.student_model.db_manager.fetch_all(query, (student_id,))

            if not grades:
                QMessageBox.information(self, "معلومات", "لا توجد درجات مسجلة لهذا الطالب")
                return

            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)
            printer.setOrientation(QPrinter.Portrait)

            print_dialog = QPrintDialog(printer, self)
            if print_dialog.exec_() == QPrintDialog.Accepted:
                painter = QPainter(printer)

                # إعداد الخطوط
                title_font = QFont("Arial", 16, QFont.Bold)
                header_font = QFont("Arial", 14, QFont.Bold)
                normal_font = QFont("Arial", 12)

                y_pos = 100

                # عنوان الكشف
                painter.setFont(title_font)
                title_rect = QRect(0, y_pos, printer.width(), 50)
                painter.drawText(title_rect, Qt.AlignCenter, "كشف درجات الطالب")
                y_pos += 80

                # معلومات الطالب
                painter.setFont(header_font)
                painter.drawText(100, y_pos, f"اسم الطالب: {student_name}")
                y_pos += 40

                current_date = datetime.now().strftime("%Y-%m-%d")
                painter.drawText(100, y_pos, f"تاريخ الإصدار: {current_date}")
                y_pos += 60

                # خط فاصل
                painter.drawLine(100, y_pos, printer.width() - 100, y_pos)
                y_pos += 40

                # رؤوس الجدول
                painter.setFont(normal_font)
                painter.drawText(100, y_pos, "المادة")
                painter.drawText(300, y_pos, "نوع الامتحان")
                painter.drawText(500, y_pos, "الدرجة")
                painter.drawText(600, y_pos, "التاريخ")
                y_pos += 30

                # خط تحت الرؤوس
                painter.drawLine(100, y_pos, printer.width() - 100, y_pos)
                y_pos += 20

                # طباعة الدرجات
                for grade in grades:
                    painter.drawText(100, y_pos, str(grade['subject_name']))
                    painter.drawText(300, y_pos, str(grade['exam_type']))
                    painter.drawText(500, y_pos, str(grade['grade']))
                    painter.drawText(600, y_pos, str(grade['exam_date']))
                    y_pos += 25

                    # التحقق من عدم تجاوز الصفحة
                    if y_pos > printer.height() - 200:
                        break

                # خط فاصل سفلي
                y_pos += 20
                painter.drawLine(100, y_pos, printer.width() - 100, y_pos)
                y_pos += 40

                # توقيع
                painter.drawText(100, y_pos, "نظام إدارة المدارس")

                painter.end()
                QMessageBox.information(self, "نجح", "تم طباعة كشف الدرجات بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في طباعة كشف الدرجات: {str(e)}")

    def export_grades(self):
        """تصدير الدرجات"""
        try:
            import csv
            from PyQt5.QtWidgets import QFileDialog

            # الحصول على جميع الدرجات
            query = """
            SELECT st.first_name, st.last_name, st.student_number,
                   s.subject_name, r.score as grade, r.exam_type, r.exam_date
            FROM results r
            JOIN students st ON r.student_id = st.student_id
            JOIN subjects s ON r.subject_id = s.subject_id
            ORDER BY st.first_name, s.subject_name, r.exam_date
            """

            grades = self.student_model.db_manager.fetch_all(query)

            if not grades:
                QMessageBox.information(self, "معلومات", "لا توجد درجات لتصديرها")
                return

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "تصدير الدرجات",
                "درجات_الطلاب.csv",
                "CSV Files (*.csv);;All Files (*)"
            )

            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة رؤوس الأعمدة
                    headers = [
                        "الاسم الأول", "الاسم الأخير", "رقم الطالب",
                        "المادة", "الدرجة", "نوع الامتحان", "تاريخ الامتحان"
                    ]
                    writer.writerow(headers)

                    # كتابة البيانات
                    for grade in grades:
                        row = [
                            grade['first_name'],
                            grade['last_name'],
                            grade['student_number'],
                            grade['subject_name'],
                            grade['grade'],
                            grade['exam_type'],
                            grade['exam_date']
                        ]
                        writer.writerow(row)

                QMessageBox.information(self, "نجح", f"تم تصدير الدرجات بنجاح إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تصدير الدرجات: {str(e)}")

    def setup_statistics_tab(self):
        """إعداد تبويب الإحصائيات"""
        layout = QVBoxLayout(self.statistics_tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # عنوان الإحصائيات
        stats_title = QLabel("إحصائيات الأداء الأكاديمي")
        stats_title.setAlignment(Qt.AlignCenter)
        stats_title.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(stats_title)

        # إحصائيات سريعة
        stats_frame = QFrame()
        stats_frame.setFrameStyle(QFrame.StyledPanel)
        stats_layout = QHBoxLayout(stats_frame)

        # عدد الطلاب المتفوقين
        self.excellent_students_label = QLabel("الطلاب المتفوقون\n0 طالب")
        self.excellent_students_label.setAlignment(Qt.AlignCenter)
        self.excellent_students_label.setStyleSheet("""
            QLabel {
                background-color: #27ae60;
                color: white;
                padding: 20px;
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
            }
        """)

        # متوسط الدرجات
        self.average_grade_label = QLabel("متوسط الدرجات\n0.0%")
        self.average_grade_label.setAlignment(Qt.AlignCenter)
        self.average_grade_label.setStyleSheet("""
            QLabel {
                background-color: #3498db;
                color: white;
                padding: 20px;
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
            }
        """)

        # الطلاب المحتاجون لمساعدة
        self.struggling_students_label = QLabel("يحتاجون مساعدة\n0 طالب")
        self.struggling_students_label.setAlignment(Qt.AlignCenter)
        self.struggling_students_label.setStyleSheet("""
            QLabel {
                background-color: #e74c3c;
                color: white;
                padding: 20px;
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
            }
        """)

        stats_layout.addWidget(self.excellent_students_label)
        stats_layout.addWidget(self.average_grade_label)
        stats_layout.addWidget(self.struggling_students_label)

        layout.addWidget(stats_frame)

        # أزرار التقارير الإحصائية
        reports_buttons_frame = QFrame()
        reports_buttons_layout = QVBoxLayout(reports_buttons_frame)

        stat_button_style = """
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                padding: 15px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """

        self.class_performance_button = QPushButton("تقرير أداء الصفوف")
        self.class_performance_button.setStyleSheet(stat_button_style)

        self.subject_analysis_button = QPushButton("تحليل أداء المواد")
        self.subject_analysis_button.setStyleSheet(stat_button_style)

        self.grade_distribution_button = QPushButton("توزيع الدرجات")
        self.grade_distribution_button.setStyleSheet(stat_button_style)

        reports_buttons_layout.addWidget(self.class_performance_button)
        reports_buttons_layout.addWidget(self.subject_analysis_button)
        reports_buttons_layout.addWidget(self.grade_distribution_button)

        layout.addWidget(reports_buttons_frame)
        layout.addStretch()

        # ربط أحداث الإحصائيات
        self.setup_statistics_connections()

        # تحميل الإحصائيات
        self.load_academic_statistics()

    def setup_statistics_connections(self):
        """ربط أحداث الإحصائيات"""
        self.class_performance_button.clicked.connect(self.show_class_performance)
        self.subject_analysis_button.clicked.connect(self.show_subject_analysis)
        self.grade_distribution_button.clicked.connect(self.show_grade_distribution)

    def load_academic_statistics(self):
        """تحميل الإحصائيات الأكاديمية"""
        try:
            # عدد الطلاب المتفوقين (90% فأكثر)
            excellent_query = """
            SELECT COUNT(DISTINCT student_id) as count
            FROM results WHERE percentage >= 90
            """
            excellent_result = self.student_model.db_manager.fetch_one(excellent_query)
            excellent_count = excellent_result['count'] if excellent_result else 0
            self.excellent_students_label.setText(f"الطلاب المتفوقون\n{excellent_count} طالب")

            # متوسط الدرجات
            average_query = """
            SELECT AVG(percentage) as average
            FROM results
            """
            average_result = self.student_model.db_manager.fetch_one(average_query)
            average_grade = average_result['average'] if average_result['average'] else 0
            self.average_grade_label.setText(f"متوسط الدرجات\n{average_grade:.1f}%")

            # الطلاب المحتاجون لمساعدة (أقل من 60%)
            struggling_query = """
            SELECT COUNT(DISTINCT student_id) as count
            FROM results WHERE percentage < 60
            """
            struggling_result = self.student_model.db_manager.fetch_one(struggling_query)
            struggling_count = struggling_result['count'] if struggling_result else 0
            self.struggling_students_label.setText(f"يحتاجون مساعدة\n{struggling_count} طالب")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل الإحصائيات: {str(e)}")

    def show_class_performance(self):
        """عرض تقرير أداء الصفوف"""
        try:
            # الحصول على إحصائيات الصفوف
            query = """
            SELECT c.class_name,
                   COUNT(DISTINCT st.student_id) as student_count,
                   AVG(r.score) as average_grade,
                   MIN(r.score) as min_grade,
                   MAX(r.score) as max_grade
            FROM classes c
            LEFT JOIN students st ON c.class_id = st.class_id
            LEFT JOIN results r ON st.student_id = r.student_id
            GROUP BY c.class_id, c.class_name
            ORDER BY c.class_name
            """

            class_stats = self.student_model.db_manager.fetch_all(query)

            if not class_stats:
                QMessageBox.information(self, "معلومات", "لا توجد بيانات أداء للصفوف")
                return

            self.show_class_performance_dialog(class_stats)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في عرض تقرير أداء الصفوف: {str(e)}")

    def show_class_performance_dialog(self, class_stats):
        """عرض نافذة تقرير أداء الصفوف"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTableWidget, QTableWidgetItem, QPushButton, QLabel

        dialog = QDialog(self)
        dialog.setWindowTitle("تقرير أداء الصفوف")
        dialog.setModal(True)
        dialog.resize(700, 500)
        dialog.setLayoutDirection(Qt.RightToLeft)

        layout = QVBoxLayout(dialog)

        # عنوان التقرير
        title_label = QLabel("تقرير أداء الصفوف")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title_label)

        # جدول البيانات
        table = QTableWidget()
        table.setColumnCount(5)
        table.setHorizontalHeaderLabels([
            "اسم الصف", "عدد الطلاب", "المعدل العام", "أقل درجة", "أعلى درجة"
        ])

        table.setRowCount(len(class_stats))

        for row, stat in enumerate(class_stats):
            table.setItem(row, 0, QTableWidgetItem(str(stat['class_name'])))
            table.setItem(row, 1, QTableWidgetItem(str(stat['student_count'] or 0)))

            avg_grade = stat['average_grade']
            if avg_grade:
                table.setItem(row, 2, QTableWidgetItem(f"{avg_grade:.2f}"))
                table.setItem(row, 3, QTableWidgetItem(str(stat['min_grade'])))
                table.setItem(row, 4, QTableWidgetItem(str(stat['max_grade'])))
            else:
                table.setItem(row, 2, QTableWidgetItem("لا توجد درجات"))
                table.setItem(row, 3, QTableWidgetItem("-"))
                table.setItem(row, 4, QTableWidgetItem("-"))

        table.resizeColumnsToContents()
        layout.addWidget(table)

        # زر الإغلاق
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(dialog.accept)
        layout.addWidget(close_button)

        dialog.exec_()

    def show_subject_analysis(self):
        """عرض تحليل أداء المواد"""
        try:
            # الحصول على إحصائيات المواد
            query = """
            SELECT s.subject_name,
                   COUNT(r.result_id) as exam_count,
                   AVG(r.score) as average_grade,
                   MIN(r.score) as min_grade,
                   MAX(r.score) as max_grade
            FROM subjects s
            LEFT JOIN results r ON s.subject_id = r.subject_id
            GROUP BY s.subject_id, s.subject_name
            ORDER BY s.subject_name
            """

            subject_stats = self.student_model.db_manager.fetch_all(query)

            if not subject_stats:
                QMessageBox.information(self, "معلومات", "لا توجد بيانات أداء للمواد")
                return

            # عرض النتائج في رسالة مبسطة
            message = "تحليل أداء المواد:\n\n"
            for stat in subject_stats:
                avg = stat['average_grade']
                if avg:
                    message += f"• {stat['subject_name']}: معدل {avg:.2f} (من {stat['min_grade']} إلى {stat['max_grade']})\n"
                else:
                    message += f"• {stat['subject_name']}: لا توجد درجات\n"

            QMessageBox.information(self, "تحليل أداء المواد", message)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحليل أداء المواد: {str(e)}")

    def show_grade_distribution(self):
        """عرض توزيع الدرجات"""
        try:
            # الحصول على توزيع الدرجات
            query = """
            SELECT
                CASE
                    WHEN score >= 90 THEN 'ممتاز (90-100)'
                    WHEN score >= 80 THEN 'جيد جداً (80-89)'
                    WHEN score >= 70 THEN 'جيد (70-79)'
                    WHEN score >= 60 THEN 'مقبول (60-69)'
                    ELSE 'راسب (أقل من 60)'
                END as grade_range,
                COUNT(*) as count
            FROM results
            GROUP BY
                CASE
                    WHEN score >= 90 THEN 'ممتاز (90-100)'
                    WHEN score >= 80 THEN 'جيد جداً (80-89)'
                    WHEN score >= 70 THEN 'جيد (70-79)'
                    WHEN score >= 60 THEN 'مقبول (60-69)'
                    ELSE 'راسب (أقل من 60)'
                END
            ORDER BY MIN(score) DESC
            """

            distribution = self.student_model.db_manager.fetch_all(query)

            if not distribution:
                QMessageBox.information(self, "معلومات", "لا توجد درجات لعرض التوزيع")
                return

            # عرض التوزيع في رسالة
            message = "توزيع الدرجات:\n\n"
            total = sum(item['count'] for item in distribution)

            for item in distribution:
                count = item['count']
                percentage = (count / total) * 100 if total > 0 else 0
                message += f"• {item['grade_range']}: {count} طالب ({percentage:.1f}%)\n"

            message += f"\nإجمالي عدد الدرجات: {total}"

            QMessageBox.information(self, "توزيع الدرجات", message)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في عرض توزيع الدرجات: {str(e)}")
