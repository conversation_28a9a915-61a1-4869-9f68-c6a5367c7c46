#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة تعديل الجداول الزمنية
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QTableWidgetItem, QPushButton, QLabel, QFrame, 
                             QMessageBox, QHeaderView, QComboBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from src.models.subject import Subject
from src.models.class_model import ClassModel
from src.models.teacher import Teacher


class ScheduleDialog(QDialog):
    """نافذة تعديل الجداول الزمنية"""
    
    def __init__(self, class_id=None, parent=None):
        super().__init__(parent)
        self.class_id = class_id
        self.subject_model = Subject()
        self.class_model = ClassModel()
        self.teacher_model = Teacher()
        
        self.setup_ui()
        self.load_class_info()
        self.setup_schedule_table()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة
        self.setWindowTitle("تعديل الجدول الزمني")
        self.setFixedSize(800, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # عنوان النافذة
        self.title_label = QLabel("تعديل الجدول الزمني")
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(self.title_label)
        
        # معلومات الصف
        info_frame = QFrame()
        info_layout = QHBoxLayout(info_frame)
        
        self.class_info_label = QLabel("الصف: ")
        self.class_info_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #34495e;
                padding: 5px;
            }
        """)
        info_layout.addWidget(self.class_info_label)
        info_layout.addStretch()
        
        main_layout.addWidget(info_frame)
        
        # جدول الجدول الزمني
        self.schedule_table = QTableWidget()
        self.setup_table()
        main_layout.addWidget(self.schedule_table)
        
        # شريط الأدوات
        tools_frame = QFrame()
        tools_layout = QHBoxLayout(tools_frame)
        
        # اختيار المادة والمعلم لإضافة حصة
        tools_layout.addWidget(QLabel("المادة:"))
        self.subject_combo = QComboBox()
        self.load_subjects()
        tools_layout.addWidget(self.subject_combo)
        
        tools_layout.addWidget(QLabel("المعلم:"))
        self.teacher_combo = QComboBox()
        self.load_teachers()
        tools_layout.addWidget(self.teacher_combo)
        
        self.add_period_button = QPushButton("إضافة حصة")
        self.add_period_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        tools_layout.addWidget(self.add_period_button)
        
        self.remove_period_button = QPushButton("حذف حصة")
        self.remove_period_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        tools_layout.addWidget(self.remove_period_button)
        
        tools_layout.addStretch()
        
        main_layout.addWidget(tools_frame)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        self.save_button = QPushButton("حفظ الجدول")
        self.save_button.setFixedHeight(40)
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setFixedHeight(40)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.save_button)
        
        main_layout.addLayout(buttons_layout)
        
        # ربط الأحداث
        self.setup_connections()
        
    def setup_table(self):
        """إعداد جدول الجدول الزمني"""
        # أيام الأسبوع والحصص
        days = ["الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس"]
        periods = ["الحصة الأولى\n8:00-8:45", "الحصة الثانية\n8:45-9:30", 
                  "الحصة الثالثة\n9:45-10:30", "الحصة الرابعة\n10:30-11:15",
                  "الحصة الخامسة\n11:30-12:15", "الحصة السادسة\n12:15-1:00"]
        
        self.schedule_table.setRowCount(len(periods))
        self.schedule_table.setColumnCount(len(days))
        
        # تعيين عناوين الصفوف والأعمدة
        self.schedule_table.setVerticalHeaderLabels(periods)
        self.schedule_table.setHorizontalHeaderLabels(days)
        
        # إعداد خصائص الجدول
        self.schedule_table.setSelectionBehavior(QTableWidget.SelectItems)
        
        # تخصيص عرض الأعمدة والصفوف
        header = self.schedule_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        v_header = self.schedule_table.verticalHeader()
        v_header.setSectionResizeMode(QHeaderView.Stretch)
        
        # تطبيق الأنماط
        self.schedule_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #34495e;
                background-color: white;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 5px;
                text-align: center;
                border: 1px solid #bdc3c7;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #e67e22;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
                font-size: 10px;
            }
        """)
        
        # ملء الجدول بخلايا فارغة
        for row in range(len(periods)):
            for col in range(len(days)):
                item = QTableWidgetItem("")
                item.setTextAlignment(Qt.AlignCenter)
                self.schedule_table.setItem(row, col, item)
                
    def setup_connections(self):
        """ربط الأحداث"""
        self.add_period_button.clicked.connect(self.add_period)
        self.remove_period_button.clicked.connect(self.remove_period)
        self.save_button.clicked.connect(self.save_schedule)
        self.cancel_button.clicked.connect(self.reject)
        
    def load_class_info(self):
        """تحميل معلومات الصف"""
        if self.class_id:
            try:
                class_info = self.class_model.get_by_id(self.class_id)
                if class_info:
                    self.class_info_label.setText(
                        f"الصف: {class_info['class_name']} - {class_info['grade_level']} - {class_info['academic_year']}"
                    )
                    self.title_label.setText(f"تعديل الجدول الزمني - {class_info['class_name']}")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل معلومات الصف: {str(e)}")
                
    def load_subjects(self):
        """تحميل المواد الدراسية"""
        try:
            subjects = self.subject_model.get_active_subjects()
            self.subject_combo.clear()
            
            for subject in subjects:
                self.subject_combo.addItem(subject['subject_name'], subject['subject_id'])
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل المواد: {str(e)}")
            
    def load_teachers(self):
        """تحميل المعلمين"""
        try:
            teachers = self.teacher_model.get_active_teachers()
            self.teacher_combo.clear()
            
            for teacher in teachers:
                name = f"{teacher['first_name']} {teacher['last_name']}"
                self.teacher_combo.addItem(name, teacher['teacher_id'])
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل المعلمين: {str(e)}")
            
    def add_period(self):
        """إضافة حصة إلى الجدول"""
        current_item = self.schedule_table.currentItem()
        if not current_item:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار خلية في الجدول أولاً")
            return
            
        if self.subject_combo.currentIndex() < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مادة")
            return
            
        if self.teacher_combo.currentIndex() < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار معلم")
            return
            
        subject_name = self.subject_combo.currentText()
        teacher_name = self.teacher_combo.currentText()
        
        # إضافة المعلومات إلى الخلية
        period_text = f"{subject_name}\n{teacher_name}"
        current_item.setText(period_text)
        current_item.setData(Qt.UserRole, {
            'subject_id': self.subject_combo.currentData(),
            'teacher_id': self.teacher_combo.currentData()
        })
        
    def remove_period(self):
        """حذف حصة من الجدول"""
        current_item = self.schedule_table.currentItem()
        if not current_item:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار خلية في الجدول أولاً")
            return
            
        current_item.setText("")
        current_item.setData(Qt.UserRole, None)
        
    def save_schedule(self):
        """حفظ الجدول الزمني"""
        QMessageBox.information(
            self, 
            "حفظ الجدول", 
            "تم حفظ الجدول الزمني بنجاح!\n"
            "ملاحظة: هذه نسخة تجريبية، سيتم ربطها بقاعدة البيانات لاحقاً"
        )
        self.accept()
