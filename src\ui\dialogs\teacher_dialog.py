#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إضافة وتعديل المعلمين
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QLineEdit, QPushButton, QLabel, QFrame, QMessageBox,
                             QComboBox, QDateEdit, QTextEdit, QTabWidget, QWidget,
                             QDoubleSpinBox)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont

from src.models.teacher import Teacher
from src.models.currency import CurrencyModel
from src.utils.ui_styles import UIStyles


class TeacherDialog(QDialog):
    """نافذة إضافة وتعديل المعلمين"""
    
    # إشارة حفظ البيانات
    teacher_saved = pyqtSignal()
    
    def __init__(self, teacher_id=None, parent=None):
        super().__init__(parent)
        self.teacher_id = teacher_id
        self.teacher_model = Teacher()
        self.currency_model = CurrencyModel(self.teacher_model.db_manager)
        self.is_edit_mode = teacher_id is not None
        
        self.setup_ui()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.load_teacher_data()
            
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة
        title = "تعديل معلم" if self.is_edit_mode else "إضافة معلم جديد"
        self.setWindowTitle(title)
        self.setFixedSize(600, 700)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # عنوان النافذة
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # علامات التبويب
        self.tab_widget = QTabWidget()
        
        # تبويب البيانات الأساسية
        self.basic_tab = QWidget()
        self.setup_basic_tab()
        self.tab_widget.addTab(self.basic_tab, "البيانات الأساسية")
        
        # تبويب بيانات العمل
        self.work_tab = QWidget()
        self.setup_work_tab()
        self.tab_widget.addTab(self.work_tab, "بيانات العمل")
        
        # تبويب معلومات إضافية
        self.additional_tab = QWidget()
        self.setup_additional_tab()
        self.tab_widget.addTab(self.additional_tab, "معلومات إضافية")
        
        main_layout.addWidget(self.tab_widget)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        self.save_button = QPushButton("حفظ")
        self.save_button.setFixedHeight(40)
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setFixedHeight(40)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.save_button)
        
        main_layout.addLayout(buttons_layout)
        
    def setup_basic_tab(self):
        """إعداد تبويب البيانات الأساسية"""
        layout = QFormLayout(self.basic_tab)
        layout.setSpacing(15)
        
        # رقم الموظف
        self.employee_number_input = QLineEdit()
        self.employee_number_input.setPlaceholderText("سيتم توليده تلقائياً")
        self.employee_number_input.setReadOnly(True)
        self.employee_number_input.setStyleSheet("background-color: #f0f0f0; color: #666;")
        layout.addRow("رقم الموظف:", self.employee_number_input)
        
        # الاسم الأول
        self.first_name_input = QLineEdit()
        self.first_name_input.setPlaceholderText("أدخل الاسم الأول")
        layout.addRow("الاسم الأول *:", self.first_name_input)
        
        # الاسم الأخير
        self.last_name_input = QLineEdit()
        self.last_name_input.setPlaceholderText("أدخل الاسم الأخير")
        layout.addRow("الاسم الأخير *:", self.last_name_input)
        
        # تاريخ الميلاد
        self.date_of_birth_input = QDateEdit()
        self.date_of_birth_input.setDate(QDate.currentDate().addYears(-30))
        self.date_of_birth_input.setCalendarPopup(True)
        layout.addRow("تاريخ الميلاد:", self.date_of_birth_input)
        
        # الجنس
        self.gender_combo = QComboBox()
        self.gender_combo.addItems(["ذكر", "أنثى"])
        layout.addRow("الجنس:", self.gender_combo)
        
        # رقم الهوية
        self.national_id_input = QLineEdit()
        self.national_id_input.setPlaceholderText("أدخل رقم الهوية")
        layout.addRow("رقم الهوية:", self.national_id_input)
        
        # العنوان
        self.address_input = QLineEdit()
        self.address_input.setPlaceholderText("أدخل العنوان")
        layout.addRow("العنوان:", self.address_input)
        
        # رقم الهاتف
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("أدخل رقم الهاتف")
        layout.addRow("رقم الهاتف *:", self.phone_input)
        
        # البريد الإلكتروني
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("أدخل البريد الإلكتروني")
        layout.addRow("البريد الإلكتروني:", self.email_input)
        
    def setup_work_tab(self):
        """إعداد تبويب بيانات العمل"""
        layout = QFormLayout(self.work_tab)
        layout.setSpacing(15)
        
        # التخصص
        self.specialization_input = QLineEdit()
        self.specialization_input.setPlaceholderText("أدخل التخصص")
        layout.addRow("التخصص:", self.specialization_input)
        
        # المؤهل العلمي
        self.qualification_input = QLineEdit()
        self.qualification_input.setPlaceholderText("أدخل المؤهل العلمي")
        layout.addRow("المؤهل العلمي:", self.qualification_input)
        
        # المنصب
        self.position_combo = QComboBox()
        positions = ["معلم", "معلمة", "مدير", "مديرة", "وكيل", "وكيلة", 
                    "مرشد طلابي", "مرشدة طلابية", "أمين مكتبة", "سكرتير", "محاسب"]
        self.position_combo.addItems(positions)
        layout.addRow("المنصب *:", self.position_combo)
        
        # القسم
        self.department_input = QLineEdit()
        self.department_input.setPlaceholderText("أدخل القسم")
        layout.addRow("القسم:", self.department_input)
        
        # تاريخ التوظيف
        self.hire_date_input = QDateEdit()
        self.hire_date_input.setDate(QDate.currentDate())
        self.hire_date_input.setCalendarPopup(True)
        layout.addRow("تاريخ التوظيف *:", self.hire_date_input)
        
        # الراتب
        self.salary_input = QDoubleSpinBox()
        self.salary_input.setRange(0, 999999)
        self.salary_input.setValue(5000)
        layout.addRow("الراتب الأساسي:", self.salary_input)

        # العملة
        self.currency_combo = QComboBox()
        self.load_currencies()
        layout.addRow("العملة:", self.currency_combo)
        
    def setup_additional_tab(self):
        """إعداد تبويب المعلومات الإضافية"""
        layout = QFormLayout(self.additional_tab)
        layout.setSpacing(15)
        
        # الحالة
        self.status_combo = QComboBox()
        self.status_combo.addItems(["نشط", "غير نشط"])
        layout.addRow("الحالة:", self.status_combo)
        
        # ملاحظات
        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText("أدخل أي ملاحظات إضافية")
        self.notes_input.setMaximumHeight(100)
        layout.addRow("ملاحظات:", self.notes_input)

    def load_currencies(self):
        """تحميل العملات المتاحة"""
        try:
            currencies = self.currency_model.get_active_currencies()
            self.currency_combo.clear()

            for currency in currencies:
                display_text = f"{currency['currency_name']} ({currency['symbol']})"
                self.currency_combo.addItem(display_text, currency['currency_id'])

            # تحديد العملة الافتراضية
            default_currency = self.currency_model.get_default_currency()
            if default_currency:
                for i in range(self.currency_combo.count()):
                    if self.currency_combo.itemData(i) == default_currency['currency_id']:
                        self.currency_combo.setCurrentIndex(i)
                        break

        except Exception as e:
            print(f"خطأ في تحميل العملات: {e}")
            # إضافة عملة افتراضية في حالة الخطأ
            self.currency_combo.addItem("الريال السعودي (ر.س)", 1)

    def update_salary_suffix(self):
        """تحديث لاحقة الراتب حسب العملة المحددة"""
        try:
            currency_id = self.currency_combo.currentData()
            if currency_id:
                currency = self.currency_model.get_by_id(currency_id)
                if currency:
                    suffix = f" {currency['symbol']}"
                    self.salary_input.setSuffix(suffix)
                else:
                    self.salary_input.setSuffix(" ر.س")
            else:
                self.salary_input.setSuffix(" ر.س")
        except Exception as e:
            print(f"خطأ في تحديث لاحقة الراتب: {e}")
            self.salary_input.setSuffix(" ر.س")

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        self.save_button.clicked.connect(self.save_teacher)
        self.cancel_button.clicked.connect(self.reject)
        self.currency_combo.currentTextChanged.connect(self.update_salary_suffix)
        
    def load_teacher_data(self):
        """تحميل بيانات المعلم للتعديل"""
        try:
            teacher = self.teacher_model.get_by_id(self.teacher_id)
            if not teacher:
                QMessageBox.critical(self, "خطأ", "لم يتم العثور على المعلم")
                self.reject()
                return
                
            # ملء الحقول بالبيانات
            self.employee_number_input.setText(str(teacher['employee_number']))
            self.first_name_input.setText(str(teacher['first_name']))
            self.last_name_input.setText(str(teacher['last_name']))
            
            # تاريخ الميلاد
            if teacher['date_of_birth']:
                date = QDate.fromString(str(teacher['date_of_birth']), "yyyy-MM-dd")
                self.date_of_birth_input.setDate(date)
                
            # الجنس
            gender_index = 0 if teacher.get('gender') == 'male' else 1
            self.gender_combo.setCurrentIndex(gender_index)
            
            # باقي الحقول
            self.national_id_input.setText(str(teacher['national_id'] or ''))
            self.address_input.setText(str(teacher['address'] or ''))
            self.phone_input.setText(str(teacher['phone']))
            self.email_input.setText(str(teacher['email'] or ''))
            self.specialization_input.setText(str(teacher['specialization'] or ''))
            self.qualification_input.setText(str(teacher['qualification'] or ''))
            
            # المنصب
            position_text = str(teacher['position'])
            position_index = self.position_combo.findText(position_text)
            if position_index >= 0:
                self.position_combo.setCurrentIndex(position_index)
                
            self.department_input.setText(str(teacher['department'] or ''))
            
            # تاريخ التوظيف
            if teacher['hire_date']:
                date = QDate.fromString(str(teacher['hire_date']), "yyyy-MM-dd")
                self.hire_date_input.setDate(date)
                
            # الراتب
            if teacher['salary']:
                self.salary_input.setValue(float(teacher['salary']))

            # العملة
            if teacher.get('currency_id'):
                for i in range(self.currency_combo.count()):
                    if self.currency_combo.itemData(i) == teacher['currency_id']:
                        self.currency_combo.setCurrentIndex(i)
                        break

            # الحالة
            status_index = 0 if teacher.get('status') == 'active' else 1
            self.status_combo.setCurrentIndex(status_index)
            
            self.notes_input.setPlainText(str(teacher['notes'] or ''))
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل بيانات المعلم: {str(e)}")
            
    def save_teacher(self):
        """حفظ بيانات المعلم"""
        try:
            # جمع البيانات من النموذج
            teacher_data = {
                'first_name': self.first_name_input.text().strip(),
                'last_name': self.last_name_input.text().strip(),
                'date_of_birth': self.date_of_birth_input.date().toString("yyyy-MM-dd"),
                'gender': 'male' if self.gender_combo.currentIndex() == 0 else 'female',
                'national_id': self.national_id_input.text().strip() or None,
                'address': self.address_input.text().strip() or None,
                'phone': self.phone_input.text().strip(),
                'email': self.email_input.text().strip() or None,
                'specialization': self.specialization_input.text().strip() or None,
                'qualification': self.qualification_input.text().strip() or None,
                'position': self.position_combo.currentText(),
                'department': self.department_input.text().strip() or None,
                'hire_date': self.hire_date_input.date().toString("yyyy-MM-dd"),
                'salary': self.salary_input.value(),
                'currency_id': self.currency_combo.currentData(),
                'status': 'active' if self.status_combo.currentIndex() == 0 else 'inactive',
                'notes': self.notes_input.toPlainText().strip() or None
            }

            # إضافة رقم الموظف فقط في حالة التعديل
            if self.teacher_id and self.employee_number_input.text().strip():
                teacher_data['employee_number'] = self.employee_number_input.text().strip()
            
            # التحقق من الحقول المطلوبة (رقم الموظف سيتم توليده تلقائياً)
            required_fields = ['first_name', 'last_name', 'phone', 'position']
            for field in required_fields:
                if not teacher_data[field]:
                    field_names = {
                        'first_name': 'الاسم الأول',
                        'last_name': 'الاسم الأخير',
                        'phone': 'رقم الهاتف',
                        'position': 'المنصب'
                    }
                    QMessageBox.warning(self, "تحذير", f"الحقل '{field_names.get(field, field)}' مطلوب")
                    return
            
            # حفظ البيانات
            if self.is_edit_mode:
                self.teacher_model.update_teacher(self.teacher_id, teacher_data)
                QMessageBox.information(self, "نجح", "تم تحديث بيانات المعلم بنجاح")
            else:
                self.teacher_model.add_teacher(teacher_data)
                QMessageBox.information(self, "نجح", "تم إضافة المعلم بنجاح")
            
            # إرسال إشارة الحفظ
            self.teacher_saved.emit()
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ البيانات: {str(e)}")
