#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النافذة الرئيسية للتطبيق
تحتوي على القائمة الجانبية والتنقل بين الوحدات المختلفة
"""

from PyQt5.QtWidgets import (QMain<PERSON>indow, QWidget, QHBoxLayout, QVBoxLayout, 
                             QListWidget, QListWidgetItem, QStackedWidget,
                             QLabel, QPushButton, QFrame, QMenuBar, QStatusBar,
                             QMessageBox, QApplication)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QIcon, QFont, QPixmap

from src.utils.config import Config
from src.utils.font_manager import apply_font_to_widget
from src.utils.icon_manager import get_icon, set_window_icon
from src.ui.widgets.dashboard import DashboardWidget
from src.ui.widgets.students_widget import StudentsWidget
from src.ui.widgets.teachers_widget import TeachersWidget
from src.ui.widgets.subjects_widget import SubjectsWidget
from src.ui.widgets.classes_widget import ClassesWidget
from src.ui.widgets.fees_widget import FeesWidget
from src.ui.widgets.results_widget import ResultsWidget
from src.ui.widgets.reports_widget import ReportsWidget
from src.ui.widgets.settings_widget import SettingsWidget
from src.ui.widgets.users_widget import UsersWidget
from src.ui.widgets.currency_widget import CurrencyWidget


class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""
    
    # إشارات مخصصة
    user_logged_out = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.current_user = None
        self.setup_ui()
        self.setup_connections()
        self.load_saved_font()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة الرئيسية
        self.setWindowTitle(Config.WINDOW_TITLE)
        self.setMinimumSize(Config.WINDOW_MIN_WIDTH, Config.WINDOW_MIN_HEIGHT)
        self.setLayoutDirection(Qt.RightToLeft)

        # تعيين أيقونة النافذة
        set_window_icon(self)
        
        # إنشاء الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # إنشاء التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # إنشاء القائمة الجانبية
        self.create_sidebar()
        
        # إنشاء منطقة المحتوى الرئيسي
        self.create_main_content()
        
        # إضافة العناصر إلى التخطيط
        main_layout.addWidget(self.sidebar_frame)
        main_layout.addWidget(self.main_content)
        
        # إنشاء شريط القوائم
        self.create_menu_bar()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
        
        # تطبيق الأنماط
        self.apply_styles()
        
    def create_sidebar(self):
        """إنشاء القائمة الجانبية"""
        # إطار القائمة الجانبية
        self.sidebar_frame = QFrame()
        self.sidebar_frame.setFixedWidth(250)
        self.sidebar_frame.setFrameStyle(QFrame.StyledPanel)
        
        # تخطيط القائمة الجانبية
        sidebar_layout = QVBoxLayout(self.sidebar_frame)
        sidebar_layout.setContentsMargins(10, 10, 10, 10)
        sidebar_layout.setSpacing(5)
        
        # شعار التطبيق
        logo_label = QLabel("برنامج إدارة المدارس")
        logo_label.setAlignment(Qt.AlignCenter)
        apply_font_to_widget(logo_label, "header", bold=True)
        logo_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        sidebar_layout.addWidget(logo_label)
        
        # قائمة التنقل
        self.navigation_list = QListWidget()
        self.navigation_list.setFrameStyle(QFrame.NoFrame)
        
        # عناصر القائمة
        menu_items = [
            ("الرئيسية", "dashboard"),
            ("الطلاب", "students"),
            ("المعلمين", "teachers"),
            ("المواد الدراسية", "subjects"),
            ("الصفوف والفصول", "classes"),
            ("الرسوم الدراسية", "fees"),
            ("النتائج", "results"),
            ("التقارير", "reports"),
            ("إدارة العملات", "currency"),
            ("المستخدمين", "users"),
            ("الإعدادات", "settings")
        ]
        
        for item_text, item_data in menu_items:
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, item_data)

            # إضافة أيقونة للعنصر
            icon = get_icon(item_data, 24)
            if icon:
                item.setIcon(icon)

            self.navigation_list.addItem(item)
        
        # تحديد العنصر الأول كافتراضي
        self.navigation_list.setCurrentRow(0)
        
        sidebar_layout.addWidget(self.navigation_list)
        
        # زر تسجيل الخروج
        self.logout_button = QPushButton("تسجيل الخروج")
        self.logout_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        sidebar_layout.addWidget(self.logout_button)
        
    def create_main_content(self):
        """إنشاء منطقة المحتوى الرئيسي"""
        # إطار المحتوى الرئيسي
        self.main_content = QFrame()
        
        # تخطيط المحتوى
        content_layout = QVBoxLayout(self.main_content)
        content_layout.setContentsMargins(10, 10, 10, 10)
        
        # شريط العنوان
        self.title_label = QLabel("الرئيسية")
        apply_font_to_widget(self.title_label, "title", bold=True)
        self.title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px 0;
                border-bottom: 2px solid #3498db;
                margin-bottom: 20px;
            }
        """)
        content_layout.addWidget(self.title_label)
        
        # منطقة المحتوى المتغير
        self.content_stack = QStackedWidget()
        
        # إنشاء الويدجتات المختلفة
        self.create_content_widgets()
        
        content_layout.addWidget(self.content_stack)
        
    def create_content_widgets(self):
        """إنشاء ويدجتات المحتوى المختلفة"""
        # لوحة المعلومات الرئيسية
        self.dashboard_widget = DashboardWidget()
        self.content_stack.addWidget(self.dashboard_widget)
        
        # ويدجت الطلاب
        self.students_widget = StudentsWidget()
        self.content_stack.addWidget(self.students_widget)
        
        # ويدجت المعلمين
        self.teachers_widget = TeachersWidget()
        self.content_stack.addWidget(self.teachers_widget)
        
        # ويدجت المواد الدراسية
        self.subjects_widget = SubjectsWidget()
        self.content_stack.addWidget(self.subjects_widget)
        
        # ويدجت الصفوف والفصول
        self.classes_widget = ClassesWidget()
        self.content_stack.addWidget(self.classes_widget)
        
        # ويدجت الرسوم
        self.fees_widget = FeesWidget()
        self.content_stack.addWidget(self.fees_widget)
        
        # ويدجت النتائج
        self.results_widget = ResultsWidget()
        self.content_stack.addWidget(self.results_widget)
        
        # ويدجت التقارير
        self.reports_widget = ReportsWidget()
        self.content_stack.addWidget(self.reports_widget)

        # ويدجت العملات
        self.currency_widget = CurrencyWidget()
        self.content_stack.addWidget(self.currency_widget)

        # ويدجت المستخدمين
        self.users_widget = UsersWidget()
        self.content_stack.addWidget(self.users_widget)

        # ويدجت الإعدادات
        self.settings_widget = SettingsWidget()
        self.content_stack.addWidget(self.settings_widget)

        # ربط إشارة تغيير العملة الافتراضية
        self.currency_widget.default_currency_changed.connect(self.fees_widget.refresh_currency_display)

        # ربط إشارة تحديث العملات مع شاشة المعلمين
        self.currency_widget.currencies_updated.connect(self.teachers_widget.refresh_currencies_and_reload)

        # ربط إشارة تحديث الخط
        self.settings_widget.font_changed.connect(self.on_font_changed)

    def on_font_changed(self, font):
        """معالجة تحديث الخط"""
        try:
            # تطبيق الخط على النافذة الرئيسية
            self.setFont(font)

            # تطبيق الخط على جميع الويدجتات
            widgets = [
                self.dashboard_widget,
                self.students_widget,
                self.teachers_widget,
                self.subjects_widget,
                self.classes_widget,
                self.fees_widget,
                self.results_widget,
                self.reports_widget,
                self.currency_widget,
                self.users_widget,
                self.settings_widget
            ]

            for widget in widgets:
                if widget:
                    self.apply_font_to_widget(widget, font)

            # تطبيق الخط على شريط القوائم والأدوات
            if hasattr(self, 'menuBar'):
                self.menuBar().setFont(font)

            if hasattr(self, 'toolbar'):
                self.toolbar.setFont(font)

            print(f"تم تطبيق الخط على جميع شاشات البرنامج: {font.family()} - {font.pointSize()}pt")

        except Exception as e:
            print(f"خطأ في تطبيق الخط: {e}")

    def apply_font_to_widget(self, widget, font):
        """تطبيق الخط على ويدجت وجميع عناصره الفرعية"""
        try:
            widget.setFont(font)

            # تطبيق الخط على جميع العناصر الفرعية
            for child in widget.findChildren(QWidget):
                child.setFont(font)

        except Exception as e:
            print(f"خطأ في تطبيق الخط على الويدجت: {e}")

    def load_saved_font(self):
        """تحميل الخط المحفوظ عند بدء التطبيق"""
        try:
            from PyQt5.QtCore import QSettings
            settings = QSettings("SchoolManagement", "Settings")

            # تحميل إعدادات الخط
            font_family = settings.value("font_family", "Arial")
            font_size = int(settings.value("font_size", 12))
            apply_on_startup = settings.value("apply_font_on_startup", True, type=bool)

            if apply_on_startup:
                # إنشاء وتطبيق الخط
                font = QFont(font_family, font_size)
                QApplication.instance().setFont(font)
                self.on_font_changed(font)
                print(f"تم تحميل الخط المحفوظ: {font_family} - {font_size}pt")

        except Exception as e:
            print(f"خطأ في تحميل الخط المحفوظ: {e}")

    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu('ملف')
        
        # قائمة العرض
        view_menu = menubar.addMenu('عرض')
        
        # قائمة المساعدة
        help_menu = menubar.addMenu('مساعدة')
        
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("مرحباً بك في برنامج إدارة المدارس")
        
    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # ربط تغيير العنصر المحدد في القائمة
        self.navigation_list.currentRowChanged.connect(self.change_content)
        
        # ربط زر تسجيل الخروج
        self.logout_button.clicked.connect(self.logout)
        
    def change_content(self, index):
        """تغيير المحتوى حسب العنصر المحدد"""
        if index < 0:
            return
            
        # الحصول على بيانات العنصر المحدد
        item = self.navigation_list.item(index)
        if not item:
            return
            
        item_data = item.data(Qt.UserRole)
        item_text = item.text()
        
        # تحديث العنوان
        self.title_label.setText(item_text)
        
        # تغيير المحتوى
        self.content_stack.setCurrentIndex(index)
        
        # تحديث شريط الحالة
        self.status_bar.showMessage(f"عرض: {item_text}")
        
    def logout(self):
        """تسجيل الخروج"""
        reply = QMessageBox.question(
            self, 
            'تأكيد تسجيل الخروج',
            'هل أنت متأكد من رغبتك في تسجيل الخروج؟',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.user_logged_out.emit()
            self.close()

    # دوال التنقل السريع للوحة المعلومات
    def switch_to_students(self):
        """التنقل إلى شاشة الطلاب"""
        self.navigation_list.setCurrentRow(1)  # الطلاب في الفهرس 1

    def switch_to_teachers(self):
        """التنقل إلى شاشة المعلمين"""
        self.navigation_list.setCurrentRow(2)  # المعلمين في الفهرس 2

    def switch_to_fees(self):
        """التنقل إلى شاشة الرسوم"""
        self.navigation_list.setCurrentRow(5)  # الرسوم في الفهرس 5

    def switch_to_reports(self):
        """التنقل إلى شاشة التقارير"""
        self.navigation_list.setCurrentRow(7)  # التقارير في الفهرس 7

    def apply_styles(self):
        """تطبيق الأنماط على النافذة"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
            
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
            }
            
            QListWidget {
                background-color: transparent;
                border: none;
                outline: none;
            }
            
            QListWidget::item {
                padding: 12px;
                margin: 2px;
                border-radius: 5px;
                color: #495057;
            }
            
            QListWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            
            QListWidget::item:hover {
                background-color: #e9ecef;
            }
            
            QListWidget::item:selected:hover {
                background-color: #2980b9;
            }
        """)
        
    def set_current_user(self, user):
        """تعيين المستخدم الحالي"""
        self.current_user = user
        # يمكن إضافة منطق لإظهار/إخفاء عناصر القائمة حسب الصلاحيات
        
    def closeEvent(self, event):
        """حدث إغلاق النافذة"""
        reply = QMessageBox.question(
            self,
            'تأكيد الإغلاق',
            'هل أنت متأكد من رغبتك في إغلاق البرنامج؟',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            event.accept()
        else:
            event.ignore()
