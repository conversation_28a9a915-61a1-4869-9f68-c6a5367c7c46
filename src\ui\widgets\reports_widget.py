#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ويدجت التقارير والإحصائيات
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                             QPushButton, QLabel, QFrame, QMessageBox, QComboBox,
                             QDateEdit, QTextEdit, QScrollArea, QGroupBox)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont, QPixmap, QIcon

from src.models.student import Student
from src.models.teacher import Teacher
from src.models.subject import Subject
from src.models.class_model import ClassModel


class ReportsWidget(QWidget):
    """ويدجت التقارير والإحصائيات"""

    def __init__(self):
        super().__init__()
        self.student_model = Student()
        self.teacher_model = Teacher()
        self.subject_model = Subject()
        self.class_model = ClassModel()
        self.setup_ui()
        self.load_statistics()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # عنوان التقارير
        title_label = QLabel("نظام التقارير والإحصائيات")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title_label)

        # منطقة التمرير للتقارير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # ويدجت المحتوى
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(20)

        # الإحصائيات السريعة
        self.setup_quick_stats(content_layout)

        # تقارير الطلاب
        self.setup_student_reports(content_layout)

        # تقارير المعلمين
        self.setup_teacher_reports(content_layout)

        # التقارير المالية
        self.setup_financial_reports(content_layout)

        # التقارير الأكاديمية
        self.setup_academic_reports(content_layout)

        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area)

    def setup_quick_stats(self, parent_layout):
        """إعداد الإحصائيات السريعة"""
        stats_group = QGroupBox("الإحصائيات السريعة")
        stats_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)

        stats_layout = QGridLayout(stats_group)
        stats_layout.setSpacing(15)

        # إحصائيات الطلاب
        self.total_students_label = QLabel("إجمالي الطلاب\n0")
        self.total_students_label.setAlignment(Qt.AlignCenter)
        self.total_students_label.setStyleSheet(self.get_stat_card_style("#3498db"))

        self.active_students_label = QLabel("الطلاب النشطون\n0")
        self.active_students_label.setAlignment(Qt.AlignCenter)
        self.active_students_label.setStyleSheet(self.get_stat_card_style("#27ae60"))

        # إحصائيات المعلمين
        self.total_teachers_label = QLabel("إجمالي المعلمين\n0")
        self.total_teachers_label.setAlignment(Qt.AlignCenter)
        self.total_teachers_label.setStyleSheet(self.get_stat_card_style("#9b59b6"))

        self.active_teachers_label = QLabel("المعلمون النشطون\n0")
        self.active_teachers_label.setAlignment(Qt.AlignCenter)
        self.active_teachers_label.setStyleSheet(self.get_stat_card_style("#e67e22"))

        # إحصائيات الصفوف والمواد
        self.total_classes_label = QLabel("إجمالي الصفوف\n0")
        self.total_classes_label.setAlignment(Qt.AlignCenter)
        self.total_classes_label.setStyleSheet(self.get_stat_card_style("#f39c12"))

        self.total_subjects_label = QLabel("إجمالي المواد\n0")
        self.total_subjects_label.setAlignment(Qt.AlignCenter)
        self.total_subjects_label.setStyleSheet(self.get_stat_card_style("#e74c3c"))

        # ترتيب البطاقات في الشبكة
        stats_layout.addWidget(self.total_students_label, 0, 0)
        stats_layout.addWidget(self.active_students_label, 0, 1)
        stats_layout.addWidget(self.total_teachers_label, 0, 2)
        stats_layout.addWidget(self.active_teachers_label, 1, 0)
        stats_layout.addWidget(self.total_classes_label, 1, 1)
        stats_layout.addWidget(self.total_subjects_label, 1, 2)

        parent_layout.addWidget(stats_group)

    def get_stat_card_style(self, color):
        """الحصول على أنماط بطاقات الإحصائيات"""
        return f"""
            QLabel {{
                background-color: {color};
                color: white;
                padding: 20px;
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
                min-height: 80px;
            }}
        """

    def setup_student_reports(self, parent_layout):
        """إعداد تقارير الطلاب"""
        student_group = QGroupBox("تقارير الطلاب")
        student_group.setStyleSheet(self.get_group_style())

        student_layout = QGridLayout(student_group)
        student_layout.setSpacing(10)

        # أزرار تقارير الطلاب
        student_list_btn = self.create_report_button("قائمة الطلاب", "#3498db")
        student_attendance_btn = self.create_report_button("تقرير الحضور", "#27ae60")
        student_grades_btn = self.create_report_button("تقرير الدرجات", "#9b59b6")
        student_fees_btn = self.create_report_button("تقرير الرسوم", "#f39c12")

        student_layout.addWidget(student_list_btn, 0, 0)
        student_layout.addWidget(student_attendance_btn, 0, 1)
        student_layout.addWidget(student_grades_btn, 1, 0)
        student_layout.addWidget(student_fees_btn, 1, 1)

        # ربط الأحداث
        student_list_btn.clicked.connect(self.generate_student_list_report)
        student_attendance_btn.clicked.connect(self.generate_attendance_report)
        student_grades_btn.clicked.connect(self.generate_grades_report)
        student_fees_btn.clicked.connect(self.generate_fees_report)

        parent_layout.addWidget(student_group)

    def setup_teacher_reports(self, parent_layout):
        """إعداد تقارير المعلمين"""
        teacher_group = QGroupBox("تقارير المعلمين")
        teacher_group.setStyleSheet(self.get_group_style())

        teacher_layout = QGridLayout(teacher_group)
        teacher_layout.setSpacing(10)

        # أزرار تقارير المعلمين
        teacher_list_btn = self.create_report_button("قائمة المعلمين", "#e67e22")
        teacher_schedule_btn = self.create_report_button("الجداول الزمنية", "#2980b9")
        teacher_salary_btn = self.create_report_button("تقرير الرواتب", "#27ae60")
        teacher_performance_btn = self.create_report_button("تقييم الأداء", "#8e44ad")

        teacher_layout.addWidget(teacher_list_btn, 0, 0)
        teacher_layout.addWidget(teacher_schedule_btn, 0, 1)
        teacher_layout.addWidget(teacher_salary_btn, 1, 0)
        teacher_layout.addWidget(teacher_performance_btn, 1, 1)

        # ربط الأحداث
        teacher_list_btn.clicked.connect(self.generate_teacher_list_report)
        teacher_schedule_btn.clicked.connect(self.generate_schedule_report)
        teacher_salary_btn.clicked.connect(self.generate_salary_report)
        teacher_performance_btn.clicked.connect(self.generate_performance_report)

        parent_layout.addWidget(teacher_group)

    def setup_financial_reports(self, parent_layout):
        """إعداد التقارير المالية"""
        financial_group = QGroupBox("التقارير المالية")
        financial_group.setStyleSheet(self.get_group_style())

        financial_layout = QGridLayout(financial_group)
        financial_layout.setSpacing(10)

        # أزرار التقارير المالية
        income_btn = self.create_report_button("تقرير الإيرادات", "#27ae60")
        expenses_btn = self.create_report_button("تقرير المصروفات", "#e74c3c")
        outstanding_btn = self.create_report_button("الرسوم المستحقة", "#f39c12")
        financial_summary_btn = self.create_report_button("الملخص المالي", "#34495e")

        financial_layout.addWidget(income_btn, 0, 0)
        financial_layout.addWidget(expenses_btn, 0, 1)
        financial_layout.addWidget(outstanding_btn, 1, 0)
        financial_layout.addWidget(financial_summary_btn, 1, 1)

        # ربط الأحداث
        income_btn.clicked.connect(self.generate_income_report)
        expenses_btn.clicked.connect(self.generate_expenses_report)
        outstanding_btn.clicked.connect(self.generate_outstanding_report)
        financial_summary_btn.clicked.connect(self.generate_financial_summary)

        parent_layout.addWidget(financial_group)

    def setup_academic_reports(self, parent_layout):
        """إعداد التقارير الأكاديمية"""
        academic_group = QGroupBox("التقارير الأكاديمية")
        academic_group.setStyleSheet(self.get_group_style())

        academic_layout = QGridLayout(academic_group)
        academic_layout.setSpacing(10)

        # أزرار التقارير الأكاديمية
        class_performance_btn = self.create_report_button("أداء الصفوف", "#9b59b6")
        subject_analysis_btn = self.create_report_button("تحليل المواد", "#3498db")
        exam_results_btn = self.create_report_button("نتائج الامتحانات", "#e67e22")
        academic_calendar_btn = self.create_report_button("التقويم الأكاديمي", "#1abc9c")

        academic_layout.addWidget(class_performance_btn, 0, 0)
        academic_layout.addWidget(subject_analysis_btn, 0, 1)
        academic_layout.addWidget(exam_results_btn, 1, 0)
        academic_layout.addWidget(academic_calendar_btn, 1, 1)

        # ربط الأحداث
        class_performance_btn.clicked.connect(self.generate_class_performance_report)
        subject_analysis_btn.clicked.connect(self.generate_subject_analysis_report)
        exam_results_btn.clicked.connect(self.generate_exam_results_report)
        academic_calendar_btn.clicked.connect(self.generate_academic_calendar)

        parent_layout.addWidget(academic_group)

    def get_group_style(self):
        """الحصول على أنماط المجموعات"""
        return """
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """

    def create_report_button(self, text, color):
        """إنشاء زر تقرير"""
        button = QPushButton(text)
        button.setFixedHeight(60)
        button.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                padding: 15px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.8)};
            }}
        """)
        return button

    def darken_color(self, color, factor=0.9):
        """تغميق اللون"""
        # تحويل بسيط للألوان الشائعة
        color_map = {
            "#3498db": "#2980b9",
            "#27ae60": "#229954",
            "#9b59b6": "#8e44ad",
            "#f39c12": "#e67e22",
            "#e74c3c": "#c0392b",
            "#e67e22": "#d35400",
            "#2980b9": "#1f618d",
            "#8e44ad": "#7d3c98",
            "#34495e": "#2c3e50",
            "#1abc9c": "#16a085"
        }
        return color_map.get(color, color)

    def load_statistics(self):
        """تحميل الإحصائيات السريعة"""
        try:
            # إحصائيات الطلاب
            total_students = len(self.student_model.get_all_students())
            active_students = len(self.student_model.get_active_students())

            self.total_students_label.setText(f"إجمالي الطلاب\n{total_students}")
            self.active_students_label.setText(f"الطلاب النشطون\n{active_students}")

            # إحصائيات المعلمين
            total_teachers = len(self.teacher_model.get_all_teachers())
            active_teachers = len(self.teacher_model.get_active_teachers())

            self.total_teachers_label.setText(f"إجمالي المعلمين\n{total_teachers}")
            self.active_teachers_label.setText(f"المعلمون النشطون\n{active_teachers}")

            # إحصائيات الصفوف والمواد
            total_classes = len(self.class_model.get_all_classes())
            total_subjects = len(self.subject_model.get_all_subjects())

            self.total_classes_label.setText(f"إجمالي الصفوف\n{total_classes}")
            self.total_subjects_label.setText(f"إجمالي المواد\n{total_subjects}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل الإحصائيات: {str(e)}")

    # دوال تقارير الطلاب
    def generate_student_list_report(self):
        """إنشاء تقرير قائمة الطلاب"""
        try:
            from src.ui.dialogs.report_dialog import ReportDialog

            # جلب بيانات الطلاب
            students = self.student_model.get_all_students()

            if not students:
                QMessageBox.information(self, "تنبيه", "لا توجد بيانات طلاب لإنشاء التقرير")
                return

            # إعداد بيانات التقرير
            report_data = {
                'title': 'تقرير قائمة الطلاب',
                'headers': ['رقم الطالب', 'الاسم الكامل', 'الصف', 'تاريخ الميلاد', 'ولي الأمر', 'رقم الهاتف', 'الحالة'],
                'data': []
            }

            for student in students:
                row = [
                    student.get('student_number', ''),
                    f"{student.get('first_name', '')} {student.get('last_name', '')}",
                    student.get('class_name', 'غير محدد'),
                    student.get('date_of_birth', ''),
                    student.get('parent_name', ''),
                    student.get('parent_phone', ''),
                    'نشط' if student.get('status') == 'active' else 'غير نشط'
                ]
                report_data['data'].append(row)

            # عرض التقرير
            dialog = ReportDialog(report_data, parent=self)
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في إنشاء التقرير: {str(e)}")

    def generate_attendance_report(self):
        """إنشاء تقرير الحضور"""
        try:
            # الحصول على إحصائيات الحضور (مبسطة)
            query = """
            SELECT COUNT(DISTINCT student_id) as total_students
            FROM students WHERE status = 'active'
            """

            result = self.student_model.db_manager.fetch_one(query)
            total_students = result['total_students'] if result else 0

            # رسالة مبسطة للحضور
            message = f"""تقرير الحضور المبسط:

إجمالي عدد الطلاب النشطين: {total_students}

ملاحظة: هذا تقرير مبسط.
لتقرير مفصل عن الحضور والغياب،
يرجى إضافة نظام تتبع الحضور أولاً."""

            QMessageBox.information(self, "تقرير الحضور", message)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في إنشاء تقرير الحضور: {str(e)}")

    def generate_grades_report(self):
        """إنشاء تقرير الدرجات"""
        try:
            # الحصول على إحصائيات الدرجات
            query = """
            SELECT COUNT(*) as total_grades, AVG(score) as average_grade,
                   MIN(score) as min_grade, MAX(score) as max_grade
            FROM results
            """

            result = self.student_model.db_manager.fetch_one(query)

            if result and result['total_grades'] > 0:
                message = f"""تقرير الدرجات:

إجمالي عدد الدرجات: {result['total_grades']}
المعدل العام: {result['average_grade']:.2f}
أقل درجة: {result['min_grade']}
أعلى درجة: {result['max_grade']}

للحصول على تقرير مفصل، يرجى استخدام شاشة النتائج."""
            else:
                message = "لا توجد درجات مسجلة في النظام حالياً."

            QMessageBox.information(self, "تقرير الدرجات", message)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في إنشاء تقرير الدرجات: {str(e)}")

    def generate_fees_report(self):
        """إنشاء تقرير الرسوم"""
        try:
            # الحصول على إحصائيات الرسوم
            query = """
            SELECT
                COUNT(*) as total_fees,
                SUM(CASE WHEN status = 'paid' THEN amount - COALESCE(discount, 0) ELSE 0 END) as total_paid,
                SUM(CASE WHEN status = 'pending' THEN amount - COALESCE(discount, 0) ELSE 0 END) as total_pending
            FROM fees
            """

            result = self.student_model.db_manager.fetch_one(query)

            if result and result['total_fees'] > 0:
                message = f"""تقرير الرسوم:

إجمالي عدد الرسوم: {result['total_fees']}
إجمالي المدفوع: {result['total_paid'] or 0:,.2f}
إجمالي المعلق: {result['total_pending'] or 0:,.2f}

للحصول على تقارير مفصلة، يرجى استخدام شاشة الرسوم الدراسية."""
            else:
                message = "لا توجد رسوم مسجلة في النظام حالياً."

            QMessageBox.information(self, "تقرير الرسوم", message)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في إنشاء تقرير الرسوم: {str(e)}")

    # دوال تقارير المعلمين
    def generate_teacher_list_report(self):
        """إنشاء تقرير قائمة المعلمين"""
        try:
            from src.ui.dialogs.report_dialog import ReportDialog

            # جلب بيانات المعلمين
            teachers = self.teacher_model.get_all_teachers()

            if not teachers:
                QMessageBox.information(self, "تنبيه", "لا توجد بيانات معلمين لإنشاء التقرير")
                return

            # إعداد بيانات التقرير
            report_data = {
                'title': 'تقرير قائمة المعلمين',
                'headers': ['رقم الموظف', 'الاسم الكامل', 'التخصص', 'المنصب', 'الراتب', 'تاريخ التوظيف', 'الحالة'],
                'data': []
            }

            for teacher in teachers:
                salary = teacher.get('salary', 0)
                salary_text = f"{salary:,.2f} ريال" if salary else "غير محدد"

                row = [
                    teacher.get('employee_number', ''),
                    f"{teacher.get('first_name', '')} {teacher.get('last_name', '')}",
                    teacher.get('specialization', ''),
                    teacher.get('position', ''),
                    salary_text,
                    teacher.get('hire_date', ''),
                    'نشط' if teacher.get('status') == 'active' else 'غير نشط'
                ]
                report_data['data'].append(row)

            # عرض التقرير
            dialog = ReportDialog(report_data, parent=self)
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في إنشاء التقرير: {str(e)}")

    def generate_schedule_report(self):
        """إنشاء تقرير الجداول الزمنية"""
        try:
            # الحصول على عدد المعلمين
            query = "SELECT COUNT(*) as count FROM teachers WHERE status = 'active'"
            result = self.student_model.db_manager.fetch_one(query)
            teacher_count = result['count'] if result else 0

            message = f"""تقرير الجداول الزمنية:

عدد المعلمين النشطين: {teacher_count}

ملاحظة: لإنشاء جداول زمنية مفصلة،
يرجى إضافة نظام إدارة الجداول الدراسية أولاً."""

            QMessageBox.information(self, "تقرير الجداول الزمنية", message)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")

    def generate_salary_report(self):
        """إنشاء تقرير الرواتب"""
        try:
            # الحصول على إحصائيات الرواتب
            query = """
            SELECT COUNT(*) as teacher_count,
                   AVG(salary) as avg_salary,
                   SUM(salary) as total_salary
            FROM teachers WHERE status = 'active' AND salary > 0
            """
            result = self.student_model.db_manager.fetch_one(query)

            if result and result['teacher_count'] > 0:
                message = f"""تقرير الرواتب:

عدد المعلمين: {result['teacher_count']}
متوسط الراتب: {result['avg_salary']:.2f}
إجمالي الرواتب: {result['total_salary']:.2f}

ملاحظة: هذا تقرير مبسط للرواتب المسجلة."""
            else:
                message = "لا توجد بيانات رواتب مسجلة في النظام."

            QMessageBox.information(self, "تقرير الرواتب", message)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")

    def generate_performance_report(self):
        """إنشاء تقرير تقييم الأداء"""
        message = """تقرير تقييم الأداء:

هذا التقرير يتطلب نظام تقييم أداء متكامل.

للحصول على تقييم أداء المعلمين، يرجى:
1. إضافة نظام تقييم الأداء
2. تسجيل معايير التقييم
3. إدخال تقييمات دورية

حالياً يمكن الاطلاع على أداء الطلاب من شاشة النتائج."""

        QMessageBox.information(self, "تقرير تقييم الأداء", message)

    # دوال التقارير المالية
    def generate_income_report(self):
        """إنشاء تقرير الإيرادات"""
        try:
            # الحصول على إجمالي الإيرادات من الرسوم المدفوعة
            query = """
            SELECT SUM(amount - COALESCE(discount, 0)) as total_income
            FROM fees WHERE status = 'paid'
            """
            result = self.student_model.db_manager.fetch_one(query)
            total_income = result['total_income'] if result and result['total_income'] else 0

            message = f"""تقرير الإيرادات:

إجمالي الإيرادات من الرسوم الدراسية: {total_income:,.2f}

ملاحظة: هذا التقرير يشمل الرسوم المدفوعة فقط.
للحصول على تقرير مالي شامل، يرجى استخدام شاشة الرسوم الدراسية."""

            QMessageBox.information(self, "تقرير الإيرادات", message)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")

    def generate_expenses_report(self):
        """إنشاء تقرير المصروفات"""
        try:
            # الحصول على إجمالي الرواتب كمصروفات أساسية
            query = """
            SELECT SUM(salary) as total_salaries, COUNT(*) as teacher_count
            FROM teachers WHERE status = 'active' AND salary > 0
            """
            result = self.student_model.db_manager.fetch_one(query)

            if result and result['total_salaries']:
                message = f"""تقرير المصروفات:

الرواتب الشهرية: {result['total_salaries']:,.2f}
عدد المعلمين: {result['teacher_count']}

ملاحظة: هذا تقرير مبسط يشمل الرواتب فقط.
لتقرير مصروفات شامل، يرجى إضافة نظام إدارة المصروفات."""
            else:
                message = "لا توجد بيانات مصروفات مسجلة في النظام."

            QMessageBox.information(self, "تقرير المصروفات", message)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")

    def generate_outstanding_report(self):
        """إنشاء تقرير الرسوم المستحقة"""
        try:
            # الحصول على الرسوم المعلقة
            query = """
            SELECT COUNT(*) as count, SUM(amount - COALESCE(discount, 0)) as total
            FROM fees WHERE status = 'pending'
            """
            result = self.student_model.db_manager.fetch_one(query)

            if result and result['count'] > 0:
                message = f"""تقرير الرسوم المستحقة:

عدد الرسوم المعلقة: {result['count']}
إجمالي المبلغ المستحق: {result['total']:,.2f}

للحصول على تفاصيل أكثر، يرجى استخدام شاشة الرسوم الدراسية."""
            else:
                message = "لا توجد رسوم مستحقة حالياً."

            QMessageBox.information(self, "تقرير الرسوم المستحقة", message)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")

    def generate_financial_summary(self):
        """إنشاء الملخص المالي"""
        try:
            # الحصول على ملخص مالي مبسط
            income_query = "SELECT SUM(amount - COALESCE(discount, 0)) as total FROM fees WHERE status = 'paid'"
            pending_query = "SELECT SUM(amount - COALESCE(discount, 0)) as total FROM fees WHERE status = 'pending'"
            salary_query = "SELECT SUM(salary) as total FROM teachers WHERE status = 'active' AND salary > 0"

            income_result = self.student_model.db_manager.fetch_one(income_query)
            pending_result = self.student_model.db_manager.fetch_one(pending_query)
            salary_result = self.student_model.db_manager.fetch_one(salary_query)

            total_income = income_result['total'] if income_result and income_result['total'] else 0
            total_pending = pending_result['total'] if pending_result and pending_result['total'] else 0
            total_salaries = salary_result['total'] if salary_result and salary_result['total'] else 0

            net_income = total_income - total_salaries

            message = f"""الملخص المالي:

الإيرادات المحصلة: {total_income:,.2f}
الرسوم المعلقة: {total_pending:,.2f}
إجمالي الرواتب: {total_salaries:,.2f}
صافي الدخل: {net_income:,.2f}

ملاحظة: هذا ملخص مبسط يشمل الرسوم والرواتب فقط."""

            QMessageBox.information(self, "الملخص المالي", message)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")

    # دوال التقارير الأكاديمية
    def generate_class_performance_report(self):
        """إنشاء تقرير أداء الصفوف"""
        message = """تقرير أداء الصفوف:

هذا التقرير متاح في شاشة النتائج.

للوصول إليه:
1. انتقل إلى شاشة النتائج
2. اختر تبويب "الإحصائيات"
3. انقر على "تقرير أداء الصفوف"

ستحصل على تقرير مفصل يشمل معدلات الصفوف وإحصائيات الأداء."""

        QMessageBox.information(self, "تقرير أداء الصفوف", message)

    def generate_subject_analysis_report(self):
        """إنشاء تقرير تحليل المواد"""
        message = """تقرير تحليل المواد:

هذا التقرير متاح في شاشة النتائج.

للوصول إليه:
1. انتقل إلى شاشة النتائج
2. اختر تبويب "الإحصائيات"
3. انقر على "تحليل أداء المواد"

ستحصل على تحليل مفصل لأداء الطلاب في كل مادة."""

        QMessageBox.information(self, "تقرير تحليل المواد", message)

    def generate_exam_results_report(self):
        """إنشاء تقرير نتائج الامتحانات"""
        try:
            # الحصول على إحصائيات الامتحانات
            query = """
            SELECT exam_type, COUNT(*) as count, AVG(score) as avg_grade
            FROM results
            GROUP BY exam_type
            ORDER BY exam_type
            """

            results = self.student_model.db_manager.fetch_all(query)

            if not results:
                message = "لا توجد نتائج امتحانات مسجلة في النظام."
            else:
                message = "تقرير نتائج الامتحانات:\n\n"
                for result in results:
                    exam_type = result['exam_type'] or "غير محدد"
                    count = result['count']
                    avg_grade = result['avg_grade']
                    message += f"• {exam_type}: {count} نتيجة، معدل {avg_grade:.2f}\n"

                message += "\nللحصول على تفاصيل أكثر، يرجى استخدام شاشة النتائج."

            QMessageBox.information(self, "تقرير نتائج الامتحانات", message)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")

    def generate_academic_calendar(self):
        """إنشاء التقويم الأكاديمي"""
        from datetime import datetime

        current_year = datetime.now().year

        message = f"""التقويم الأكاديمي {current_year}:

هذه ميزة متقدمة تتطلب إعداد:
• مواعيد الامتحانات
• الإجازات الرسمية
• الأنشطة المدرسية
• الفعاليات الخاصة

لإنشاء تقويم أكاديمي مفصل، يرجى:
1. إضافة نظام إدارة الأحداث
2. تسجيل المواعيد المهمة
3. تحديد الفترات الدراسية

حالياً يمكن الاطلاع على النتائج والرسوم من الشاشات المخصصة."""

        QMessageBox.information(self, "التقويم الأكاديمي", message)
