#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النموذج الأساسي لجميع النماذج
يحتوي على العمليات الأساسية المشتركة
"""

from datetime import datetime
from src.database.db_manager import DatabaseManager


class BaseModel:
    """النموذج الأساسي لجميع النماذج"""

    def __init__(self, db_manager=None):
        if db_manager is None:
            self.db_manager = DatabaseManager()
        else:
            self.db_manager = db_manager
        self.table_name = ""
        self.primary_key = "id"
        
    def get_all(self, where_clause="", params=None):
        """جلب جميع السجلات"""
        query = f"SELECT * FROM {self.table_name}"
        if where_clause:
            query += f" WHERE {where_clause}"
        query += " ORDER BY created_at DESC"
        
        return self.db_manager.fetch_all(query, params)
    
    def get_by_id(self, record_id):
        """جلب سجل بواسطة المعرف"""
        query = f"SELECT * FROM {self.table_name} WHERE {self.primary_key} = ?"
        return self.db_manager.fetch_one(query, (record_id,))
    
    def insert(self, data):
        """إدراج سجل جديد"""
        # إضافة تاريخ الإنشاء
        data['created_at'] = datetime.now()
        data['updated_at'] = datetime.now()
        
        # بناء الاستعلام
        columns = ', '.join(data.keys())
        placeholders = ', '.join(['?' for _ in data])
        query = f"INSERT INTO {self.table_name} ({columns}) VALUES ({placeholders})"
        
        cursor = self.db_manager.execute_query(query, list(data.values()))
        return cursor.lastrowid
    
    def update(self, record_id, data):
        """تحديث سجل موجود"""
        # إضافة تاريخ التحديث
        data['updated_at'] = datetime.now()
        
        # بناء الاستعلام
        set_clause = ', '.join([f"{key} = ?" for key in data.keys()])
        query = f"UPDATE {self.table_name} SET {set_clause} WHERE {self.primary_key} = ?"
        
        values = list(data.values()) + [record_id]
        self.db_manager.execute_query(query, values)
        return record_id
    
    def delete(self, record_id):
        """حذف سجل"""
        query = f"DELETE FROM {self.table_name} WHERE {self.primary_key} = ?"
        self.db_manager.execute_query(query, (record_id,))
        return True
    
    def soft_delete(self, record_id):
        """حذف ناعم (تعطيل السجل)"""
        data = {
            'is_active': False,
            'updated_at': datetime.now()
        }
        return self.update(record_id, data)
    
    def search(self, search_term, search_fields):
        """البحث في السجلات"""
        if not search_fields:
            return []
            
        # بناء شروط البحث
        conditions = []
        params = []
        
        for field in search_fields:
            conditions.append(f"{field} LIKE ?")
            params.append(f"%{search_term}%")
        
        where_clause = " OR ".join(conditions)
        return self.get_all(where_clause, params)
    
    def count(self, where_clause="", params=None):
        """عد السجلات"""
        query = f"SELECT COUNT(*) as count FROM {self.table_name}"
        if where_clause:
            query += f" WHERE {where_clause}"
            
        result = self.db_manager.fetch_one(query, params)
        return result['count'] if result else 0
    
    def exists(self, where_clause, params=None):
        """التحقق من وجود سجل"""
        return self.count(where_clause, params) > 0
    
    def get_paginated(self, page=1, per_page=50, where_clause="", params=None):
        """جلب السجلات مع التصفح"""
        offset = (page - 1) * per_page
        
        query = f"SELECT * FROM {self.table_name}"
        if where_clause:
            query += f" WHERE {where_clause}"
        query += f" ORDER BY created_at DESC LIMIT {per_page} OFFSET {offset}"
        
        records = self.db_manager.fetch_all(query, params)
        total = self.count(where_clause, params)
        
        return {
            'records': records,
            'total': total,
            'page': page,
            'per_page': per_page,
            'pages': (total + per_page - 1) // per_page
        }
    
    def validate_required_fields(self, data, required_fields):
        """التحقق من الحقول المطلوبة"""
        missing_fields = []
        for field in required_fields:
            if field not in data or not data[field]:
                missing_fields.append(field)
        
        if missing_fields:
            raise ValueError(f"الحقول التالية مطلوبة: {', '.join(missing_fields)}")
        
        return True
    
    def validate_unique_field(self, field_name, value, exclude_id=None):
        """التحقق من تفرد الحقل"""
        where_clause = f"{field_name} = ?"
        params = [value]
        
        if exclude_id:
            where_clause += f" AND {self.primary_key} != ?"
            params.append(exclude_id)
        
        if self.exists(where_clause, params):
            raise ValueError(f"القيمة '{value}' موجودة مسبقاً في الحقل '{field_name}'")
        
        return True
