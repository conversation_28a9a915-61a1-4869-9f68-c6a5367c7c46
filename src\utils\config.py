#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف إعدادات التطبيق
يحتوي على جميع الإعدادات والثوابت المستخدمة في التطبيق
"""

import os
from pathlib import Path

class Config:
    """فئة إعدادات التطبيق"""
    
    # مسارات المشروع
    PROJECT_ROOT = Path(__file__).parent.parent.parent
    SRC_DIR = PROJECT_ROOT / "src"
    UI_DIR = PROJECT_ROOT / "ui"
    DB_DIR = PROJECT_ROOT / "db"
    RESOURCES_DIR = PROJECT_ROOT / "resources"
    ICONS_DIR = RESOURCES_DIR / "icons"
    IMAGES_DIR = RESOURCES_DIR / "images"
    REPORTS_DIR = PROJECT_ROOT / "reports"
    
    # إعدادات قاعدة البيانات
    DATABASE_NAME = "school_management.db"
    DATABASE_PATH = DB_DIR / DATABASE_NAME

    # أدوار المستخدمين
    USER_ROLES = {
        'admin': 'مدير عام',
        'academic_manager': 'مدير أكاديمي',
        'financial_manager': 'مدير مالي',
        'teacher': 'معلم',
        'secretary': 'سكرتير',
        'user': 'مستخدم عادي'
    }

    # الصلاحيات المتاحة
    PERMISSIONS = {
        # صلاحيات الطلاب
        'view_students': 'عرض الطلاب',
        'add_students': 'إضافة طلاب',
        'edit_students': 'تعديل الطلاب',
        'delete_students': 'حذف الطلاب',
        'manage_student_attendance': 'إدارة حضور الطلاب',

        # صلاحيات المعلمين
        'view_teachers': 'عرض المعلمين',
        'add_teachers': 'إضافة معلمين',
        'edit_teachers': 'تعديل المعلمين',
        'delete_teachers': 'حذف المعلمين',
        'manage_teacher_salaries': 'إدارة رواتب المعلمين',

        # صلاحيات المواد الدراسية
        'view_subjects': 'عرض المواد',
        'add_subjects': 'إضافة مواد',
        'edit_subjects': 'تعديل المواد',
        'delete_subjects': 'حذف المواد',
        'manage_schedules': 'إدارة الجداول الزمنية',

        # صلاحيات الرسوم والمالية
        'view_fees': 'عرض الرسوم',
        'add_fees': 'إضافة رسوم',
        'edit_fees': 'تعديل الرسوم',
        'delete_fees': 'حذف الرسوم',
        'manage_payments': 'إدارة المدفوعات',
        'view_financial_reports': 'عرض التقارير المالية',

        # صلاحيات النتائج والدرجات
        'view_results': 'عرض النتائج',
        'add_results': 'إضافة نتائج',
        'edit_results': 'تعديل النتائج',
        'delete_results': 'حذف النتائج',
        'generate_report_cards': 'إنشاء كشوف الدرجات',

        # صلاحيات التقارير
        'view_reports': 'عرض التقارير',
        'generate_reports': 'إنشاء التقارير',
        'export_reports': 'تصدير التقارير',
        'print_reports': 'طباعة التقارير',

        # صلاحيات إدارة المستخدمين
        'view_users': 'عرض المستخدمين',
        'add_users': 'إضافة مستخدمين',
        'edit_users': 'تعديل المستخدمين',
        'delete_users': 'حذف المستخدمين',
        'manage_permissions': 'إدارة الصلاحيات',
        'change_user_passwords': 'تغيير كلمات مرور المستخدمين',

        # صلاحيات النظام
        'system_settings': 'إعدادات النظام',
        'backup_restore': 'النسخ الاحتياطي والاستعادة',
        'view_system_logs': 'عرض سجلات النظام',
        'database_access': 'الوصول لقاعدة البيانات',

        # صلاحيات متقدمة
        'manage_all_users': 'إدارة جميع المستخدمين',
        'system_admin': 'مدير النظام',
        'full_access': 'وصول كامل'
    }

    # الصلاحيات الافتراضية لكل دور
    DEFAULT_ROLE_PERMISSIONS = {
        'admin': [
            'view_students', 'add_students', 'edit_students', 'delete_students',
            'view_teachers', 'add_teachers', 'edit_teachers', 'delete_teachers',
            'view_subjects', 'add_subjects', 'edit_subjects', 'delete_subjects',
            'view_fees', 'add_fees', 'edit_fees', 'delete_fees',
            'view_results', 'add_results', 'edit_results', 'delete_results',
            'view_reports', 'generate_reports', 'export_reports',
            'view_users', 'add_users', 'edit_users', 'delete_users',
            'manage_permissions', 'system_settings', 'backup_restore',
            'manage_all_users', 'system_admin', 'full_access'
        ],

        'academic_manager': [
            'view_students', 'add_students', 'edit_students',
            'view_teachers', 'add_teachers', 'edit_teachers',
            'view_subjects', 'add_subjects', 'edit_subjects',
            'view_results', 'add_results', 'edit_results',
            'manage_schedules', 'generate_report_cards',
            'view_reports', 'generate_reports', 'export_reports'
        ],

        'financial_manager': [
            'view_students', 'view_fees', 'add_fees', 'edit_fees',
            'manage_payments', 'view_financial_reports',
            'view_reports', 'generate_reports', 'export_reports',
            'manage_teacher_salaries'
        ],

        'teacher': [
            'view_students', 'view_subjects', 'view_results',
            'add_results', 'edit_results', 'generate_report_cards',
            'manage_student_attendance'
        ],

        'secretary': [
            'view_students', 'add_students', 'edit_students',
            'view_teachers', 'view_subjects', 'view_fees',
            'manage_payments', 'view_reports'
        ],

        'user': [
            'view_students', 'view_teachers', 'view_subjects',
            'view_reports'
        ]
    }

    # قوالب الصلاحيات
    PERMISSION_TEMPLATES = {
        'admin': DEFAULT_ROLE_PERMISSIONS['admin'],
        'academic': DEFAULT_ROLE_PERMISSIONS['academic_manager'],
        'financial': DEFAULT_ROLE_PERMISSIONS['financial_manager'],
        'teacher': DEFAULT_ROLE_PERMISSIONS['teacher'],
        'user': DEFAULT_ROLE_PERMISSIONS['user']
    }

    @classmethod
    def get_user_role_name(cls, role_key):
        """الحصول على اسم الدور"""
        return cls.USER_ROLES.get(role_key, role_key)

    @classmethod
    def get_permission_name(cls, permission_key):
        """الحصول على اسم الصلاحية"""
        return cls.PERMISSIONS.get(permission_key, permission_key)

    @classmethod
    def get_default_permissions(cls, role):
        """الحصول على الصلاحيات الافتراضية للدور"""
        return cls.DEFAULT_ROLE_PERMISSIONS.get(role, [])

    # إعدادات كلمة المرور
    PASSWORD_MIN_LENGTH = 6

    @classmethod
    def is_admin_role(cls, role):
        """التحقق من كون الدور دور إداري"""
        admin_roles = ['admin', 'academic_manager', 'financial_manager']
        return role in admin_roles
    
    # إعدادات التطبيق
    APP_NAME = "برنامج إدارة المدارس"
    APP_VERSION = "1.0.0"
    APP_ORGANIZATION = "School Management Pro"
    
    # إعدادات الواجهة
    WINDOW_TITLE = "برنامج إدارة المدارس - School Management Pro"
    WINDOW_MIN_WIDTH = 1200
    WINDOW_MIN_HEIGHT = 800
    
    # إعدادات الخطوط
    DEFAULT_FONT_FAMILY = "Segoe UI"
    ARABIC_FONT_FAMILY = "Tahoma"
    DEFAULT_FONT_SIZE = 11
    SMALL_FONT_SIZE = 9
    MEDIUM_FONT_SIZE = 11
    LARGE_FONT_SIZE = 13
    HEADER_FONT_SIZE = 14
    TITLE_FONT_SIZE = 16
    LABEL_FONT_SIZE = 11
    BUTTON_FONT_SIZE = 11
    INPUT_FONT_SIZE = 11
    
    # إعدادات الألوان
    PRIMARY_COLOR = "#2c3e50"
    SECONDARY_COLOR = "#3498db"
    SUCCESS_COLOR = "#27ae60"
    WARNING_COLOR = "#f39c12"
    DANGER_COLOR = "#e74c3c"
    LIGHT_COLOR = "#ecf0f1"
    DARK_COLOR = "#34495e"
    
    # إعدادات الأمان
    PASSWORD_MIN_LENGTH = 6
    SESSION_TIMEOUT = 3600  # ساعة واحدة بالثواني
    
    # إعدادات التقارير
    REPORTS_PER_PAGE = 50
    PDF_PAGE_SIZE = "A4"
    
    # إعدادات النسخ الاحتياطي
    BACKUP_INTERVAL_DAYS = 7
    MAX_BACKUP_FILES = 10
    
    # أدوار المستخدمين
    USER_ROLES = {
        'admin': 'مدير النظام',
        'principal': 'مدير المدرسة', 
        'teacher': 'معلم',
        'accountant': 'محاسب',
        'secretary': 'سكرتير'
    }
    
    # صلاحيات المستخدمين
    PERMISSIONS = {
        'students_view': 'عرض الطلاب',
        'students_add': 'إضافة طلاب',
        'students_edit': 'تعديل الطلاب',
        'students_delete': 'حذف الطلاب',
        'teachers_view': 'عرض المعلمين',
        'teachers_add': 'إضافة معلمين',
        'teachers_edit': 'تعديل المعلمين',
        'teachers_delete': 'حذف المعلمين',
        'fees_view': 'عرض الرسوم',
        'fees_add': 'إضافة رسوم',
        'fees_edit': 'تعديل الرسوم',
        'fees_delete': 'حذف الرسوم',
        'results_view': 'عرض النتائج',
        'results_add': 'إضافة نتائج',
        'results_edit': 'تعديل النتائج',
        'results_delete': 'حذف النتائج',
        'reports_view': 'عرض التقارير',
        'reports_export': 'تصدير التقارير',
        'settings_view': 'عرض الإعدادات',
        'settings_edit': 'تعديل الإعدادات',
        'users_manage': 'إدارة المستخدمين'
    }
    
    # الصلاحيات الافتراضية لكل دور
    DEFAULT_ROLE_PERMISSIONS = {
        'admin': list(PERMISSIONS.keys()),  # جميع الصلاحيات
        'principal': [
            'students_view', 'students_add', 'students_edit',
            'teachers_view', 'teachers_add', 'teachers_edit',
            'fees_view', 'fees_add', 'fees_edit',
            'results_view', 'results_add', 'results_edit',
            'reports_view', 'reports_export',
            'settings_view', 'settings_edit'
        ],
        'teacher': [
            'students_view',
            'results_view', 'results_add', 'results_edit',
            'reports_view'
        ],
        'accountant': [
            'students_view',
            'fees_view', 'fees_add', 'fees_edit',
            'reports_view', 'reports_export'
        ],
        'secretary': [
            'students_view', 'students_add', 'students_edit',
            'teachers_view',
            'reports_view'
        ]
    }
    
    @classmethod
    def ensure_directories(cls):
        """التأكد من وجود جميع المجلدات المطلوبة"""
        directories = [
            cls.DB_DIR,
            cls.RESOURCES_DIR,
            cls.ICONS_DIR,
            cls.IMAGES_DIR,
            cls.REPORTS_DIR,
            cls.UI_DIR
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    @classmethod
    def get_database_url(cls):
        """الحصول على رابط قاعدة البيانات"""
        cls.ensure_directories()
        return f"sqlite:///{cls.DATABASE_PATH}"
