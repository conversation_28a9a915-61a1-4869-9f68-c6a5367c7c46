# دليل استكشاف الأخطاء وإصلاحها

## المشكلة التي تم حلها

### الخطأ: `argument 1 has unexpected type 'sqlite3.Row'`

**الوصف:** ظهور رسالة خطأ عند محاولة تسجيل الدخول تشير إلى نوع بيانات غير متوقع.

**السبب:** كانت الإشارة `login_successful` تتوقع نوع `dict` لكنها كانت تستقبل نوع `sqlite3.Row`.

**الحل:** تم تحويل `sqlite3.Row` إلى `dict` قبل إرسال الإشارة.

```python
# الكود القديم (مشكلة)
self.login_successful.emit(user)

# الكود الجديد (محلول)
user_dict = dict(user)
self.login_successful.emit(user_dict)
```

## مشاكل شائعة أخرى وحلولها

### 1. البرنامج لا يبدأ

**الأعراض:**
- رسالة خطأ عند تشغيل `python run.py`
- عدم ظهور أي نافذة

**الحلول:**
```bash
# تأكد من تثبيت Python 3.8+
python --version

# تأكد من تثبيت المتطلبات
pip install -r requirements.txt

# تشغيل الاختبار
python test_app.py
```

### 2. مشاكل في قاعدة البيانات

**الأعراض:**
- رسائل خطأ تتعلق بـ SQLite
- عدم ظهور البيانات

**الحلول:**
```bash
# حذف قاعدة البيانات وإعادة إنشائها
rm -rf db/
python run.py

# إنشاء بيانات وهمية جديدة
python generate_sample_data.py
```

### 3. مشاكل في الخطوط العربية

**الأعراض:**
- ظهور رموز غريبة بدلاً من النص العربي
- تحذيرات حول الخطوط

**الحلول:**
- **Windows:** تأكد من تثبيت خط "Tahoma"
- **Linux:** `sudo apt-get install fonts-dejavu-core`
- **macOS:** الخطوط العربية مثبتة افتراضياً

### 4. مشاكل في الصلاحيات

**الأعراض:**
- عدم القدرة على إنشاء ملفات
- رسائل خطأ حول الصلاحيات

**الحلول:**
```bash
# تأكد من صلاحيات الكتابة في مجلد المشروع
chmod -R 755 SchoolManagementPro/

# تشغيل البرنامج من مجلد المشروع
cd SchoolManagementPro
python run.py
```

### 5. مشاكل في واجهة المستخدم

**الأعراض:**
- عدم ظهور النوافذ بشكل صحيح
- مشاكل في التخطيط

**الحلول:**
- تأكد من تثبيت PyQt5 بشكل صحيح
- أعد تشغيل البرنامج
- تحقق من دقة الشاشة (يُنصح بـ 1920x1080 أو أعلى)

## أوامر مفيدة للتشخيص

### اختبار سريع للنظام
```bash
# اختبار Python
python --version

# اختبار PyQt5
python -c "from PyQt5.QtWidgets import QApplication; print('PyQt5 يعمل')"

# اختبار قاعدة البيانات
python -c "import sqlite3; print('SQLite يعمل')"

# تشغيل اختبار شامل
python test_app.py
```

### إعادة تعيين البرنامج
```bash
# حذف قاعدة البيانات
rm -rf db/

# إعادة تثبيت المتطلبات
pip uninstall -r requirements.txt -y
pip install -r requirements.txt

# إعادة إنشاء البيانات
python generate_sample_data.py
```

## معلومات النظام المطلوبة

عند طلب المساعدة، يرجى تقديم المعلومات التالية:

```bash
# معلومات النظام
python --version
pip list | grep PyQt
uname -a  # Linux/macOS
systeminfo | findstr "OS"  # Windows

# معلومات الخطأ
python run.py 2>&1 | tee error.log
```

## بيانات الدخول الافتراضية

```
اسم المستخدم: admin
كلمة المرور: admin123
```

## ملفات السجل

البرنامج يطبع رسائل الخطأ في وحدة التحكم. لحفظ السجل:

```bash
# Linux/macOS
python run.py 2>&1 | tee app.log

# Windows
python run.py > app.log 2>&1
```

## الحصول على المساعدة

1. راجع هذا الدليل أولاً
2. شغل `python test_app.py` للتشخيص
3. تحقق من ملف `README.md`
4. راجع ملف `docs/user_guide.md`

## نصائح للأداء الأمثل

1. **استخدم SSD** لتحسين أداء قاعدة البيانات
2. **أغلق البرامج الأخرى** لتوفير الذاكرة
3. **استخدم دقة شاشة مناسبة** (1920x1080 أو أعلى)
4. **تأكد من تحديث النظام** وبرامج التشغيل

---

**آخر تحديث:** 2025-01-29  
**الإصدار:** 1.0.1
