#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة sqlite3.Row
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.models.student import Student
from src.models.teacher import Teacher
from src.models.user import User
from src.models.class_model import ClassModel
from src.database.db_manager import DatabaseManager


def test_database_returns_dicts():
    """اختبار أن قاعدة البيانات ترجع قواميس وليس sqlite3.Row"""
    print("=== اختبار إرجاع قواميس من قاعدة البيانات ===")
    try:
        db_manager = DatabaseManager()
        
        # اختبار fetch_one
        user = db_manager.fetch_one("SELECT * FROM users LIMIT 1")
        if user:
            print(f"✓ fetch_one يرجع: {type(user)}")
            if isinstance(user, dict):
                print("✓ fetch_one يرجع قاموس")
            else:
                print("✗ fetch_one لا يرجع قاموس")
                return False
        
        # اختبار fetch_all
        users = db_manager.fetch_all("SELECT * FROM users LIMIT 3")
        if users:
            print(f"✓ fetch_all يرجع: {type(users)} من {len(users)} عنصر")
            if isinstance(users[0], dict):
                print("✓ fetch_all يرجع قائمة من القواميس")
            else:
                print("✗ fetch_all لا يرجع قائمة من القواميس")
                return False
        
        return True
    except Exception as e:
        print(f"✗ خطأ في اختبار قاعدة البيانات: {e}")
        return False


def test_models_return_dicts():
    """اختبار أن النماذج ترجع قواميس"""
    print("\n=== اختبار النماذج ترجع قواميس ===")
    try:
        # اختبار نموذج المستخدمين
        user_model = User()
        users = user_model.get_all()
        if users:
            print(f"✓ نموذج المستخدمين يرجع: {type(users[0])}")
            if isinstance(users[0], dict):
                print("✓ نموذج المستخدمين يرجع قواميس")
            else:
                print("✗ نموذج المستخدمين لا يرجع قواميس")
                return False
        
        # اختبار نموذج الطلاب
        student_model = Student()
        students = student_model.get_all()
        if students:
            print(f"✓ نموذج الطلاب يرجع: {type(students[0])}")
            if isinstance(students[0], dict):
                print("✓ نموذج الطلاب يرجع قواميس")
            else:
                print("✗ نموذج الطلاب لا يرجع قواميس")
                return False
        
        # اختبار نموذج المعلمين
        teacher_model = Teacher()
        teachers = teacher_model.get_all()
        if teachers:
            print(f"✓ نموذج المعلمين يرجع: {type(teachers[0])}")
            if isinstance(teachers[0], dict):
                print("✓ نموذج المعلمين يرجع قواميس")
            else:
                print("✗ نموذج المعلمين لا يرجع قواميس")
                return False
        
        # اختبار نموذج الصفوف
        class_model = ClassModel()
        classes = class_model.get_all()
        if classes:
            print(f"✓ نموذج الصفوف يرجع: {type(classes[0])}")
            if isinstance(classes[0], dict):
                print("✓ نموذج الصفوف يرجع قواميس")
            else:
                print("✗ نموذج الصفوف لا يرجع قواميس")
                return False
        
        return True
    except Exception as e:
        print(f"✗ خطأ في اختبار النماذج: {e}")
        return False


def test_data_access():
    """اختبار الوصول للبيانات باستخدام المفاتيح"""
    print("\n=== اختبار الوصول للبيانات ===")
    try:
        # اختبار الوصول لبيانات المستخدمين
        user_model = User()
        users = user_model.get_all()
        if users:
            user = users[0]
            try:
                username = user['username']
                user_id = user.get('user_id')
                print(f"✓ الوصول لبيانات المستخدم: {username} (ID: {user_id})")
            except Exception as e:
                print(f"✗ خطأ في الوصول لبيانات المستخدم: {e}")
                return False
        
        # اختبار الوصول لبيانات الطلاب
        student_model = Student()
        students = student_model.get_all()
        if students:
            student = students[0]
            try:
                student_name = f"{student['first_name']} {student['last_name']}"
                student_id = student.get('student_id')
                print(f"✓ الوصول لبيانات الطالب: {student_name} (ID: {student_id})")
            except Exception as e:
                print(f"✗ خطأ في الوصول لبيانات الطالب: {e}")
                return False
        
        # اختبار الوصول لبيانات المعلمين
        teacher_model = Teacher()
        teachers = teacher_model.get_all()
        if teachers:
            teacher = teachers[0]
            try:
                teacher_name = f"{teacher['first_name']} {teacher['last_name']}"
                teacher_id = teacher.get('teacher_id')
                print(f"✓ الوصول لبيانات المعلم: {teacher_name} (ID: {teacher_id})")
            except Exception as e:
                print(f"✗ خطأ في الوصول لبيانات المعلم: {e}")
                return False
        
        # اختبار الوصول لبيانات الصفوف
        class_model = ClassModel()
        classes = class_model.get_all()
        if classes:
            class_data = classes[0]
            try:
                class_name = class_data['class_name']
                class_id = class_data.get('class_id')
                print(f"✓ الوصول لبيانات الصف: {class_name} (ID: {class_id})")
            except Exception as e:
                print(f"✗ خطأ في الوصول لبيانات الصف: {e}")
                return False
        
        return True
    except Exception as e:
        print(f"✗ خطأ في اختبار الوصول للبيانات: {e}")
        return False


def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار إصلاح مشكلة sqlite3.Row")
    print("=" * 50)
    
    tests = [
        test_database_returns_dicts,
        test_models_return_dicts,
        test_data_access
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبار: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 تم إصلاح مشكلة sqlite3.Row بنجاح!")
        print("\n✅ الإصلاحات المطبقة:")
        print("   - تحويل sqlite3.Row إلى قواميس في db_manager")
        print("   - استخدام .get() بدلاً من [] في الويدجتات")
        print("   - إصلاح جميع دوال الوصول للبيانات")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
