#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ويدجت لوحة المعلومات الرئيسية
يعرض إحصائيات سريعة ومعلومات مهمة
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                             QLabel, QPushButton, QFrame, QScrollArea)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QPalette

from src.models.student import Student
from src.models.teacher import Teacher
from src.utils.config import Config


class StatCard(QFrame):
    """بطاقة إحصائية"""
    
    def __init__(self, title, value, color="#3498db"):
        super().__init__()
        self.setup_ui(title, value, color)
        
    def setup_ui(self, title, value, color):
        """إعداد واجهة البطاقة"""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setFixedHeight(120)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {color};
                font-size: 14px;
                font-weight: bold;
                margin-bottom: 10px;
            }}
        """)
        
        # القيمة
        value_label = QLabel(str(value))
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                font-size: 28px;
                font-weight: bold;
            }
        """)
        
        layout.addWidget(title_label)
        layout.addWidget(value_label)
        
        # تطبيق الأنماط
        self.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 2px solid {color};
                border-radius: 10px;
            }}
            QFrame:hover {{
                background-color: #f8f9fa;
            }}
        """)


class QuickActionButton(QPushButton):
    """زر إجراء سريع"""
    
    def __init__(self, text, color="#3498db"):
        super().__init__(text)
        self.setup_style(color)
        
    def setup_style(self, color):
        """إعداد أنماط الزر"""
        self.setFixedHeight(50)
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.8)};
            }}
        """)
        
    def darken_color(self, color, factor=0.9):
        """تغميق اللون"""
        # تحويل بسيط للون - يمكن تحسينه
        if color == "#3498db":
            return "#2980b9" if factor == 0.9 else "#21618c"
        elif color == "#27ae60":
            return "#229954" if factor == 0.9 else "#1e8449"
        elif color == "#e74c3c":
            return "#c0392b" if factor == 0.9 else "#a93226"
        elif color == "#f39c12":
            return "#d68910" if factor == 0.9 else "#b7950b"
        return color


class DashboardWidget(QWidget):
    """ويدجت لوحة المعلومات الرئيسية"""
    
    def __init__(self):
        super().__init__()
        self.student_model = Student()
        self.teacher_model = Teacher()
        self.setup_ui()
        self.load_statistics()
        
        # تحديث الإحصائيات كل 5 دقائق
        self.timer = QTimer()
        self.timer.timeout.connect(self.load_statistics)
        self.timer.start(300000)  # 5 دقائق
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameStyle(QFrame.NoFrame)
        
        # الويدجت الرئيسي
        main_widget = QWidget()
        scroll_area.setWidget(main_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # عنوان الترحيب
        welcome_label = QLabel("مرحباً بك في برنامج إدارة المدارس")
        welcome_label.setAlignment(Qt.AlignCenter)
        welcome_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background-color: white;
                border-radius: 10px;
                border: 2px solid #3498db;
            }
        """)
        main_layout.addWidget(welcome_label)
        
        # قسم الإحصائيات
        stats_frame = QFrame()
        stats_frame.setFrameStyle(QFrame.StyledPanel)
        stats_layout = QVBoxLayout(stats_frame)
        
        stats_title = QLabel("الإحصائيات السريعة")
        stats_title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
            }
        """)
        stats_layout.addWidget(stats_title)
        
        # شبكة الإحصائيات
        self.stats_grid = QGridLayout()
        stats_layout.addLayout(self.stats_grid)
        
        main_layout.addWidget(stats_frame)
        
        # قسم الإجراءات السريعة
        actions_frame = QFrame()
        actions_frame.setFrameStyle(QFrame.StyledPanel)
        actions_layout = QVBoxLayout(actions_frame)
        
        actions_title = QLabel("الإجراءات السريعة")
        actions_title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
            }
        """)
        actions_layout.addWidget(actions_title)
        
        # شبكة الإجراءات
        actions_grid = QGridLayout()
        
        # أزرار الإجراءات السريعة
        self.add_student_btn = QuickActionButton("إضافة طالب جديد", "#27ae60")
        self.add_teacher_btn = QuickActionButton("إضافة معلم جديد", "#3498db")
        self.view_fees_btn = QuickActionButton("عرض الرسوم المستحقة", "#f39c12")
        self.generate_report_btn = QuickActionButton("إنشاء تقرير", "#9b59b6")

        # ربط الأزرار بالوظائف
        self.add_student_btn.clicked.connect(self.add_student)
        self.add_teacher_btn.clicked.connect(self.add_teacher)
        self.view_fees_btn.clicked.connect(self.view_fees)
        self.generate_report_btn.clicked.connect(self.generate_report)

        actions_grid.addWidget(self.add_student_btn, 0, 0)
        actions_grid.addWidget(self.add_teacher_btn, 0, 1)
        actions_grid.addWidget(self.view_fees_btn, 1, 0)
        actions_grid.addWidget(self.generate_report_btn, 1, 1)
        
        actions_layout.addLayout(actions_grid)
        main_layout.addWidget(actions_frame)
        
        # إضافة مساحة مرنة
        main_layout.addStretch()
        
        # تخطيط النافذة الرئيسية
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(scroll_area)
        
    def load_statistics(self):
        """تحميل الإحصائيات"""
        try:
            # مسح الإحصائيات السابقة
            self.clear_stats_grid()

            # إحصائيات الطلاب
            try:
                student_stats = self.student_model.get_students_statistics()
                print(f"إحصائيات الطلاب: {student_stats}")
            except Exception as e:
                print(f"خطأ في تحميل إحصائيات الطلاب: {e}")
                student_stats = {'active_students': 0, 'by_gender': {'male': 0, 'female': 0}}

            # إحصائيات المعلمين
            try:
                teacher_stats = self.teacher_model.get_teachers_statistics()
                print(f"إحصائيات المعلمين: {teacher_stats}")
            except Exception as e:
                print(f"خطأ في تحميل إحصائيات المعلمين: {e}")
                teacher_stats = {'active_teachers': 0, 'by_gender': {'male': 0, 'female': 0}}

            # إضافة بطاقات الإحصائيات مع التحقق من البيانات
            total_students = student_stats.get('active_students', 0) or student_stats.get('total_active', 0)
            total_teachers = teacher_stats.get('active_teachers', 0) or teacher_stats.get('total_active', 0)

            male_students = student_stats.get('by_gender', {}).get('male', 0)
            female_students = student_stats.get('by_gender', {}).get('female', 0)
            male_teachers = teacher_stats.get('by_gender', {}).get('male', 0)
            female_teachers = teacher_stats.get('by_gender', {}).get('female', 0)

            stats_data = [
                ("إجمالي الطلاب", total_students, "#3498db"),
                ("إجمالي المعلمين", total_teachers, "#27ae60"),
                ("الطلاب الذكور", male_students, "#2980b9"),
                ("الطالبات الإناث", female_students, "#8e44ad"),
                ("المعلمين الذكور", male_teachers, "#16a085"),
                ("المعلمات الإناث", female_teachers, "#d35400"),
            ]

            row = 0
            col = 0
            for title, value, color in stats_data:
                card = StatCard(title, value, color)
                self.stats_grid.addWidget(card, row, col)

                col += 1
                if col >= 3:  # 3 أعمدة
                    col = 0
                    row += 1

        except Exception as e:
            print(f"خطأ عام في تحميل الإحصائيات: {e}")
            import traceback
            traceback.print_exc()
            # إضافة بطاقة خطأ
            error_card = StatCard("خطأ في التحميل", "⚠️", "#e74c3c")
            self.stats_grid.addWidget(error_card, 0, 0)
            
    def clear_stats_grid(self):
        """مسح شبكة الإحصائيات"""
        while self.stats_grid.count():
            child = self.stats_grid.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

    def add_student(self):
        """إضافة طالب جديد"""
        try:
            # إرسال إشارة للنافذة الرئيسية للانتقال لشاشة الطلاب
            main_window = self.get_main_window()
            if main_window:
                main_window.switch_to_students()
                # محاولة فتح نافذة إضافة طالب
                if hasattr(main_window, 'students_widget'):
                    main_window.students_widget.add_student()
        except Exception as e:
            print(f"خطأ في إضافة طالب: {e}")

    def add_teacher(self):
        """إضافة معلم جديد"""
        try:
            # إرسال إشارة للنافذة الرئيسية للانتقال لشاشة المعلمين
            main_window = self.get_main_window()
            if main_window:
                main_window.switch_to_teachers()
                # محاولة فتح نافذة إضافة معلم
                if hasattr(main_window, 'teachers_widget'):
                    main_window.teachers_widget.add_teacher()
        except Exception as e:
            print(f"خطأ في إضافة معلم: {e}")

    def view_fees(self):
        """عرض الرسوم المستحقة"""
        try:
            # إرسال إشارة للنافذة الرئيسية للانتقال لشاشة الرسوم
            main_window = self.get_main_window()
            if main_window:
                main_window.switch_to_fees()
        except Exception as e:
            print(f"خطأ في عرض الرسوم: {e}")

    def generate_report(self):
        """إنشاء تقرير"""
        try:
            # إرسال إشارة للنافذة الرئيسية للانتقال لشاشة التقارير
            main_window = self.get_main_window()
            if main_window:
                main_window.switch_to_reports()
        except Exception as e:
            print(f"خطأ في إنشاء التقرير: {e}")

    def get_main_window(self):
        """الحصول على النافذة الرئيسية"""
        parent = self.parent()
        while parent:
            if hasattr(parent, 'switch_to_students'):  # التحقق من أنها النافذة الرئيسية
                return parent
            parent = parent.parent()
        return None
