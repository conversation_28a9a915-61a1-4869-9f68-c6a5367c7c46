#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QLineEdit, QPushButton, QLabel, QFrame, QMessageBox,
                             QCheckBox, QApplication)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QIcon

from src.models.user import User
from src.utils.font_manager import apply_font_to_widget
from src.utils.icon_manager import set_window_icon
from src.utils.config import Config


class LoginWindow(QDialog):
    """نافذة تسجيل الدخول"""
    
    # إشارة نجاح تسجيل الدخول
    login_successful = pyqtSignal(dict)
    
    def __init__(self):
        super().__init__()
        self.user_model = User()
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة
        self.setWindowTitle("تسجيل الدخول - برنامج إدارة المدارس")
        self.setFixedSize(400, 500)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

        # تعيين أيقونة النافذة
        set_window_icon(self)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(20)
        
        # إطار الشعار والعنوان
        header_frame = QFrame()
        header_layout = QVBoxLayout(header_frame)
        header_layout.setAlignment(Qt.AlignCenter)
        
        # شعار التطبيق
        logo_label = QLabel("🏫")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("""
            QLabel {
                font-size: 48px;
                color: #3498db;
                margin-bottom: 10px;
            }
        """)
        
        # عنوان التطبيق
        title_label = QLabel("برنامج إدارة المدارس")
        title_label.setAlignment(Qt.AlignCenter)
        apply_font_to_widget(title_label, "title", bold=True)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                margin-bottom: 5px;
            }
        """)
        
        # عنوان فرعي
        subtitle_label = QLabel("تسجيل الدخول")
        subtitle_label.setAlignment(Qt.AlignCenter)
        apply_font_to_widget(subtitle_label, "header")
        subtitle_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                margin-bottom: 20px;
            }
        """)
        
        header_layout.addWidget(logo_label)
        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)
        
        main_layout.addWidget(header_frame)
        
        # إطار النموذج
        form_frame = QFrame()
        form_frame.setFrameStyle(QFrame.StyledPanel)
        form_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        form_layout = QFormLayout(form_frame)
        form_layout.setSpacing(15)
        
        # حقل اسم المستخدم
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        self.username_input.setFixedHeight(40)
        apply_font_to_widget(self.username_input, "input")
        self.username_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                padding: 8px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        
        # حقل كلمة المرور
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setFixedHeight(40)
        apply_font_to_widget(self.password_input, "input")
        self.password_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                padding: 8px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        
        # خانة اختيار "تذكرني"
        self.remember_checkbox = QCheckBox("تذكرني")
        self.remember_checkbox.setStyleSheet("""
            QCheckBox {
                color: #2c3e50;
                font-size: 12px;
            }
        """)
        
        form_layout.addRow("اسم المستخدم:", self.username_input)
        form_layout.addRow("كلمة المرور:", self.password_input)
        form_layout.addRow("", self.remember_checkbox)
        
        main_layout.addWidget(form_frame)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.setFixedHeight(45)
        apply_font_to_widget(self.login_button, "button", bold=True)
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setFixedHeight(45)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
            QPushButton:pressed {
                background-color: #6c7b7d;
            }
        """)
        
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.login_button)
        
        main_layout.addLayout(buttons_layout)
        
        # معلومات المطور
        footer_label = QLabel("تم التطوير بواسطة Augment Agent")
        footer_label.setAlignment(Qt.AlignCenter)
        footer_label.setStyleSheet("""
            QLabel {
                color: #95a5a6;
                font-size: 10px;
                margin-top: 20px;
            }
        """)
        
        main_layout.addWidget(footer_label)
        
        # تطبيق الأنماط العامة
        self.setStyleSheet("""
            QDialog {
                background-color: #ecf0f1;
            }
        """)
        
        # تعيين التركيز على حقل اسم المستخدم
        self.username_input.setFocus()
        
    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        self.login_button.clicked.connect(self.login)
        self.cancel_button.clicked.connect(self.reject)
        
        # تسجيل الدخول عند الضغط على Enter
        self.username_input.returnPressed.connect(self.password_input.setFocus)
        self.password_input.returnPressed.connect(self.login)
        
    def login(self):
        """تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        # التحقق من إدخال البيانات
        if not username:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المستخدم")
            self.username_input.setFocus()
            return
            
        if not password:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال كلمة المرور")
            self.password_input.setFocus()
            return
        
        # تعطيل الأزرار أثناء المعالجة
        self.login_button.setEnabled(False)
        self.login_button.setText("جاري التحقق...")
        
        try:
            # محاولة المصادقة
            user = self.user_model.authenticate(username, password)
            
            if user:
                # نجح تسجيل الدخول
                QMessageBox.information(self, "نجح", f"مرحباً {user['full_name']}")
                # تحويل sqlite3.Row إلى dictionary
                user_dict = dict(user)
                self.login_successful.emit(user_dict)
                self.accept()
            else:
                # فشل تسجيل الدخول
                QMessageBox.critical(self, "خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
                self.password_input.clear()
                self.password_input.setFocus()
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تسجيل الدخول: {str(e)}")
            
        finally:
            # إعادة تفعيل الأزرار
            self.login_button.setEnabled(True)
            self.login_button.setText("تسجيل الدخول")
    
    def show_default_credentials(self):
        """عرض بيانات الدخول الافتراضية (للاختبار)"""
        self.username_input.setText("admin")
        self.password_input.setText("admin123")
