#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نموذج المعلمين والموظفين
يحتوي على جميع العمليات المتعلقة بإدارة المعلمين والموظفين
"""

from datetime import datetime, date
from src.models.base_model import BaseModel


class Teacher(BaseModel):
    """نموذج المعلمين والموظفين"""
    
    def __init__(self):
        super().__init__()
        self.table_name = "teachers"
        self.primary_key = "teacher_id"
        
        # الحقول المطلوبة
        self.required_fields = [
            'employee_number', 'first_name', 'last_name', 
            'phone', 'hire_date', 'position'
        ]
        
        # حقول البحث
        self.search_fields = [
            'employee_number', 'first_name', 'last_name', 
            'national_id', 'phone', 'email', 'specialization', 'position'
        ]

    def generate_employee_number(self):
        """توليد رقم موظف تلقائي"""
        try:
            # الحصول على آخر رقم موظف
            query = "SELECT employee_number FROM teachers ORDER BY teacher_id DESC LIMIT 1"
            last_teacher = self.db_manager.fetch_one(query)

            if last_teacher and last_teacher['employee_number']:
                # استخراج الرقم من آخر رقم موظف
                last_number = last_teacher['employee_number']
                if last_number.startswith('EMP'):
                    try:
                        number_part = int(last_number[3:])
                        new_number = number_part + 1
                    except ValueError:
                        new_number = 1
                else:
                    new_number = 1
            else:
                new_number = 1

            # تكوين رقم الموظف الجديد
            employee_number = f"EMP{new_number:06d}"  # مثال: EMP000001

            # التحقق من عدم وجود الرقم مسبقاً
            while self.get_teacher_by_employee_number(employee_number):
                new_number += 1
                employee_number = f"EMP{new_number:06d}"

            return employee_number

        except Exception as e:
            # في حالة الخطأ، استخدم timestamp
            import time
            timestamp = int(time.time())
            return f"EMP{timestamp}"

    def add_teacher(self, teacher_data):
        """إضافة معلم أو موظف جديد"""
        # توليد رقم الموظف تلقائياً إذا لم يتم تمريره
        if 'employee_number' not in teacher_data or not teacher_data['employee_number']:
            teacher_data['employee_number'] = self.generate_employee_number()

        # إزالة employee_number من الحقول المطلوبة مؤقتاً للتحقق
        required_fields_temp = [field for field in self.required_fields if field != 'employee_number']

        # التحقق من الحقول المطلوبة
        self.validate_required_fields(teacher_data, required_fields_temp)

        # التحقق من تفرد رقم الموظف
        if 'employee_number' in teacher_data:
            self.validate_unique_field('employee_number', teacher_data['employee_number'])

        # التحقق من تفرد رقم الهوية إذا تم إدخاله
        if 'national_id' in teacher_data and teacher_data['national_id']:
            self.validate_unique_field('national_id', teacher_data['national_id'])

        # التحقق من تفرد البريد الإلكتروني إذا تم إدخاله
        if 'email' in teacher_data and teacher_data['email']:
            self.validate_unique_field('email', teacher_data['email'])

        # التحقق من صحة تاريخ التوظيف
        if 'hire_date' in teacher_data:
            self.validate_hire_date(teacher_data['hire_date'])

        # إضافة الحالة الافتراضية
        if 'status' not in teacher_data:
            teacher_data['status'] = 'active'

        return self.insert(teacher_data)
    
    def update_teacher(self, teacher_id, teacher_data):
        """تحديث بيانات معلم أو موظف"""
        # التحقق من وجود المعلم
        existing_teacher = self.get_by_id(teacher_id)
        if not existing_teacher:
            raise ValueError("المعلم غير موجود")
        
        # التحقق من تفرد رقم الموظف
        if 'employee_number' in teacher_data:
            self.validate_unique_field('employee_number', teacher_data['employee_number'], teacher_id)
        
        # التحقق من تفرد رقم الهوية
        if 'national_id' in teacher_data and teacher_data['national_id']:
            self.validate_unique_field('national_id', teacher_data['national_id'], teacher_id)
        
        # التحقق من تفرد البريد الإلكتروني
        if 'email' in teacher_data and teacher_data['email']:
            self.validate_unique_field('email', teacher_data['email'], teacher_id)
        
        # التحقق من صحة تاريخ التوظيف
        if 'hire_date' in teacher_data:
            self.validate_hire_date(teacher_data['hire_date'])
        
        return self.update(teacher_id, teacher_data)
    
    def get_active_teachers(self):
        """جلب المعلمين النشطين فقط"""
        return self.get_all("status = 'active'")
    
    def get_teachers_by_specialization(self, specialization):
        """جلب المعلمين حسب التخصص"""
        return self.get_all("specialization = ? AND status = 'active'", (specialization,))
    
    def get_teachers_by_position(self, position):
        """جلب الموظفين حسب المنصب"""
        return self.get_all("position = ? AND status = 'active'", (position,))
    
    def search_teachers(self, search_term):
        """البحث في المعلمين"""
        return self.search(search_term, self.search_fields)
    
    def get_teacher_by_employee_number(self, employee_number):
        """جلب معلم برقم الموظف"""
        return self.db_manager.fetch_one(
            "SELECT * FROM teachers WHERE employee_number = ?",
            (employee_number,)
        )
    
    def get_teacher_by_national_id(self, national_id):
        """جلب معلم برقم الهوية"""
        return self.db_manager.fetch_one(
            "SELECT * FROM teachers WHERE national_id = ?",
            (national_id,)
        )
    
    def get_teachers_with_subjects(self):
        """جلب المعلمين مع المواد التي يدرسونها"""
        query = """
        SELECT DISTINCT t.*, 
               GROUP_CONCAT(s.subject_name) as subjects
        FROM teachers t
        LEFT JOIN class_subjects cs ON t.teacher_id = cs.teacher_id
        LEFT JOIN subjects s ON cs.subject_id = s.subject_id
        WHERE t.status = 'active'
        GROUP BY t.teacher_id
        ORDER BY t.first_name, t.last_name
        """
        return self.db_manager.fetch_all(query)
    
    def deactivate_teacher(self, teacher_id, reason=""):
        """إلغاء تفعيل معلم"""
        data = {
            'status': 'inactive',
            'notes': f"تم إلغاء التفعيل: {reason}" if reason else "تم إلغاء التفعيل"
        }
        return self.update_teacher(teacher_id, data)
    
    def reactivate_teacher(self, teacher_id):
        """إعادة تفعيل معلم"""
        return self.update_teacher(teacher_id, {'status': 'active'})
    
    def get_teacher_statistics(self):
        """إحصائيات المعلمين"""
        stats = {}
        
        # إجمالي المعلمين النشطين
        stats['total_active'] = self.count("status = 'active'")
        
        # إجمالي المعلمين غير النشطين
        stats['total_inactive'] = self.count("status = 'inactive'")
        
        # المعلمين حسب الجنس
        stats['male_teachers'] = self.count("gender = 'male' AND status = 'active'")
        stats['female_teachers'] = self.count("gender = 'female' AND status = 'active'")
        
        # المعلمين حسب المنصب
        positions_query = """
        SELECT position, COUNT(*) as count 
        FROM teachers 
        WHERE status = 'active' 
        GROUP BY position
        """
        positions = self.db_manager.fetch_all(positions_query)
        stats['by_position'] = {pos['position']: pos['count'] for pos in positions}
        
        # المعلمين حسب التخصص
        specializations_query = """
        SELECT specialization, COUNT(*) as count 
        FROM teachers 
        WHERE status = 'active' AND specialization IS NOT NULL
        GROUP BY specialization
        """
        specializations = self.db_manager.fetch_all(specializations_query)
        stats['by_specialization'] = {spec['specialization']: spec['count'] for spec in specializations}
        
        return stats
    
    def validate_hire_date(self, hire_date):
        """التحقق من صحة تاريخ التوظيف"""
        if isinstance(hire_date, str):
            try:
                hire_date_obj = datetime.strptime(hire_date, '%Y-%m-%d').date()
            except ValueError:
                raise ValueError("تنسيق تاريخ التوظيف غير صحيح. يجب أن يكون YYYY-MM-DD")
        else:
            hire_date_obj = hire_date
        
        # التحقق من أن التاريخ ليس في المستقبل
        if hire_date_obj > date.today():
            raise ValueError("تاريخ التوظيف لا يمكن أن يكون في المستقبل")
        
        return True

    def get_all_teachers(self):
        """الحصول على جميع المعلمين مع معلومات العملة"""
        try:
            # أولاً، تحقق من وجود عمود currency_id
            cursor = self.db_manager.connection.cursor()
            cursor.execute("PRAGMA table_info(teachers)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'currency_id' in columns:
                # إذا كان العمود موجود، استخدم الاستعلام مع العملة
                query = """
                SELECT t.*, c.symbol as currency_symbol, c.currency_name
                FROM teachers t
                LEFT JOIN currencies c ON t.currency_id = c.currency_id
                ORDER BY t.created_at DESC
                """
                return self.db_manager.fetch_all(query)
            else:
                # إذا لم يكن العمود موجود، استخدم الاستعلام البسيط
                return self.get_all()
        except Exception as e:
            # في حالة أي خطأ، استخدم الطريقة القديمة
            print(f"تحذير: {e}")
            return self.get_all()

    def get_teachers_statistics(self):
        """الحصول على إحصائيات المعلمين"""
        try:
            stats = {}

            # إجمالي المعلمين
            stats['total_teachers'] = self.count()

            # المعلمين النشطون
            stats['active_teachers'] = self.count("status = 'active'")

            # المعلمين غير النشطين
            stats['inactive_teachers'] = self.count("status = 'inactive'")

            # المعلمين حسب الجنس
            male_count = self.count("gender = 'ذكر' AND status = 'active'")
            female_count = self.count("gender = 'أنثى' AND status = 'active'")
            stats['by_gender'] = {
                'male': male_count,
                'female': female_count
            }

            # المعلمين حسب المنصب
            positions_query = """
            SELECT position, COUNT(*) as count
            FROM teachers
            WHERE status = 'active' AND position IS NOT NULL
            GROUP BY position
            """
            positions_result = self.db_manager.fetch_all(positions_query)
            stats['by_position'] = {row['position']: row['count'] for row in positions_result}

            # المعلمين حسب التخصص
            specializations_query = """
            SELECT specialization, COUNT(*) as count
            FROM teachers
            WHERE status = 'active' AND specialization IS NOT NULL
            GROUP BY specialization
            """
            specializations_result = self.db_manager.fetch_all(specializations_query)
            stats['by_specialization'] = {row['specialization']: row['count'] for row in specializations_result}

            # متوسط سنوات الخبرة
            experience_query = """
            SELECT AVG(
                (julianday('now') - julianday(hire_date)) / 365.25
            ) as avg_experience
            FROM teachers
            WHERE status = 'active' AND hire_date IS NOT NULL
            """
            experience_result = self.db_manager.fetch_one(experience_query)
            stats['average_experience'] = round(experience_result['avg_experience'], 1) if experience_result and experience_result['avg_experience'] else 0

            return stats
        except Exception as e:
            raise Exception(f"خطأ في جلب إحصائيات المعلمين: {str(e)}")

    def search_teachers(self, search_term):
        """البحث في المعلمين"""
        try:
            where_clause = """
            employee_number LIKE ? OR first_name LIKE ? OR last_name LIKE ?
            OR national_id LIKE ? OR phone LIKE ? OR email LIKE ?
            OR specialization LIKE ? OR position LIKE ?
            """
            search_param = f"%{search_term}%"
            params = (search_param, search_param, search_param, search_param,
                     search_param, search_param, search_param, search_param)
            return self.get_all(where_clause, params)
        except Exception as e:
            raise Exception(f"خطأ في البحث: {str(e)}")

    def get_teachers_by_specialization(self, specialization):
        """الحصول على المعلمين حسب التخصص"""
        try:
            return self.get_all("specialization = ? AND status = 'active'", (specialization,))
        except Exception as e:
            raise Exception(f"خطأ في جلب المعلمين حسب التخصص: {str(e)}")

    def get_teachers_by_position(self, position):
        """الحصول على المعلمين حسب المنصب"""
        try:
            return self.get_all("position = ? AND status = 'active'", (position,))
        except Exception as e:
            raise Exception(f"خطأ في جلب المعلمين حسب المنصب: {str(e)}")

    def activate_teacher(self, teacher_id):
        """تفعيل معلم"""
        try:
            return self.update(teacher_id, {'status': 'active'})
        except Exception as e:
            raise Exception(f"خطأ في تفعيل المعلم: {str(e)}")

    def deactivate_teacher(self, teacher_id):
        """إلغاء تفعيل معلم"""
        try:
            return self.update(teacher_id, {'status': 'inactive'})
        except Exception as e:
            raise Exception(f"خطأ في إلغاء تفعيل المعلم: {str(e)}")

    def get_teacher_by_number(self, employee_number):
        """الحصول على معلم برقم الموظف"""
        try:
            return self.get_all("employee_number = ?", (employee_number,))
        except Exception as e:
            raise Exception(f"خطأ في جلب المعلم: {str(e)}")

    def update_teacher_salary(self, teacher_id, salary_data):
        """تحديث راتب المعلم"""
        try:
            return self.update(teacher_id, salary_data)
        except Exception as e:
            raise Exception(f"خطأ في تحديث الراتب: {str(e)}")

    def get_active_teachers(self):
        """الحصول على المعلمين النشطين"""
        try:
            return self.get_all("status = 'active'")
        except Exception as e:
            raise Exception(f"خطأ في جلب المعلمين النشطين: {str(e)}")
    
    def get_teachers_by_hire_year(self, year):
        """جلب المعلمين حسب سنة التوظيف"""
        query = """
        SELECT * FROM teachers 
        WHERE strftime('%Y', hire_date) = ? 
        AND status = 'active'
        ORDER BY hire_date
        """
        return self.db_manager.fetch_all(query, (str(year),))
    
    def get_senior_teachers(self, years_of_service=5):
        """جلب المعلمين الأقدم (حسب سنوات الخدمة)"""
        cutoff_date = date.today().replace(year=date.today().year - years_of_service)
        query = """
        SELECT *, 
               (julianday('now') - julianday(hire_date)) / 365.25 as years_of_service
        FROM teachers 
        WHERE hire_date <= ? 
        AND status = 'active'
        ORDER BY hire_date
        """
        return self.db_manager.fetch_all(query, (cutoff_date,))
