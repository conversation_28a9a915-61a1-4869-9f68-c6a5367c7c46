#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نافذة حوار الصفوف
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from src.ui.dialogs.class_dialog import ClassDialog
from src.models.class_model import ClassModel


def test_class_dialog():
    """اختبار نافذة حوار الصفوف"""
    print("=== اختبار نافذة حوار الصفوف ===")
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # اختبار إنشاء النافذة
        dialog = ClassDialog()
        print("✓ تم إنشاء نافذة إضافة الصف بنجاح")
        
        # اختبار نموذج الصفوف
        class_model = ClassModel()
        classes = class_model.get_all_classes()
        print(f"✓ تم جلب {len(classes)} صف من قاعدة البيانات")
        
        # اختبار نافذة التعديل إذا كان هناك صفوف
        if classes:
            edit_dialog = ClassDialog(class_data=classes[0])
            print("✓ تم إنشاء نافذة تعديل الصف بنجاح")
        
        print("✓ جميع اختبارات نافذة الصفوف نجحت")
        return True
        
    except Exception as e:
        print(f"✗ خطأ في اختبار نافذة الصفوف: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_class_dialog()
    if success:
        print("\n🎉 نافذة حوار الصفوف جاهزة للاستخدام!")
    else:
        print("\n❌ فشل في اختبار نافذة الصفوف")
    
    sys.exit(0 if success else 1)
