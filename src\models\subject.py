#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نموذج المواد الدراسية
يحتوي على جميع العمليات المتعلقة بإدارة المواد الدراسية
"""

from src.models.base_model import BaseModel


class Subject(BaseModel):
    """نموذج المواد الدراسية"""
    
    def __init__(self):
        super().__init__()
        self.table_name = "subjects"
        self.primary_key = "subject_id"
        
        # الحقول المطلوبة
        self.required_fields = [
            'subject_code', 'subject_name'
        ]
        
        # حقول البحث
        self.search_fields = [
            'subject_code', 'subject_name', 'description'
        ]
    
    def add_subject(self, subject_data):
        """إضافة مادة دراسية جديدة"""
        # التحقق من الحقول المطلوبة
        self.validate_required_fields(subject_data, self.required_fields)
        
        # التحقق من تفرد رمز المادة
        if 'subject_code' in subject_data:
            self.validate_unique_field('subject_code', subject_data['subject_code'])
        
        # إضافة الحالة الافتراضية
        if 'is_active' not in subject_data:
            subject_data['is_active'] = True
            
        # إضافة الساعات الافتراضية
        if 'credit_hours' not in subject_data:
            subject_data['credit_hours'] = 1
        
        return self.insert(subject_data)
    
    def update_subject(self, subject_id, subject_data):
        """تحديث بيانات مادة دراسية"""
        # التحقق من وجود المادة
        existing_subject = self.get_by_id(subject_id)
        if not existing_subject:
            raise ValueError("المادة الدراسية غير موجودة")
        
        # التحقق من تفرد رمز المادة
        if 'subject_code' in subject_data:
            self.validate_unique_field('subject_code', subject_data['subject_code'], subject_id)
        
        return self.update(subject_id, subject_data)
    
    def get_active_subjects(self):
        """جلب المواد النشطة فقط"""
        return self.get_all("is_active = 1")
    
    def search_subjects(self, search_term):
        """البحث في المواد الدراسية"""
        return self.search(search_term, self.search_fields)
    
    def get_subject_by_code(self, subject_code):
        """جلب مادة برمز المادة"""
        return self.db_manager.fetch_one(
            "SELECT * FROM subjects WHERE subject_code = ?",
            (subject_code,)
        )
    
    def deactivate_subject(self, subject_id):
        """إلغاء تفعيل مادة"""
        return self.update_subject(subject_id, {'is_active': False})
    
    def activate_subject(self, subject_id):
        """تفعيل مادة"""
        return self.update_subject(subject_id, {'is_active': True})
    
    def get_subject_statistics(self):
        """إحصائيات المواد الدراسية"""
        stats = {}
        
        # إجمالي المواد النشطة
        stats['total_active'] = self.count("is_active = 1")
        
        # إجمالي المواد غير النشطة
        stats['total_inactive'] = self.count("is_active = 0")
        
        return stats

    def get_all_subjects(self):
        """الحصول على جميع المواد الدراسية"""
        return self.get_all()

    def get_subjects_statistics(self):
        """الحصول على إحصائيات المواد الدراسية"""
        try:
            stats = {}

            # المواد النشطة
            stats['active_subjects'] = self.count("is_active = 1")

            # المواد غير النشطة
            stats['inactive_subjects'] = self.count("is_active = 0")

            # إجمالي المواد
            stats['total_subjects'] = self.count()

            # المواد حسب الساعات المعتمدة
            hours_query = """
            SELECT credit_hours, COUNT(*) as count
            FROM subjects
            WHERE is_active = 1
            GROUP BY credit_hours
            """
            hours_result = self.db_manager.fetch_all(hours_query)
            stats['by_credit_hours'] = {row['credit_hours']: row['count'] for row in hours_result}

            # متوسط الساعات المعتمدة
            avg_hours_query = """
            SELECT AVG(credit_hours) as avg_hours
            FROM subjects
            WHERE is_active = 1
            """
            avg_result = self.db_manager.fetch_one(avg_hours_query)
            stats['average_credit_hours'] = round(avg_result['avg_hours'], 1) if avg_result and avg_result['avg_hours'] else 0

            return stats
        except Exception as e:
            raise Exception(f"خطأ في جلب إحصائيات المواد: {str(e)}")

    def get_subjects_by_credit_hours(self, credit_hours):
        """الحصول على المواد حسب الساعات المعتمدة"""
        try:
            return self.get_all("credit_hours = ? AND is_active = 1", (credit_hours,))
        except Exception as e:
            raise Exception(f"خطأ في جلب المواد حسب الساعات: {str(e)}")

    def validate_subject_data(self, subject_data):
        """التحقق من صحة بيانات المادة"""
        try:
            # التحقق من الحقول المطلوبة
            self.validate_required_fields(subject_data)

            # التحقق من رمز المادة
            subject_code = subject_data.get('subject_code', '')
            if not subject_code or len(subject_code) < 2:
                raise ValueError("رمز المادة يجب أن يكون على الأقل حرفين")

            # التحقق من اسم المادة
            subject_name = subject_data.get('subject_name', '')
            if not subject_name or len(subject_name) < 3:
                raise ValueError("اسم المادة يجب أن يكون على الأقل 3 أحرف")

            # التحقق من الساعات المعتمدة
            credit_hours = subject_data.get('credit_hours', 1)
            if credit_hours < 1 or credit_hours > 6:
                raise ValueError("الساعات المعتمدة يجب أن تكون بين 1 و 6")

            return True
        except ValueError:
            raise
        except Exception as e:
            raise Exception(f"خطأ في التحقق من البيانات: {str(e)}")

    def subject_code_exists(self, subject_code, exclude_subject_id=None):
        """التحقق من وجود رمز المادة"""
        try:
            if exclude_subject_id:
                where_clause = "subject_code = ? AND subject_id != ?"
                params = (subject_code, exclude_subject_id)
            else:
                where_clause = "subject_code = ?"
                params = (subject_code,)

            return self.exists(where_clause, params)
        except Exception:
            return False
