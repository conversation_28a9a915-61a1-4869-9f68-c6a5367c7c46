#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ويدجت إدارة الصفوف والفصول الدراسية
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QH<PERSON><PERSON>Layout, QTableWidget,
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                             QMessageBox, QHeaderView, QFrame, QComboBox,
                             QGroupBox, QSplitter)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

from src.models.class_model import ClassModel
from src.models.student import Student
from src.models.teacher import Teacher
from src.ui.dialogs.class_dialog import ClassDialog


class ClassesWidget(QWidget):
    """ويدجت إدارة الصفوف والفصول الدراسية"""

    def __init__(self):
        super().__init__()
        self.class_model = ClassModel()
        self.student_model = Student()
        self.teacher_model = Teacher()
        self.setup_ui()
        self.load_classes()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # عنوان الصفحة
        title_label = QLabel("إدارة الصفوف والفصول الدراسية")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # شريط البحث والأزرار
        top_frame = QFrame()
        top_layout = QHBoxLayout(top_frame)

        # حقل البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("البحث عن صف...")
        self.search_input.setFixedHeight(35)
        self.search_input.textChanged.connect(self.search_classes)

        # أزرار الإجراءات
        self.add_class_button = QPushButton("إضافة صف")
        self.edit_class_button = QPushButton("تعديل")
        self.delete_class_button = QPushButton("حذف")
        self.refresh_button = QPushButton("تحديث")

        # تطبيق الأنماط على الأزرار
        button_style = """
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """

        for button in [self.add_class_button, self.edit_class_button, self.refresh_button]:
            button.setStyleSheet(button_style)
            button.setFixedHeight(35)

        # تخصيص لون زر الحذف
        self.delete_class_button.setStyleSheet(
            button_style.replace("#3498db", "#e74c3c").replace("#2980b9", "#c0392b")
        )
        self.delete_class_button.setFixedHeight(35)

        top_layout.addWidget(QLabel("البحث:"))
        top_layout.addWidget(self.search_input)
        top_layout.addStretch()
        top_layout.addWidget(self.add_class_button)
        top_layout.addWidget(self.edit_class_button)
        top_layout.addWidget(self.delete_class_button)
        top_layout.addWidget(self.refresh_button)

        layout.addWidget(top_frame)

        # جدول الصفوف
        self.classes_table = QTableWidget()
        self.setup_classes_table()
        layout.addWidget(self.classes_table)

        # ربط الأحداث
        self.setup_connections()

    def setup_classes_table(self):
        """إعداد جدول الصفوف"""
        columns = [
            "اسم الصف", "المرحلة الدراسية", "السنة الدراسية",
            "عدد الطلاب", "السعة القصوى", "الحالة"
        ]

        self.classes_table.setColumnCount(len(columns))
        self.classes_table.setHorizontalHeaderLabels(columns)

        # إعداد خصائص الجدول
        self.classes_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.classes_table.setAlternatingRowColors(True)
        self.classes_table.setSortingEnabled(True)

        # تخصيص عرض الأعمدة
        header = self.classes_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)

        # تطبيق الأنماط
        self.classes_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

    def setup_connections(self):
        """ربط الأحداث"""
        self.add_class_button.clicked.connect(self.add_class)
        self.edit_class_button.clicked.connect(self.edit_class)
        self.delete_class_button.clicked.connect(self.delete_class)
        self.refresh_button.clicked.connect(self.load_classes)

    def load_classes(self):
        """تحميل قائمة الصفوف"""
        try:
            classes = self.class_model.get_all_classes()
            self.populate_classes_table(classes)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل الصفوف: {str(e)}")

    def populate_classes_table(self, classes):
        """ملء جدول الصفوف بالبيانات"""
        self.classes_table.setRowCount(len(classes))

        for row, class_data in enumerate(classes):
            try:
                # تحويل sqlite3.Row إلى dict إذا لزم الأمر
                if hasattr(class_data, 'keys'):
                    class_dict = dict(class_data)
                else:
                    class_dict = class_data

                # اسم الصف
                class_name = class_dict.get('class_name', '')
                self.classes_table.setItem(row, 0, QTableWidgetItem(str(class_name)))

                # المرحلة الدراسية
                grade_level = class_dict.get('grade_level', '')
                self.classes_table.setItem(row, 1, QTableWidgetItem(str(grade_level)))

                # السنة الدراسية
                academic_year = class_dict.get('academic_year', '')
                self.classes_table.setItem(row, 2, QTableWidgetItem(str(academic_year)))

                # عدد الطلاب
                class_id = class_dict.get('class_id', 0)
                students_count = self.get_class_students_count(class_id)
                self.classes_table.setItem(row, 3, QTableWidgetItem(str(students_count)))

                # السعة القصوى
                capacity = class_dict.get('capacity', 30)
                self.classes_table.setItem(row, 4, QTableWidgetItem(str(capacity)))

                # الحالة
                is_active = class_dict.get('is_active', True)
                status = "نشط" if is_active else "غير نشط"
                status_item = QTableWidgetItem(status)
                if is_active:
                    status_item.setBackground(Qt.green)
                else:
                    status_item.setBackground(Qt.red)
                self.classes_table.setItem(row, 5, status_item)

                # حفظ معرف الصف في البيانات المخفية
                self.classes_table.item(row, 0).setData(Qt.UserRole, class_id)

            except Exception as e:
                print(f"خطأ في إضافة الصف رقم {row}: {e}")

    def get_class_students_count(self, class_id):
        """الحصول على عدد طلاب الصف"""
        try:
            return self.student_model.count("class_id = ? AND status = 'active'", (class_id,))
        except Exception:
            return 0

    def search_classes(self):
        """البحث في الصفوف"""
        search_text = self.search_input.text().strip()

        if not search_text:
            self.load_classes()
            return

        try:
            classes = self.class_model.search_classes(search_text)
            self.populate_classes_table(classes)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في البحث: {str(e)}")

    def add_class(self):
        """إضافة صف جديد"""
        dialog = ClassDialog(parent=self)
        dialog.class_saved.connect(self.load_classes)
        dialog.exec_()

    def edit_class(self):
        """تعديل صف"""
        current_row = self.classes_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار صف للتعديل")
            return

        try:
            class_id = self.classes_table.item(current_row, 0).data(Qt.UserRole)
            class_data = self.class_model.get_by_id(class_id)

            if class_data:
                dialog = ClassDialog(class_data=class_data, parent=self)
                dialog.class_saved.connect(self.load_classes)
                dialog.exec_()
            else:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على بيانات الصف")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل بيانات الصف: {str(e)}")

    def delete_class(self):
        """حذف صف"""
        current_row = self.classes_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار صف للحذف")
            return

        class_id = self.classes_table.item(current_row, 0).data(Qt.UserRole)
        class_name = self.classes_table.item(current_row, 0).text()

        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل أنت متأكد من رغبتك في حذف الصف '{class_name}'؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                self.class_model.delete(class_id)
                QMessageBox.information(self, "نجح", "تم حذف الصف بنجاح")
                self.load_classes()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ في حذف الصف: {str(e)}")
