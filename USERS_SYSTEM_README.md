# نظام إدارة المستخدمين والصلاحيات

## نظرة عامة

تم إنشاء نظام شامل لإدارة المستخدمين والصلاحيات في نظام إدارة المدارس. يوفر هذا النظام إدارة كاملة للمستخدمين مع نظام صلاحيات متقدم وواجهة مستخدم حديثة.

## المميزات الرئيسية

### 🔐 إدارة المستخدمين
- إضافة وتعديل وحذف المستخدمين
- نظام مصادقة آمن مع تشفير كلمات المرور
- إدارة حالة المستخدمين (نشط/غير نشط)
- البحث والفلترة المتقدمة
- إحصائيات شاملة للمستخدمين

### 👥 الأدوار والصلاحيات
- **6 أدوار مختلفة:**
  - مدير عام (Admin)
  - مدير أكاديمي (Academic Manager)
  - مدير مالي (Financial Manager)
  - معلم (Teacher)
  - سكرتير (Secretary)
  - مستخدم عادي (User)

### 🛡️ نظام الصلاحيات
- **70+ صلاحية مختلفة** مقسمة إلى فئات:
  - صلاحيات الطلاب
  - صلاحيات المعلمين
  - صلاحيات المواد الدراسية
  - صلاحيات الرسوم والمالية
  - صلاحيات النتائج والدرجات
  - صلاحيات التقارير
  - صلاحيات إدارة المستخدمين
  - صلاحيات النظام
  - صلاحيات متقدمة

### 🎨 واجهة المستخدم
- واجهة حديثة ومتجاوبة
- دعم كامل للغة العربية مع RTL
- نوافذ حوار متقدمة لإدارة المستخدمين
- نظام تبويب منظم
- إحصائيات مرئية

## الملفات المنشأة

### النماذج (Models)
- `src/models/user.py` - نموذج المستخدم الشامل

### واجهة المستخدم
- `src/ui/widgets/users_widget.py` - ويدجت إدارة المستخدمين
- `src/ui/dialogs/user_dialog.py` - نافذة إضافة/تعديل المستخدم
- `src/ui/dialogs/change_password_dialog.py` - نافذة تغيير كلمة المرور
- `src/ui/dialogs/permissions_dialog.py` - نافذة إدارة الصلاحيات

### التكوين
- `src/utils/config.py` - إعدادات الأدوار والصلاحيات

### الاختبار
- `test_users_system.py` - اختبار شامل للنظام

## كيفية الاستخدام

### 1. تشغيل النظام
```bash
python test_users_system.py
```

### 2. تسجيل الدخول
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

### 3. إدارة المستخدمين
1. انتقل إلى تبويب "المستخدمين"
2. استخدم الأزرار لإضافة أو تعديل المستخدمين
3. قم بتعيين الأدوار والصلاحيات المناسبة

## الأدوار والصلاحيات الافتراضية

### مدير عام (Admin)
- جميع الصلاحيات
- إدارة كاملة للنظام
- إدارة المستخدمين والصلاحيات

### مدير أكاديمي (Academic Manager)
- إدارة الطلاب والمعلمين
- إدارة المواد والجداول
- إدارة النتائج والدرجات
- إنشاء التقارير الأكاديمية

### مدير مالي (Financial Manager)
- إدارة الرسوم والمدفوعات
- إدارة رواتب المعلمين
- التقارير المالية
- عرض بيانات الطلاب

### معلم (Teacher)
- عرض الطلاب والمواد
- إدخال وتعديل النتائج
- إدارة الحضور والغياب
- إنشاء كشوف الدرجات

### سكرتير (Secretary)
- إدارة بيانات الطلاب
- إدارة المدفوعات
- عرض التقارير الأساسية
- عرض بيانات المعلمين

### مستخدم عادي (User)
- عرض البيانات الأساسية فقط
- عرض التقارير العامة

## المميزات الأمنية

### تشفير كلمات المرور
- استخدام مكتبة `bcrypt` للتشفير
- Salt عشوائي لكل كلمة مرور
- حماية ضد هجمات Rainbow Table

### إدارة الجلسات
- تتبع آخر تسجيل دخول
- إمكانية إلغاء تفعيل المستخدمين
- حماية من الوصول غير المصرح

### التحقق من الصلاحيات
- فحص الصلاحيات قبل كل عملية
- منع الوصول غير المصرح
- تسجيل العمليات الحساسة

## قوالب الصلاحيات

يوفر النظام قوالب جاهزة للصلاحيات:

### قالب المدير العام
- جميع الصلاحيات المتاحة
- تحكم كامل في النظام

### قالب المدير الأكاديمي
- صلاحيات إدارة الطلاب والمعلمين
- صلاحيات إدارة المواد والنتائج
- صلاحيات التقارير الأكاديمية

### قالب المدير المالي
- صلاحيات إدارة الرسوم
- صلاحيات إدارة الرواتب
- صلاحيات التقارير المالية

### قالب المعلم
- صلاحيات عرض الطلاب
- صلاحيات إدارة النتائج
- صلاحيات الحضور والغياب

### قالب المستخدم العادي
- صلاحيات العرض الأساسية فقط

## إحصائيات النظام

يوفر النظام إحصائيات شاملة:
- عدد المستخدمين النشطين
- عدد المستخدمين غير النشطين
- توزيع المستخدمين حسب الأدوار
- آخر تسجيلات الدخول

## التطوير المستقبلي

### مميزات مخططة
- نظام إشعارات للمستخدمين
- سجل العمليات (Audit Log)
- نظام الجلسات المتقدم
- تكامل مع Active Directory
- نظام النسخ الاحتياطي للمستخدمين

### تحسينات الأمان
- مصادقة ثنائية العامل (2FA)
- سياسات كلمات المرور المتقدمة
- انتهاء صلاحية كلمات المرور
- قفل الحساب بعد محاولات فاشلة

## الدعم الفني

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. تحقق من ملف الاختبار `test_users_system.py`
2. راجع رسائل الخطأ في وحدة التحكم
3. تأكد من صحة إعدادات قاعدة البيانات

## الخلاصة

تم إنشاء نظام إدارة مستخدمين شامل ومتقدم يوفر:
- ✅ إدارة كاملة للمستخدمين
- ✅ نظام صلاحيات متطور
- ✅ واجهة مستخدم حديثة
- ✅ أمان عالي المستوى
- ✅ سهولة في الاستخدام
- ✅ قابلية التوسع

النظام جاهز للاستخدام الفوري ويمكن تخصيصه حسب احتياجات المؤسسة التعليمية.
