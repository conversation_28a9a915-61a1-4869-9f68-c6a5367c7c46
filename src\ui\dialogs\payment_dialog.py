#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة تسجيل المدفوعات
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QLineEdit, QPushButton, QLabel, QFrame, QMessageBox,
                             QComboBox, QDateEdit, QTextEdit, QTableWidget,
                             QTableWidgetItem, QHeaderView)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont

from src.models.student import Student
from src.models.currency import CurrencyModel


class PaymentDialog(QDialog):
    """نافذة تسجيل المدفوعات"""
    
    # إشارة تسجيل المدفوعة
    payment_recorded = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.student_model = Student()
        self.currency_model = CurrencyModel(self.student_model.db_manager)
        self.default_currency = None
        self.load_default_currency()
        self.setup_ui()
        self.setup_connections()
        self.load_pending_fees()

    def load_default_currency(self):
        """تحميل العملة الافتراضية"""
        try:
            self.default_currency = self.currency_model.get_base_currency()
            if not self.default_currency:
                self.default_currency = {
                    'symbol': 'ر.س',
                    'currency_name': 'الريال السعودي'
                }
        except Exception as e:
            print(f"خطأ في تحميل العملة الافتراضية: {e}")
            self.default_currency = {
                'symbol': 'ر.س',
                'currency_name': 'الريال السعودي'
            }

    def get_currency_symbol(self):
        """الحصول على رمز العملة الافتراضية"""
        if self.default_currency:
            return self.default_currency.get('symbol', 'ر.س')
        return 'ر.س'
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة
        self.setWindowTitle("تسجيل دفعة جديدة")
        self.setFixedSize(700, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # عنوان النافذة
        title_label = QLabel("تسجيل دفعة جديدة")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # جدول الرسوم المعلقة
        fees_label = QLabel("الرسوم المعلقة:")
        fees_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #34495e;
                margin-bottom: 5px;
            }
        """)
        main_layout.addWidget(fees_label)
        
        self.pending_fees_table = QTableWidget()
        self.setup_pending_fees_table()
        main_layout.addWidget(self.pending_fees_table)
        
        # نموذج تسجيل الدفعة
        payment_frame = QFrame()
        payment_frame.setFrameStyle(QFrame.StyledPanel)
        payment_layout = QFormLayout(payment_frame)
        payment_layout.setSpacing(15)
        
        # تاريخ الدفع
        self.payment_date_input = QDateEdit()
        self.payment_date_input.setDate(QDate.currentDate())
        self.payment_date_input.setCalendarPopup(True)
        payment_layout.addRow("تاريخ الدفع:", self.payment_date_input)
        
        # طريقة الدفع
        self.payment_method_combo = QComboBox()
        payment_methods = ["نقدي", "شيك", "تحويل بنكي", "بطاقة ائتمان", "أخرى"]
        self.payment_method_combo.addItems(payment_methods)
        payment_layout.addRow("طريقة الدفع:", self.payment_method_combo)
        
        # رقم المرجع (للشيكات والتحويلات)
        self.reference_input = QLineEdit()
        self.reference_input.setPlaceholderText("رقم الشيك أو التحويل (اختياري)")
        payment_layout.addRow("رقم المرجع:", self.reference_input)
        
        # ملاحظات الدفعة
        self.payment_notes_input = QTextEdit()
        self.payment_notes_input.setPlaceholderText("ملاحظات حول الدفعة")
        self.payment_notes_input.setMaximumHeight(60)
        payment_layout.addRow("ملاحظات:", self.payment_notes_input)
        
        main_layout.addWidget(payment_frame)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        self.record_payment_button = QPushButton("تسجيل الدفعة")
        self.record_payment_button.setFixedHeight(40)
        self.record_payment_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setFixedHeight(40)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.record_payment_button)
        
        main_layout.addLayout(buttons_layout)
        
    def setup_pending_fees_table(self):
        """إعداد جدول الرسوم المعلقة"""
        columns = ["اختيار", "اسم الطالب", "نوع الرسوم", "المبلغ", "تاريخ الاستحقاق"]
        
        self.pending_fees_table.setColumnCount(len(columns))
        self.pending_fees_table.setHorizontalHeaderLabels(columns)
        
        # إعداد خصائص الجدول
        self.pending_fees_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.pending_fees_table.setAlternatingRowColors(True)
        
        # تخصيص عرض الأعمدة
        header = self.pending_fees_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        # تطبيق الأنماط
        self.pending_fees_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
            }
            QTableWidget::item:selected {
                background-color: #27ae60;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)
        
    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        self.record_payment_button.clicked.connect(self.record_payment)
        self.cancel_button.clicked.connect(self.reject)
        
    def load_pending_fees(self):
        """تحميل الرسوم المعلقة"""
        try:
            query = """
            SELECT f.*, s.first_name, s.last_name, s.student_number
            FROM fees f
            JOIN students s ON f.student_id = s.student_id
            WHERE f.status = 'pending' AND s.status = 'active'
            ORDER BY f.due_date, s.first_name
            """
            pending_fees = self.student_model.db_manager.fetch_all(query)
            self.populate_pending_fees_table(pending_fees)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل الرسوم المعلقة: {str(e)}")
            
    def populate_pending_fees_table(self, fees):
        """ملء جدول الرسوم المعلقة بالبيانات"""
        self.pending_fees_table.setRowCount(len(fees))
        
        for row, fee in enumerate(fees):
            try:
                # خانة الاختيار
                checkbox_item = QTableWidgetItem()
                checkbox_item.setFlags(Qt.ItemIsUserCheckable | Qt.ItemIsEnabled)
                checkbox_item.setCheckState(Qt.Unchecked)
                self.pending_fees_table.setItem(row, 0, checkbox_item)
                
                # اسم الطالب
                student_name = f"{fee['first_name']} {fee['last_name']} ({fee['student_number']})"
                self.pending_fees_table.setItem(row, 1, QTableWidgetItem(student_name))
                
                # نوع الرسوم
                self.pending_fees_table.setItem(row, 2, QTableWidgetItem(str(fee['fee_type'])))
                
                # المبلغ
                amount = fee['amount'] - (fee['discount'] or 0)
                currency_symbol = self.get_currency_symbol()
                self.pending_fees_table.setItem(row, 3, QTableWidgetItem(f"{amount:,.2f} {currency_symbol}"))
                
                # تاريخ الاستحقاق
                self.pending_fees_table.setItem(row, 4, QTableWidgetItem(str(fee['due_date'])))
                
                # حفظ معرف الرسوم في البيانات المخفية
                checkbox_item.setData(Qt.UserRole, fee['fee_id'])
                
            except Exception as e:
                print(f"خطأ في إضافة الرسوم المعلقة رقم {row}: {e}")
                
    def get_selected_fees(self):
        """الحصول على الرسوم المحددة للدفع"""
        selected_fees = []
        
        for row in range(self.pending_fees_table.rowCount()):
            checkbox_item = self.pending_fees_table.item(row, 0)
            if checkbox_item.checkState() == Qt.Checked:
                fee_id = checkbox_item.data(Qt.UserRole)
                selected_fees.append(fee_id)
                
        return selected_fees
        
    def record_payment(self):
        """تسجيل الدفعة"""
        try:
            # الحصول على الرسوم المحددة
            selected_fees = self.get_selected_fees()
            
            if not selected_fees:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار رسوم واحدة على الأقل للدفع")
                return
                
            # جمع بيانات الدفعة
            payment_date = self.payment_date_input.date().toString("yyyy-MM-dd")
            payment_method = self.payment_method_combo.currentText()
            reference = self.reference_input.text().strip() or None
            notes = self.payment_notes_input.toPlainText().strip() or None
            
            # تحديث حالة الرسوم إلى مدفوع
            for fee_id in selected_fees:
                update_query = """
                UPDATE fees SET 
                status = 'paid',
                payment_date = ?,
                payment_method = ?,
                notes = ?
                WHERE fee_id = ?
                """
                
                # دمج الملاحظات الجديدة مع الموجودة
                existing_notes_query = "SELECT notes FROM fees WHERE fee_id = ?"
                existing_fee = self.student_model.db_manager.fetch_one(existing_notes_query, (fee_id,))
                existing_notes = existing_fee['notes'] if existing_fee and existing_fee['notes'] else ""
                
                combined_notes = f"{existing_notes}\nدفع في {payment_date} - {payment_method}"
                if reference:
                    combined_notes += f" - مرجع: {reference}"
                if notes:
                    combined_notes += f" - {notes}"
                    
                params = (payment_date, payment_method, combined_notes.strip(), fee_id)
                self.student_model.db_manager.execute_query(update_query, params)
            
            # رسالة نجاح
            fees_count = len(selected_fees)
            QMessageBox.information(
                self, 
                "نجح", 
                f"تم تسجيل دفعة {fees_count} رسوم بنجاح!\n"
                f"تاريخ الدفع: {payment_date}\n"
                f"طريقة الدفع: {payment_method}"
            )
            
            # إرسال إشارة التسجيل
            self.payment_recorded.emit()
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تسجيل الدفعة: {str(e)}")
