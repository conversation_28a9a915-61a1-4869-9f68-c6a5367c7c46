#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة حوار إضافة وتعديل العملات
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QLineEdit, QDoubleSpinBox, QPushButton, QLabel,
                             QMessageBox, QGroupBox, QCheckBox, QFrame)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

from src.models.currency import CurrencyModel
from src.utils.dialog_utils import apply_dialog_styles, center_dialog_on_parent, ensure_dialog_visibility


class CurrencyDialog(QDialog):
    """نافذة حوار إضافة وتعديل العملات"""
    
    currency_saved = pyqtSignal()  # إشارة عند حفظ العملة
    
    def __init__(self, currency_data=None, parent=None):
        super().__init__(parent)
        self.currency_data = currency_data
        # سنحتاج لتمرير db_manager من الوالد
        from src.database.db_manager import DatabaseManager
        db_manager = DatabaseManager()
        self.currency_model = CurrencyModel(db_manager)
        self.is_edit_mode = currency_data is not None
        
        self.setup_ui()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.load_currency_data()
            
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("تعديل العملة" if self.is_edit_mode else "إضافة عملة جديدة")
        self.setModal(True)
        self.setFixedSize(450, 500)

        # إعدادات النافذة لضمان الظهور في المقدمة
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint | Qt.WindowStaysOnTopHint)
        self.setAttribute(Qt.WA_DeleteOnClose)
        self.setLayoutDirection(Qt.RightToLeft)

        # توسيط النافذة
        center_dialog_on_parent(self, self.parent())
        
        # تطبيق الأنماط الموحدة
        apply_dialog_styles(self)

        # أنماط إضافية خاصة بهذه النافذة
        self.setStyleSheet(self.styleSheet() + """
            QDialog {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Tahoma, Arial;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #495057;
            }
            QLineEdit, QDoubleSpinBox {
                border: 2px solid #dee2e6;
                border-radius: 5px;
                padding: 8px;
                font-size: 11px;
                background-color: white;
            }
            QLineEdit:focus, QDoubleSpinBox:focus {
                border-color: #007bff;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        title_label = QLabel("تعديل بيانات العملة" if self.is_edit_mode else "إضافة عملة جديدة")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #212529;
                padding: 15px;
                background-color: #e9ecef;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # مجموعة البيانات الأساسية
        basic_group = QGroupBox("البيانات الأساسية")
        basic_layout = QFormLayout(basic_group)
        basic_layout.setSpacing(10)
        
        # رمز العملة
        self.currency_code_input = QLineEdit()
        self.currency_code_input.setPlaceholderText("مثال: USD, EUR, SAR")
        self.currency_code_input.setMaxLength(3)
        basic_layout.addRow("رمز العملة *:", self.currency_code_input)
        
        # اسم العملة
        self.currency_name_input = QLineEdit()
        self.currency_name_input.setPlaceholderText("مثال: الدولار الأمريكي")
        basic_layout.addRow("اسم العملة *:", self.currency_name_input)
        
        # رمز العملة (الرمز المرئي)
        self.symbol_input = QLineEdit()
        self.symbol_input.setPlaceholderText("مثال: $, €, ر.س")
        self.symbol_input.setMaxLength(5)
        basic_layout.addRow("الرمز *:", self.symbol_input)
        
        layout.addWidget(basic_group)
        
        # مجموعة إعدادات الصرف
        exchange_group = QGroupBox("إعدادات الصرف")
        exchange_layout = QFormLayout(exchange_group)
        exchange_layout.setSpacing(10)
        
        # سعر الصرف
        self.exchange_rate_spin = QDoubleSpinBox()
        self.exchange_rate_spin.setRange(0.0001, 999999.9999)
        self.exchange_rate_spin.setDecimals(4)
        self.exchange_rate_spin.setValue(1.0000)
        self.exchange_rate_spin.setSuffix(" (مقابل العملة الأساسية)")
        exchange_layout.addRow("سعر الصرف:", self.exchange_rate_spin)
        
        layout.addWidget(exchange_group)
        
        # مجموعة الإعدادات
        settings_group = QGroupBox("الإعدادات")
        settings_layout = QFormLayout(settings_group)
        settings_layout.setSpacing(10)
        
        # العملة الأساسية
        self.is_base_currency_checkbox = QCheckBox("هذه هي العملة الأساسية")
        self.is_base_currency_checkbox.setToolTip("العملة الأساسية هي المرجع لحساب أسعار الصرف")
        settings_layout.addRow("", self.is_base_currency_checkbox)
        
        # حالة العملة
        self.is_active_checkbox = QCheckBox("العملة نشطة")
        self.is_active_checkbox.setChecked(True)
        settings_layout.addRow("الحالة:", self.is_active_checkbox)
        
        layout.addWidget(settings_group)
        
        # ملاحظة
        note_label = QLabel("* الحقول المطلوبة")
        note_label.setStyleSheet("color: #6c757d; font-style: italic;")
        layout.addWidget(note_label)
        
        # أزرار الحفظ والإلغاء
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(0, 10, 0, 0)
        
        self.save_button = QPushButton("حفظ التغييرات" if self.is_edit_mode else "إضافة العملة")
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                font-size: 12px;
                padding: 12px 25px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                font-size: 12px;
                padding: 12px 25px;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.save_button)
        buttons_layout.addWidget(self.cancel_button)
        
        layout.addWidget(buttons_frame)
        
    def setup_connections(self):
        """ربط الأحداث"""
        self.save_button.clicked.connect(self.save_currency)
        self.cancel_button.clicked.connect(self.reject)
        self.is_base_currency_checkbox.toggled.connect(self.on_base_currency_changed)
        
    def on_base_currency_changed(self, checked):
        """عند تغيير حالة العملة الأساسية"""
        if checked:
            self.exchange_rate_spin.setValue(1.0000)
            self.exchange_rate_spin.setEnabled(False)
        else:
            self.exchange_rate_spin.setEnabled(True)
            
    def load_currency_data(self):
        """تحميل بيانات العملة للتعديل"""
        if not self.currency_data:
            return
            
        try:
            self.currency_code_input.setText(self.currency_data.get('currency_code', ''))
            self.currency_name_input.setText(self.currency_data.get('currency_name', ''))
            self.symbol_input.setText(self.currency_data.get('symbol', ''))
            
            # سعر الصرف
            exchange_rate = self.currency_data.get('exchange_rate', 1.0)
            self.exchange_rate_spin.setValue(exchange_rate)
            
            # العملة الأساسية
            is_base = self.currency_data.get('is_base_currency', False)
            self.is_base_currency_checkbox.setChecked(is_base)
            
            # حالة العملة
            is_active = self.currency_data.get('is_active', True)
            self.is_active_checkbox.setChecked(is_active)
            
        except Exception as e:
            QMessageBox.warning(self, "تحذير", f"خطأ في تحميل بيانات العملة: {str(e)}")
            
    def validate_input(self):
        """التحقق من صحة البيانات المدخلة"""
        # التحقق من رمز العملة
        currency_code = self.currency_code_input.text().strip().upper()
        if not currency_code:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رمز العملة")
            self.currency_code_input.setFocus()
            return False
            
        if len(currency_code) != 3:
            QMessageBox.warning(self, "تحذير", "رمز العملة يجب أن يكون 3 أحرف بالضبط")
            self.currency_code_input.setFocus()
            return False
            
        # التحقق من اسم العملة
        currency_name = self.currency_name_input.text().strip()
        if not currency_name:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم العملة")
            self.currency_name_input.setFocus()
            return False
            
        # التحقق من الرمز
        symbol = self.symbol_input.text().strip()
        if not symbol:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رمز العملة")
            self.symbol_input.setFocus()
            return False
            
        # التحقق من سعر الصرف
        exchange_rate = self.exchange_rate_spin.value()
        if exchange_rate <= 0:
            QMessageBox.warning(self, "تحذير", "سعر الصرف يجب أن يكون أكبر من صفر")
            self.exchange_rate_spin.setFocus()
            return False
            
        return True
        
    def save_currency(self):
        """حفظ بيانات العملة"""
        if not self.validate_input():
            return
            
        try:
            # جمع البيانات
            currency_data = {
                'currency_code': self.currency_code_input.text().strip().upper(),
                'currency_name': self.currency_name_input.text().strip(),
                'symbol': self.symbol_input.text().strip(),
                'exchange_rate': self.exchange_rate_spin.value(),
                'is_base_currency': self.is_base_currency_checkbox.isChecked(),
                'is_active': self.is_active_checkbox.isChecked()
            }
            
            # حفظ البيانات
            if self.is_edit_mode:
                currency_id = self.currency_data['currency_id']
                success = self.currency_model.update_currency(currency_id, currency_data)
                message = "تم تحديث بيانات العملة بنجاح"
            else:
                success = self.currency_model.add_currency(currency_data)
                message = "تم إضافة العملة بنجاح"
                
            if success:
                # إذا تم تعيين العملة كأساسية، تحديث العملات الأخرى
                if currency_data['is_base_currency']:
                    if self.is_edit_mode:
                        self.currency_model.set_base_currency(self.currency_data['currency_id'])
                    else:
                        # البحث عن العملة المضافة حديثاً
                        new_currency = self.currency_model.db_manager.fetch_one(
                            "SELECT * FROM currencies WHERE currency_code = ?",
                            (currency_data['currency_code'],)
                        )
                        if new_currency:
                            self.currency_model.set_base_currency(new_currency['currency_id'])
                
                QMessageBox.information(self, "نجح", message)
                self.currency_saved.emit()
                self.accept()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في حفظ بيانات العملة")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ البيانات: {str(e)}")

    def showEvent(self, event):
        """عند إظهار النافذة"""
        super().showEvent(event)
        # ضمان الظهور في المقدمة
        ensure_dialog_visibility(self)

    def exec_(self):
        """تنفيذ النافذة مع ضمان الظهور الصحيح"""
        # ضمان الظهور في المقدمة
        ensure_dialog_visibility(self)
        return super().exec_()
