#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الخطوط والنصوص
يعرض نافذة اختبار لجميع أنواع الخطوط المدعومة
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QWidget, QLabel, QPushButton, QLineEdit, QTextEdit,
                             QGroupBox, QFormLayout, QScrollArea)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from src.utils.font_manager import FontManager, apply_font_to_widget


class FontTestWindow(QMainWindow):
    """نافذة اختبار الخطوط"""
    
    def __init__(self):
        super().__init__()
        self.font_manager = FontManager()
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("اختبار الخطوط - Font Test")
        self.setGeometry(100, 100, 800, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # الويدجت الرئيسي
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # عنوان النافذة
        title_label = QLabel("اختبار الخطوط والنصوص")
        apply_font_to_widget(title_label, "title", bold=True)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; padding: 20px; background-color: #ecf0f1; border-radius: 10px; margin-bottom: 20px;")
        scroll_layout.addWidget(title_label)
        
        # معلومات النظام
        self.add_system_info(scroll_layout)
        
        # اختبار أنواع الخطوط المختلفة
        self.add_font_tests(scroll_layout)
        
        # اختبار النصوص العربية والإنجليزية
        self.add_text_tests(scroll_layout)
        
        # اختبار العناصر التفاعلية
        self.add_interactive_tests(scroll_layout)
        
        # إعداد منطقة التمرير
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(main_widget)
        main_layout.addWidget(scroll_area)
        
    def add_system_info(self, layout):
        """إضافة معلومات النظام والخطوط"""
        group = QGroupBox("معلومات النظام والخطوط")
        group_layout = QFormLayout(group)
        
        # نظام التشغيل
        system_label = QLabel(f"نظام التشغيل: {self.font_manager.system}")
        apply_font_to_widget(system_label, "label")
        group_layout.addRow(system_label)
        
        # أفضل خط عربي
        arabic_font_label = QLabel(f"أفضل خط عربي: {getattr(self.font_manager, 'best_arabic_font', 'غير محدد')}")
        apply_font_to_widget(arabic_font_label, "label")
        group_layout.addRow(arabic_font_label)
        
        # أفضل خط إنجليزي
        english_font_label = QLabel(f"أفضل خط إنجليزي: {getattr(self.font_manager, 'best_english_font', 'غير محدد')}")
        apply_font_to_widget(english_font_label, "label")
        group_layout.addRow(english_font_label)
        
        # عدد الخطوط المتاحة
        available_fonts = self.font_manager.get_available_fonts()
        fonts_count_label = QLabel(f"عدد الخطوط المتاحة: {len(available_fonts)}")
        apply_font_to_widget(fonts_count_label, "label")
        group_layout.addRow(fonts_count_label)
        
        layout.addWidget(group)
        
    def add_font_tests(self, layout):
        """إضافة اختبارات أنواع الخطوط"""
        group = QGroupBox("اختبار أنواع الخطوط")
        group_layout = QVBoxLayout(group)
        
        font_types = [
            ("default", "الخط الافتراضي - Default Font"),
            ("label", "خط التسميات - Label Font"),
            ("button", "خط الأزرار - Button Font"),
            ("input", "خط حقول الإدخال - Input Font"),
            ("header", "خط العناوين - Header Font"),
            ("title", "خط العناوين الرئيسية - Title Font"),
            ("small", "خط صغير - Small Font"),
            ("large", "خط كبير - Large Font")
        ]
        
        for font_type, text in font_types:
            label = QLabel(text)
            apply_font_to_widget(label, font_type, bold=(font_type in ["button", "header", "title"]))
            label.setStyleSheet("padding: 5px; margin: 2px; background-color: #f8f9fa; border-radius: 3px;")
            group_layout.addWidget(label)
            
        layout.addWidget(group)
        
    def add_text_tests(self, layout):
        """إضافة اختبارات النصوص العربية والإنجليزية"""
        group = QGroupBox("اختبار النصوص العربية والإنجليزية")
        group_layout = QVBoxLayout(group)
        
        # نص عربي
        arabic_text = "هذا نص تجريبي باللغة العربية لاختبار وضوح الخط وقابلية القراءة. يجب أن يظهر النص واضحاً ومقروءاً."
        arabic_label = QLabel(arabic_text)
        apply_font_to_widget(arabic_label, "label")
        arabic_label.setWordWrap(True)
        arabic_label.setStyleSheet("padding: 10px; background-color: #e8f5e8; border-radius: 5px; margin: 5px;")
        group_layout.addWidget(arabic_label)
        
        # نص إنجليزي
        english_text = "This is a sample English text to test font clarity and readability. The text should appear clear and readable."
        english_label = QLabel(english_text)
        apply_font_to_widget(english_label, "label")
        english_label.setWordWrap(True)
        english_label.setStyleSheet("padding: 10px; background-color: #e8f0ff; border-radius: 5px; margin: 5px;")
        group_layout.addWidget(english_label)
        
        # نص مختلط
        mixed_text = "نص مختلط: Mixed Text - العربية والإنجليزية معاً 123456"
        mixed_label = QLabel(mixed_text)
        apply_font_to_widget(mixed_label, "label")
        mixed_label.setWordWrap(True)
        mixed_label.setStyleSheet("padding: 10px; background-color: #fff8e8; border-radius: 5px; margin: 5px;")
        group_layout.addWidget(mixed_label)
        
        layout.addWidget(group)
        
    def add_interactive_tests(self, layout):
        """إضافة اختبارات العناصر التفاعلية"""
        group = QGroupBox("اختبار العناصر التفاعلية")
        group_layout = QFormLayout(group)
        
        # حقل إدخال
        input_field = QLineEdit()
        input_field.setPlaceholderText("اكتب هنا لاختبار خط الإدخال")
        apply_font_to_widget(input_field, "input")
        group_layout.addRow("حقل الإدخال:", input_field)
        
        # زر
        test_button = QPushButton("زر اختبار - Test Button")
        apply_font_to_widget(test_button, "button")
        test_button.clicked.connect(self.show_font_info)
        group_layout.addRow("الزر:", test_button)
        
        # منطقة نص
        text_area = QTextEdit()
        text_area.setPlaceholderText("منطقة نص لاختبار الخط في النصوص الطويلة...")
        text_area.setMaximumHeight(100)
        apply_font_to_widget(text_area, "input")
        group_layout.addRow("منطقة النص:", text_area)
        
        layout.addWidget(group)
        
    def show_font_info(self):
        """عرض معلومات الخط"""
        from PyQt5.QtWidgets import QMessageBox
        
        info = f"""
معلومات الخطوط:

نظام التشغيل: {self.font_manager.system}
أفضل خط عربي: {getattr(self.font_manager, 'best_arabic_font', 'غير محدد')}
أفضل خط إنجليزي: {getattr(self.font_manager, 'best_english_font', 'غير محدد')}

الخطوط المتاحة: {len(self.font_manager.get_available_fonts())} خط
        """
        
        msg = QMessageBox()
        msg.setWindowTitle("معلومات الخطوط")
        msg.setText(info)
        apply_font_to_widget(msg, "label")
        msg.exec_()


def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # إعداد اللغة العربية
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء النافذة
    window = FontTestWindow()
    window.show()
    
    # تشغيل التطبيق
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
