#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام إدارة المستخدمين
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.ui.widgets.users_widget import UsersWidget
from src.models.user import User
from src.database.db_manager import DatabaseManager


def create_test_users():
    """إنشاء مستخدمين تجريبيين"""
    user_model = User()
    
    # إنشاء مدير افتراضي
    try:
        user_model.create_default_admin()
        print("✓ تم إنشاء المدير الافتراضي")
    except Exception as e:
        print(f"خطأ في إنشاء المدير الافتراضي: {e}")
    
    # إنشاء مستخدمين تجريبيين
    test_users = [
        {
            'username': 'academic_manager',
            'password': 'manager123',
            'first_name': 'أحمد',
            'last_name': 'محمد',
            'email': '<EMAIL>',
            'role': 'academic_manager',
            'is_active': True
        },
        {
            'username': 'financial_manager',
            'password': 'finance123',
            'first_name': 'فاطمة',
            'last_name': 'علي',
            'email': '<EMAIL>',
            'role': 'financial_manager',
            'is_active': True
        },
        {
            'username': 'teacher1',
            'password': 'teacher123',
            'first_name': 'محمد',
            'last_name': 'أحمد',
            'email': '<EMAIL>',
            'role': 'teacher',
            'is_active': True
        },
        {
            'username': 'secretary1',
            'password': 'secretary123',
            'first_name': 'مريم',
            'last_name': 'سالم',
            'email': '<EMAIL>',
            'role': 'secretary',
            'is_active': True
        },
        {
            'username': 'user1',
            'password': 'user123',
            'first_name': 'خالد',
            'last_name': 'عبدالله',
            'email': '<EMAIL>',
            'role': 'user',
            'is_active': False  # مستخدم غير نشط للاختبار
        }
    ]
    
    for user_data in test_users:
        try:
            # التحقق من وجود المستخدم
            existing_users = user_model.get_all("username = ?", (user_data['username'],))
            if not existing_users:
                user_model.create_user(user_data)
                print(f"✓ تم إنشاء المستخدم: {user_data['username']}")
            else:
                print(f"- المستخدم موجود مسبقاً: {user_data['username']}")
        except Exception as e:
            print(f"خطأ في إنشاء المستخدم {user_data['username']}: {e}")


def test_user_authentication():
    """اختبار مصادقة المستخدمين"""
    user_model = User()
    
    print("\n=== اختبار المصادقة ===")
    
    # اختبار مصادقة صحيحة
    try:
        user = user_model.authenticate('admin', 'admin123')
        if user:
            print("✓ مصادقة المدير نجحت")
        else:
            print("✗ فشل في مصادقة المدير")
    except Exception as e:
        print(f"خطأ في مصادقة المدير: {e}")

    # اختبار مصادقة خاطئة
    try:
        user = user_model.authenticate('admin', 'wrong_password')
        if user:
            print("✗ مصادقة خاطئة نجحت (خطأ!)")
        else:
            print("✓ رفض كلمة مرور خاطئة")
    except Exception as e:
        print(f"خطأ في اختبار كلمة مرور خاطئة: {e}")


def test_user_permissions():
    """اختبار صلاحيات المستخدمين"""
    user_model = User()
    
    print("\n=== اختبار الصلاحيات ===")
    
    try:
        # الحصول على المدير
        admin = user_model.get_user_by_username('admin')
        if admin:
            # اختبار صلاحيات المدير
            has_admin_permission = user_model.has_permission(admin['user_id'], 'system_admin')
            print(f"✓ المدير لديه صلاحية النظام: {has_admin_permission}")
            
            # عرض جميع صلاحيات المدير
            permissions = user_model.get_user_permissions(admin['user_id'])
            print(f"✓ عدد صلاحيات المدير: {len(permissions)}")
        
        # اختبار صلاحيات معلم
        teacher = user_model.get_user_by_username('teacher1')
        if teacher:
            has_admin_permission = user_model.has_permission(teacher['user_id'], 'system_admin')
            has_view_students = user_model.has_permission(teacher['user_id'], 'view_students')
            print(f"✓ المعلم ليس لديه صلاحية النظام: {not has_admin_permission}")
            print(f"✓ المعلم لديه صلاحية عرض الطلاب: {has_view_students}")
            
    except Exception as e:
        print(f"خطأ في اختبار الصلاحيات: {e}")


def test_user_statistics():
    """اختبار إحصائيات المستخدمين"""
    user_model = User()
    
    print("\n=== إحصائيات المستخدمين ===")
    
    try:
        stats = user_model.get_user_statistics()
        print(f"✓ المستخدمون النشطون: {stats.get('total_active', 0)}")
        print(f"✓ المستخدمون غير النشطين: {stats.get('total_inactive', 0)}")
        
        print("✓ المستخدمون حسب الدور:")
        for role, count in stats.get('by_role', {}).items():
            from src.utils.config import Config
            role_name = Config.USER_ROLES.get(role, role)
            print(f"  - {role_name}: {count}")
            
    except Exception as e:
        print(f"خطأ في جلب الإحصائيات: {e}")


class TestUsersWindow(QMainWindow):
    """نافذة اختبار نظام المستخدمين"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار نظام إدارة المستخدمين")
        self.setGeometry(100, 100, 1200, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # إضافة ويدجت إدارة المستخدمين
        self.users_widget = UsersWidget()
        layout.addWidget(self.users_widget)


def main():
    """الدالة الرئيسية"""
    print("=== اختبار نظام إدارة المستخدمين ===\n")
    
    # إنشاء قاعدة البيانات
    try:
        db_manager = DatabaseManager()
        db_manager.create_tables()
        print("✓ تم إنشاء قاعدة البيانات")
    except Exception as e:
        print(f"خطأ في إنشاء قاعدة البيانات: {e}")
        return
    
    # إنشاء مستخدمين تجريبيين
    create_test_users()
    
    # اختبار المصادقة
    test_user_authentication()
    
    # اختبار الصلاحيات
    test_user_permissions()
    
    # اختبار الإحصائيات
    test_user_statistics()
    
    print("\n=== تشغيل واجهة المستخدم ===")
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    
    # تطبيق الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء النافذة
    window = TestUsersWindow()
    window.show()
    
    # تشغيل التطبيق
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
