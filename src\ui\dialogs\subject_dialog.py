#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إضافة وتعديل المواد الدراسية
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QLineEdit, QPushButton, QLabel, QFrame, QMessageBox,
                             QComboBox, QTextEdit, QSpinBox, QCheckBox, QTabWidget, QWidget)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

from src.models.subject import Subject
from src.utils.ui_styles import UIStyles
from src.utils.dialog_enhancer import EnhancedDialog


class SubjectDialog(EnhancedDialog):
    """نافذة إضافة وتعديل المواد الدراسية"""
    
    # إشارة حفظ البيانات
    subject_saved = pyqtSignal()
    
    def __init__(self, subject_id=None, parent=None):
        super().__init__(parent)
        self.subject_id = subject_id
        self.subject_model = Subject()
        self.is_edit_mode = subject_id is not None
        
        self.setup_ui()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.load_subject_data()
            
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة
        title = "تعديل مادة دراسية" if self.is_edit_mode else "إضافة مادة دراسية جديدة"
        self.setWindowTitle(title)
        self.setFixedSize(600, 500)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # عنوان النافذة
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)

        # علامات التبويب
        self.tab_widget = QTabWidget()

        # تبويب البيانات الأساسية
        self.basic_tab = QWidget()
        self.setup_basic_tab()
        self.tab_widget.addTab(self.basic_tab, "البيانات الأساسية")

        # تبويب الإعدادات
        self.settings_tab = QWidget()
        self.setup_settings_tab()
        self.tab_widget.addTab(self.settings_tab, "الإعدادات")

        main_layout.addWidget(self.tab_widget)

        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        self.save_button = QPushButton("حفظ")
        self.save_button.setFixedHeight(40)
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setFixedHeight(40)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)

        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.save_button)

        main_layout.addLayout(buttons_layout)

    def setup_basic_tab(self):
        """إعداد تبويب البيانات الأساسية"""
        layout = QFormLayout(self.basic_tab)
        layout.setSpacing(15)

        # رمز المادة
        self.subject_code_input = QLineEdit()
        self.subject_code_input.setPlaceholderText("أدخل رمز المادة (مثل: MATH101)")
        layout.addRow("رمز المادة *:", self.subject_code_input)

        # اسم المادة
        self.subject_name_input = QLineEdit()
        self.subject_name_input.setPlaceholderText("أدخل اسم المادة")
        layout.addRow("اسم المادة *:", self.subject_name_input)

        # الساعات المعتمدة
        self.credit_hours_spin = QSpinBox()
        self.credit_hours_spin.setRange(1, 10)
        self.credit_hours_spin.setValue(1)
        self.credit_hours_spin.setSuffix(" ساعة")
        layout.addRow("الساعات المعتمدة:", self.credit_hours_spin)

        # الوصف
        self.description_input = QTextEdit()
        self.description_input.setPlaceholderText("أدخل وصف المادة (اختياري)")
        self.description_input.setMaximumHeight(100)
        layout.addRow("الوصف:", self.description_input)

    def setup_settings_tab(self):
        """إعداد تبويب الإعدادات"""
        layout = QFormLayout(self.settings_tab)
        layout.setSpacing(15)

        # الحالة
        self.is_active_checkbox = QCheckBox("المادة نشطة")
        self.is_active_checkbox.setChecked(True)
        layout.addRow("الحالة:", self.is_active_checkbox)

        # نوع المادة
        self.subject_type_combo = QComboBox()
        self.subject_type_combo.addItems(["أساسية", "اختيارية", "متطلب جامعة", "متطلب كلية"])
        layout.addRow("نوع المادة:", self.subject_type_combo)

        # المستوى
        self.level_combo = QComboBox()
        self.level_combo.addItems(["المستوى الأول", "المستوى الثاني", "المستوى الثالث", "المستوى الرابع"])
        layout.addRow("المستوى:", self.level_combo)

        # الفصل الدراسي
        self.semester_combo = QComboBox()
        self.semester_combo.addItems(["الفصل الأول", "الفصل الثاني", "الفصل الصيفي"])
        layout.addRow("الفصل الدراسي:", self.semester_combo)

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        self.save_button.clicked.connect(self.save_subject)
        self.cancel_button.clicked.connect(self.reject)
        
    def load_subject_data(self):
        """تحميل بيانات المادة للتعديل"""
        try:
            subject = self.subject_model.get_by_id(self.subject_id)
            if not subject:
                QMessageBox.critical(self, "خطأ", "لم يتم العثور على المادة")
                self.reject()
                return
                
            # ملء الحقول بالبيانات
            self.subject_code_input.setText(str(subject['subject_code']))
            self.subject_name_input.setText(str(subject['subject_name']))
            self.credit_hours_spin.setValue(subject['credit_hours'] or 1)
            self.description_input.setPlainText(str(subject['description'] or ''))
            self.is_active_checkbox.setChecked(bool(subject.get('is_active', True)))

            # ملء الحقول الجديدة (مع قيم افتراضية إذا لم تكن موجودة)
            if hasattr(self, 'subject_type_combo'):
                self.subject_type_combo.setCurrentText(subject.get('subject_type', 'أساسية'))
            if hasattr(self, 'level_combo'):
                self.level_combo.setCurrentText(subject.get('level', 'المستوى الأول'))
            if hasattr(self, 'semester_combo'):
                self.semester_combo.setCurrentText(subject.get('semester', 'الفصل الأول'))
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل بيانات المادة: {str(e)}")
            
    def save_subject(self):
        """حفظ بيانات المادة"""
        try:
            # جمع البيانات من النموذج
            subject_data = {
                'subject_code': self.subject_code_input.text().strip(),
                'subject_name': self.subject_name_input.text().strip(),
                'credit_hours': self.credit_hours_spin.value(),
                'description': self.description_input.toPlainText().strip() or None,
                'is_active': self.is_active_checkbox.isChecked()
            }

            # إضافة الحقول الجديدة إذا كانت موجودة
            if hasattr(self, 'subject_type_combo'):
                subject_data['subject_type'] = self.subject_type_combo.currentText()
            if hasattr(self, 'level_combo'):
                subject_data['level'] = self.level_combo.currentText()
            if hasattr(self, 'semester_combo'):
                subject_data['semester'] = self.semester_combo.currentText()
            
            # التحقق من الحقول المطلوبة
            if not subject_data['subject_code']:
                QMessageBox.warning(self, "تحذير", "رمز المادة مطلوب")
                self.subject_code_input.setFocus()
                return
                
            if not subject_data['subject_name']:
                QMessageBox.warning(self, "تحذير", "اسم المادة مطلوب")
                self.subject_name_input.setFocus()
                return
            
            # حفظ البيانات
            if self.is_edit_mode:
                self.subject_model.update_subject(self.subject_id, subject_data)
                QMessageBox.information(self, "نجح", "تم تحديث المادة بنجاح")
            else:
                self.subject_model.add_subject(subject_data)
                QMessageBox.information(self, "نجح", "تم إضافة المادة بنجاح")
            
            # إرسال إشارة الحفظ
            self.subject_saved.emit()
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ البيانات: {str(e)}")
