#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة تغيير كلمة المرور
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QLineEdit, QPushButton, QLabel, QFrame, QMessageBox,
                             QCheckBox, QProgressBar)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

from src.models.user import User


class ChangePasswordDialog(QDialog):
    """نافذة تغيير كلمة المرور"""
    
    # إشارة تغيير كلمة المرور
    password_changed = pyqtSignal()
    
    def __init__(self, user_id=None, username="", is_self=False, parent=None):
        super().__init__(parent)
        self.user_id = user_id
        self.username = username
        self.is_self = is_self  # هل المستخدم يغير كلمة مروره الخاصة
        self.user_model = User()
        
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة
        title = "تغيير كلمة المرور"
        if self.username:
            title += f" - {self.username}"
            
        self.setWindowTitle(title)
        self.setFixedSize(450, 400)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # عنوان النافذة
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # نموذج تغيير كلمة المرور
        form_frame = QFrame()
        form_frame.setFrameStyle(QFrame.StyledPanel)
        form_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        form_layout = QFormLayout(form_frame)
        form_layout.setSpacing(15)
        
        # كلمة المرور الحالية (فقط إذا كان المستخدم يغير كلمة مروره)
        if self.is_self:
            self.current_password_input = QLineEdit()
            self.current_password_input.setEchoMode(QLineEdit.Password)
            self.current_password_input.setPlaceholderText("أدخل كلمة المرور الحالية")
            self.current_password_input.setStyleSheet(self.get_input_style())
            form_layout.addRow("كلمة المرور الحالية *:", self.current_password_input)
        
        # كلمة المرور الجديدة
        self.new_password_input = QLineEdit()
        self.new_password_input.setEchoMode(QLineEdit.Password)
        self.new_password_input.setPlaceholderText("أدخل كلمة المرور الجديدة")
        self.new_password_input.setStyleSheet(self.get_input_style())
        self.new_password_input.textChanged.connect(self.check_password_strength)
        form_layout.addRow("كلمة المرور الجديدة *:", self.new_password_input)
        
        # تأكيد كلمة المرور الجديدة
        self.confirm_password_input = QLineEdit()
        self.confirm_password_input.setEchoMode(QLineEdit.Password)
        self.confirm_password_input.setPlaceholderText("تأكيد كلمة المرور الجديدة")
        self.confirm_password_input.setStyleSheet(self.get_input_style())
        self.confirm_password_input.textChanged.connect(self.check_password_match)
        form_layout.addRow("تأكيد كلمة المرور *:", self.confirm_password_input)
        
        # مؤشر قوة كلمة المرور
        self.strength_label = QLabel("قوة كلمة المرور:")
        form_layout.addRow("", self.strength_label)
        
        self.strength_bar = QProgressBar()
        self.strength_bar.setRange(0, 100)
        self.strength_bar.setValue(0)
        self.strength_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #e74c3c;
                border-radius: 3px;
            }
        """)
        form_layout.addRow("", self.strength_bar)
        
        # رسالة التطابق
        self.match_label = QLabel("")
        self.match_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                padding: 5px;
            }
        """)
        form_layout.addRow("", self.match_label)
        
        # خيار إظهار كلمات المرور
        self.show_passwords_checkbox = QCheckBox("إظهار كلمات المرور")
        self.show_passwords_checkbox.toggled.connect(self.toggle_password_visibility)
        form_layout.addRow("", self.show_passwords_checkbox)
        
        main_layout.addWidget(form_frame)
        
        # نصائح كلمة المرور
        tips_frame = QFrame()
        tips_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        
        tips_layout = QVBoxLayout(tips_frame)
        tips_title = QLabel("نصائح لكلمة مرور قوية:")
        tips_title.setStyleSheet("font-weight: bold; color: #495057;")
        tips_layout.addWidget(tips_title)
        
        tips = [
            "• استخدم 8 أحرف على الأقل",
            "• امزج بين الأحرف الكبيرة والصغيرة",
            "• أضف أرقام ورموز خاصة",
            "• تجنب المعلومات الشخصية",
            "• لا تستخدم كلمات مرور شائعة"
        ]
        
        for tip in tips:
            tip_label = QLabel(tip)
            tip_label.setStyleSheet("color: #6c757d; font-size: 11px;")
            tips_layout.addWidget(tip_label)
        
        main_layout.addWidget(tips_frame)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        self.save_button = QPushButton("تغيير كلمة المرور")
        self.save_button.setFixedHeight(40)
        self.save_button.setEnabled(False)  # معطل في البداية
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setFixedHeight(40)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.save_button)
        
        main_layout.addLayout(buttons_layout)
        
    def get_input_style(self):
        """الحصول على أنماط حقول الإدخال"""
        return """
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                padding: 8px 12px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498db;
                outline: none;
            }
        """
        
    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        self.save_button.clicked.connect(self.change_password)
        self.cancel_button.clicked.connect(self.reject)
        
    def toggle_password_visibility(self, show):
        """تبديل إظهار/إخفاء كلمات المرور"""
        echo_mode = QLineEdit.Normal if show else QLineEdit.Password
        
        if hasattr(self, 'current_password_input'):
            self.current_password_input.setEchoMode(echo_mode)
        self.new_password_input.setEchoMode(echo_mode)
        self.confirm_password_input.setEchoMode(echo_mode)
        
    def check_password_strength(self):
        """فحص قوة كلمة المرور"""
        password = self.new_password_input.text()
        strength = 0
        feedback = []
        
        # طول كلمة المرور
        if len(password) >= 8:
            strength += 25
        else:
            feedback.append("قصيرة جداً")
            
        # وجود أحرف كبيرة
        if any(c.isupper() for c in password):
            strength += 25
        else:
            feedback.append("تحتاج أحرف كبيرة")
            
        # وجود أحرف صغيرة
        if any(c.islower() for c in password):
            strength += 25
        else:
            feedback.append("تحتاج أحرف صغيرة")
            
        # وجود أرقام أو رموز
        if any(c.isdigit() or not c.isalnum() for c in password):
            strength += 25
        else:
            feedback.append("تحتاج أرقام أو رموز")
        
        # تحديث مؤشر القوة
        self.strength_bar.setValue(strength)
        
        # تغيير لون المؤشر حسب القوة
        if strength < 50:
            color = "#e74c3c"  # أحمر
            text = "ضعيفة"
        elif strength < 75:
            color = "#f39c12"  # برتقالي
            text = "متوسطة"
        else:
            color = "#27ae60"  # أخضر
            text = "قوية"
            
        self.strength_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                text-align: center;
            }}
            QProgressBar::chunk {{
                background-color: {color};
                border-radius: 3px;
            }}
        """)
        
        if feedback:
            self.strength_label.setText(f"قوة كلمة المرور: {text} ({', '.join(feedback)})")
        else:
            self.strength_label.setText(f"قوة كلمة المرور: {text}")
            
        self.check_form_validity()
        
    def check_password_match(self):
        """فحص تطابق كلمات المرور"""
        new_password = self.new_password_input.text()
        confirm_password = self.confirm_password_input.text()
        
        if not confirm_password:
            self.match_label.setText("")
        elif new_password == confirm_password:
            self.match_label.setText("✓ كلمات المرور متطابقة")
            self.match_label.setStyleSheet("color: #27ae60; font-size: 12px; padding: 5px;")
        else:
            self.match_label.setText("✗ كلمات المرور غير متطابقة")
            self.match_label.setStyleSheet("color: #e74c3c; font-size: 12px; padding: 5px;")
            
        self.check_form_validity()
        
    def check_form_validity(self):
        """فحص صحة النموذج"""
        valid = True
        
        # التحقق من كلمة المرور الحالية (إذا كانت مطلوبة)
        if hasattr(self, 'current_password_input'):
            if not self.current_password_input.text():
                valid = False
        
        # التحقق من كلمة المرور الجديدة
        if len(self.new_password_input.text()) < 6:
            valid = False
            
        # التحقق من تطابق كلمات المرور
        if self.new_password_input.text() != self.confirm_password_input.text():
            valid = False
            
        self.save_button.setEnabled(valid)
        
    def change_password(self):
        """تغيير كلمة المرور"""
        try:
            new_password = self.new_password_input.text()
            
            if self.is_self:
                # المستخدم يغير كلمة مروره الخاصة
                current_password = self.current_password_input.text()
                self.user_model.change_password(self.user_id, current_password, new_password)
            else:
                # المدير يعيد تعيين كلمة مرور مستخدم آخر
                hashed_password = self.user_model.hash_password(new_password)
                self.user_model.update(self.user_id, {'password_hash': hashed_password})
            
            QMessageBox.information(self, "نجح", "تم تغيير كلمة المرور بنجاح")
            
            # إرسال إشارة التغيير
            self.password_changed.emit()
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تغيير كلمة المرور: {str(e)}")
