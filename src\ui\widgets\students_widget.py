#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ويدجت إدارة الطلاب
يحتوي على واجهات عرض وإضافة وتعديل الطلاب
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                             QMessageBox, QHeaderView, QFrame)
from PyQt5.QtCore import Qt

from src.models.student import Student
from src.ui.dialogs.student_dialog import StudentDialog
from src.utils.icon_manager import get_icon


class StudentsWidget(QWidget):
    """ويدجت إدارة الطلاب"""
    
    def __init__(self):
        super().__init__()
        self.student_model = Student()
        self.setup_ui()
        self.load_students()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # شريط البحث والأزرار
        top_frame = QFrame()
        top_layout = QHBoxLayout(top_frame)
        
        # حقل البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("البحث عن طالب...")
        self.search_input.setFixedHeight(35)
        self.search_input.textChanged.connect(self.search_students)
        
        # أزرار الإجراءات
        self.add_button = QPushButton("إضافة طالب")
        self.add_button.setIcon(get_icon("add", 16))

        self.edit_button = QPushButton("تعديل")
        self.edit_button.setIcon(get_icon("edit", 16))

        self.delete_button = QPushButton("حذف")
        self.delete_button.setIcon(get_icon("delete", 16))

        self.refresh_button = QPushButton("تحديث")
        self.refresh_button.setIcon(get_icon("refresh", 16))
        
        # تطبيق الأنماط على الأزرار
        button_style = """
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """
        
        for button in [self.add_button, self.edit_button, self.delete_button, self.refresh_button]:
            button.setStyleSheet(button_style)
            button.setFixedHeight(35)
        
        # تخصيص لون زر الحذف
        self.delete_button.setStyleSheet(button_style.replace("#3498db", "#e74c3c").replace("#2980b9", "#c0392b"))
        
        top_layout.addWidget(QLabel("البحث:"))
        top_layout.addWidget(self.search_input)
        top_layout.addStretch()
        top_layout.addWidget(self.add_button)
        top_layout.addWidget(self.edit_button)
        top_layout.addWidget(self.delete_button)
        top_layout.addWidget(self.refresh_button)
        
        layout.addWidget(top_frame)
        
        # جدول الطلاب
        self.students_table = QTableWidget()
        self.setup_table()
        layout.addWidget(self.students_table)
        
        # ربط الأحداث
        self.setup_connections()
        
    def setup_table(self):
        """إعداد جدول الطلاب"""
        # تحديد الأعمدة
        columns = [
            "رقم الطالب", "الاسم الأول", "الاسم الأخير", 
            "تاريخ الميلاد", "الجنس", "ولي الأمر", "رقم الهاتف", "الحالة"
        ]
        
        self.students_table.setColumnCount(len(columns))
        self.students_table.setHorizontalHeaderLabels(columns)
        
        # إعداد خصائص الجدول
        self.students_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.students_table.setAlternatingRowColors(True)
        self.students_table.setSortingEnabled(True)
        
        # تخصيص عرض الأعمدة
        header = self.students_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        # تطبيق الأنماط
        self.students_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)
        
    def setup_connections(self):
        """ربط الأحداث"""
        self.add_button.clicked.connect(self.add_student)
        self.edit_button.clicked.connect(self.edit_student)
        self.delete_button.clicked.connect(self.delete_student)
        self.refresh_button.clicked.connect(self.load_students)
        
    def load_students(self):
        """تحميل قائمة الطلاب"""
        try:
            students = self.student_model.get_active_students()
            self.populate_table(students)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل الطلاب: {str(e)}")
            
    def populate_table(self, students):
        """ملء الجدول بالبيانات"""
        self.students_table.setRowCount(len(students))

        for row, student in enumerate(students):
            try:
                # رقم الطالب
                self.students_table.setItem(row, 0, QTableWidgetItem(str(student['student_number'] or '')))

                # الاسم الأول
                self.students_table.setItem(row, 1, QTableWidgetItem(str(student['first_name'] or '')))

                # الاسم الأخير
                self.students_table.setItem(row, 2, QTableWidgetItem(str(student['last_name'] or '')))

                # تاريخ الميلاد
                self.students_table.setItem(row, 3, QTableWidgetItem(str(student['date_of_birth'] or '')))

                # الجنس
                gender = student['gender'] if 'gender' in student else 'male'
                gender_text = "ذكر" if gender == 'male' else "أنثى"
                self.students_table.setItem(row, 4, QTableWidgetItem(gender_text))

                # ولي الأمر
                self.students_table.setItem(row, 5, QTableWidgetItem(str(student['parent_name'] or '')))

                # رقم الهاتف
                self.students_table.setItem(row, 6, QTableWidgetItem(str(student['parent_phone'] or '')))

                # الحالة
                status = student['status'] if 'status' in student else 'active'
                status_text = "نشط" if status == 'active' else "غير نشط"
                self.students_table.setItem(row, 7, QTableWidgetItem(status_text))

                # حفظ معرف الطالب في البيانات المخفية
                self.students_table.item(row, 0).setData(Qt.UserRole, student.get('student_id'))

            except Exception as e:
                print(f"خطأ في إضافة الطالب رقم {row}: {e}")
                # إضافة صف فارغ في حالة الخطأ
                for col in range(8):
                    self.students_table.setItem(row, col, QTableWidgetItem("خطأ في البيانات"))
            
    def search_students(self):
        """البحث في الطلاب"""
        search_text = self.search_input.text().strip()
        
        if not search_text:
            self.load_students()
            return
            
        try:
            students = self.student_model.search_students(search_text)
            self.populate_table(students)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في البحث: {str(e)}")
            
    def add_student(self):
        """إضافة طالب جديد"""
        dialog = StudentDialog(parent=self)
        dialog.student_saved.connect(self.load_students)
        dialog.exec_()
        
    def edit_student(self):
        """تعديل طالب"""
        current_row = self.students_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار طالب للتعديل")
            return

        # الحصول على معرف الطالب
        student_id = self.students_table.item(current_row, 0).data(Qt.UserRole)

        dialog = StudentDialog(student_id=student_id, parent=self)
        dialog.student_saved.connect(self.load_students)
        dialog.exec_()
        
    def delete_student(self):
        """حذف طالب"""
        current_row = self.students_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار طالب للحذف")
            return
            
        # الحصول على معرف الطالب
        student_id = self.students_table.item(current_row, 0).data(Qt.UserRole)
        student_name = f"{self.students_table.item(current_row, 1).text()} {self.students_table.item(current_row, 2).text()}"
        
        # تأكيد الحذف
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل أنت متأكد من رغبتك في حذف الطالب: {student_name}؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                self.student_model.deactivate_student(student_id, "تم الحذف من الواجهة")
                QMessageBox.information(self, "نجح", "تم حذف الطالب بنجاح")
                self.load_students()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ في حذف الطالب: {str(e)}")
