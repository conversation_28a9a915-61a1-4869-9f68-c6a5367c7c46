#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إضافة وتعديل الرواتب
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QLineEdit, QPushButton, QLabel, QFrame, QMessageBox,
                             QComboBox, QDateEdit, QTextEdit, QDoubleSpinBox,
                             QSpinBox)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont

from src.models.teacher import Teacher


class SalaryDialog(QDialog):
    """نافذة إضافة وتعديل الرواتب"""
    
    # إشارة حفظ البيانات
    salary_saved = pyqtSignal()
    
    def __init__(self, salary_id=None, parent=None):
        super().__init__(parent)
        self.salary_id = salary_id
        self.teacher_model = Teacher()
        self.is_edit_mode = salary_id is not None
        
        self.setup_ui()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.load_salary_data()
        else:
            self.load_teachers()
            
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة
        title = "تعديل راتب" if self.is_edit_mode else "إضافة راتب جديد"
        self.setWindowTitle(title)
        self.setFixedSize(500, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # عنوان النافذة
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # نموذج البيانات
        form_frame = QFrame()
        form_frame.setFrameStyle(QFrame.StyledPanel)
        form_layout = QFormLayout(form_frame)
        form_layout.setSpacing(15)
        
        # اختيار المعلم
        self.teacher_combo = QComboBox()
        self.teacher_combo.setEnabled(not self.is_edit_mode)
        form_layout.addRow("المعلم *:", self.teacher_combo)
        
        # الشهر
        self.month_combo = QComboBox()
        months = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                 "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]
        self.month_combo.addItems(months)
        self.month_combo.setCurrentIndex(QDate.currentDate().month() - 1)
        form_layout.addRow("الشهر *:", self.month_combo)
        
        # السنة
        self.year_spin = QSpinBox()
        self.year_spin.setRange(2020, 2030)
        self.year_spin.setValue(QDate.currentDate().year())
        form_layout.addRow("السنة *:", self.year_spin)
        
        # الراتب الأساسي
        self.basic_salary_input = QDoubleSpinBox()
        self.basic_salary_input.setRange(0, 999999)
        self.basic_salary_input.setSuffix(" ريال")
        self.basic_salary_input.setValue(5000)
        form_layout.addRow("الراتب الأساسي *:", self.basic_salary_input)
        
        # البدلات
        self.allowances_input = QDoubleSpinBox()
        self.allowances_input.setRange(0, 999999)
        self.allowances_input.setSuffix(" ريال")
        self.allowances_input.setValue(0)
        form_layout.addRow("البدلات:", self.allowances_input)
        
        # الخصومات
        self.deductions_input = QDoubleSpinBox()
        self.deductions_input.setRange(0, 999999)
        self.deductions_input.setSuffix(" ريال")
        self.deductions_input.setValue(0)
        form_layout.addRow("الخصومات:", self.deductions_input)
        
        # العمل الإضافي
        self.overtime_input = QDoubleSpinBox()
        self.overtime_input.setRange(0, 999999)
        self.overtime_input.setSuffix(" ريال")
        self.overtime_input.setValue(0)
        form_layout.addRow("العمل الإضافي:", self.overtime_input)
        
        # إجمالي الراتب (محسوب تلقائياً)
        self.total_salary_label = QLabel("0.00 ريال")
        self.total_salary_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #27ae60;
                padding: 5px;
                background-color: #ecf0f1;
                border-radius: 3px;
            }
        """)
        form_layout.addRow("إجمالي الراتب:", self.total_salary_label)
        
        # تاريخ الدفع
        self.payment_date_input = QDateEdit()
        self.payment_date_input.setDate(QDate.currentDate())
        self.payment_date_input.setCalendarPopup(True)
        form_layout.addRow("تاريخ الدفع:", self.payment_date_input)
        
        # حالة الدفع
        self.status_combo = QComboBox()
        self.status_combo.addItems(["معلق", "مدفوع"])
        form_layout.addRow("حالة الدفع:", self.status_combo)
        
        # ملاحظات
        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText("أدخل أي ملاحظات إضافية")
        self.notes_input.setMaximumHeight(80)
        form_layout.addRow("ملاحظات:", self.notes_input)
        
        main_layout.addWidget(form_frame)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        self.save_button = QPushButton("حفظ")
        self.save_button.setFixedHeight(40)
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setFixedHeight(40)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.save_button)
        
        main_layout.addLayout(buttons_layout)
        
    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        self.save_button.clicked.connect(self.save_salary)
        self.cancel_button.clicked.connect(self.reject)
        
        # ربط حساب إجمالي الراتب
        self.basic_salary_input.valueChanged.connect(self.calculate_total)
        self.allowances_input.valueChanged.connect(self.calculate_total)
        self.deductions_input.valueChanged.connect(self.calculate_total)
        self.overtime_input.valueChanged.connect(self.calculate_total)
        
    def load_teachers(self):
        """تحميل قائمة المعلمين"""
        try:
            teachers = self.teacher_model.get_active_teachers()
            self.teacher_combo.clear()
            
            for teacher in teachers:
                name = f"{teacher['first_name']} {teacher['last_name']} - {teacher['employee_number']}"
                self.teacher_combo.addItem(name, teacher['teacher_id'])
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل المعلمين: {str(e)}")
            
    def calculate_total(self):
        """حساب إجمالي الراتب"""
        basic = self.basic_salary_input.value()
        allowances = self.allowances_input.value()
        deductions = self.deductions_input.value()
        overtime = self.overtime_input.value()
        
        total = basic + allowances + overtime - deductions
        self.total_salary_label.setText(f"{total:,.2f} ريال")
        
    def load_salary_data(self):
        """تحميل بيانات الراتب للتعديل"""
        try:
            # استعلام بيانات الراتب
            query = """
            SELECT s.*, t.first_name, t.last_name, t.employee_number
            FROM salaries s
            JOIN teachers t ON s.teacher_id = t.teacher_id
            WHERE s.salary_id = ?
            """
            salary = self.teacher_model.db_manager.fetch_one(query, (self.salary_id,))
            
            if not salary:
                QMessageBox.critical(self, "خطأ", "لم يتم العثور على الراتب")
                self.reject()
                return
                
            # ملء الحقول بالبيانات
            teacher_name = f"{salary['first_name']} {salary['last_name']} - {salary['employee_number']}"
            self.teacher_combo.addItem(teacher_name, salary['teacher_id'])
            self.teacher_combo.setCurrentIndex(0)
            
            self.month_combo.setCurrentIndex(salary['month'] - 1)
            self.year_spin.setValue(salary['year'])
            self.basic_salary_input.setValue(salary['basic_salary'])
            self.allowances_input.setValue(salary['allowances'] or 0)
            self.deductions_input.setValue(salary['deductions'] or 0)
            self.overtime_input.setValue(salary['overtime'] or 0)
            
            # تاريخ الدفع
            if salary['payment_date']:
                date = QDate.fromString(str(salary['payment_date']), "yyyy-MM-dd")
                self.payment_date_input.setDate(date)
                
            # حالة الدفع
            status_index = 1 if salary['status'] == 'paid' else 0
            self.status_combo.setCurrentIndex(status_index)
            
            self.notes_input.setPlainText(str(salary['notes'] or ''))
            
            # حساب الإجمالي
            self.calculate_total()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل بيانات الراتب: {str(e)}")
            
    def save_salary(self):
        """حفظ بيانات الراتب"""
        try:
            # التحقق من اختيار المعلم
            if self.teacher_combo.currentIndex() < 0:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار المعلم")
                return
                
            # جمع البيانات من النموذج
            teacher_id = self.teacher_combo.currentData()
            month = self.month_combo.currentIndex() + 1
            year = self.year_spin.value()
            basic_salary = self.basic_salary_input.value()
            allowances = self.allowances_input.value()
            deductions = self.deductions_input.value()
            overtime = self.overtime_input.value()
            total_salary = basic_salary + allowances + overtime - deductions
            
            salary_data = {
                'teacher_id': teacher_id,
                'month': month,
                'year': year,
                'basic_salary': basic_salary,
                'allowances': allowances,
                'deductions': deductions,
                'overtime': overtime,
                'total_salary': total_salary,
                'payment_date': self.payment_date_input.date().toString("yyyy-MM-dd") if self.status_combo.currentIndex() == 1 else None,
                'status': 'paid' if self.status_combo.currentIndex() == 1 else 'pending',
                'notes': self.notes_input.toPlainText().strip() or None
            }
            
            # حفظ البيانات
            if self.is_edit_mode:
                # تحديث الراتب
                query = """
                UPDATE salaries SET 
                basic_salary = ?, allowances = ?, deductions = ?, overtime = ?,
                total_salary = ?, payment_date = ?, status = ?, notes = ?
                WHERE salary_id = ?
                """
                params = (
                    salary_data['basic_salary'], salary_data['allowances'],
                    salary_data['deductions'], salary_data['overtime'],
                    salary_data['total_salary'], salary_data['payment_date'],
                    salary_data['status'], salary_data['notes'], self.salary_id
                )
                self.teacher_model.db_manager.execute_query(query, params)
                QMessageBox.information(self, "نجح", "تم تحديث الراتب بنجاح")
            else:
                # إضافة راتب جديد
                # التحقق من عدم وجود راتب للشهر نفسه
                existing_query = """
                SELECT salary_id FROM salaries 
                WHERE teacher_id = ? AND month = ? AND year = ?
                """
                existing = self.teacher_model.db_manager.fetch_one(
                    existing_query, (teacher_id, month, year)
                )
                
                if existing:
                    QMessageBox.warning(self, "تحذير", "يوجد راتب مسجل لهذا المعلم في نفس الشهر")
                    return
                
                # إدراج الراتب الجديد
                query = """
                INSERT INTO salaries (teacher_id, month, year, basic_salary, allowances,
                deductions, overtime, total_salary, payment_date, status, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                params = (
                    salary_data['teacher_id'], salary_data['month'], salary_data['year'],
                    salary_data['basic_salary'], salary_data['allowances'],
                    salary_data['deductions'], salary_data['overtime'],
                    salary_data['total_salary'], salary_data['payment_date'],
                    salary_data['status'], salary_data['notes']
                )
                self.teacher_model.db_manager.execute_query(query, params)
                QMessageBox.information(self, "نجح", "تم إضافة الراتب بنجاح")
            
            # إرسال إشارة الحفظ
            self.salary_saved.emit()
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ البيانات: {str(e)}")
