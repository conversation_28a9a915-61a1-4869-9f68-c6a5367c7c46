#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نموذج الصفوف والفصول الدراسية
يحتوي على جميع العمليات المتعلقة بإدارة الصفوف والفصول
"""

from src.models.base_model import BaseModel


class ClassModel(BaseModel):
    """نموذج الصفوف والفصول الدراسية"""
    
    def __init__(self):
        super().__init__()
        self.table_name = "classes"
        self.primary_key = "class_id"
        
        # الحقول المطلوبة
        self.required_fields = [
            'class_name', 'grade_level', 'academic_year'
        ]
        
        # حقول البحث
        self.search_fields = [
            'class_name', 'grade_level', 'academic_year', 'description'
        ]
    
    def add_class(self, class_data):
        """إضافة صف دراسي جديد"""
        try:
            # التحقق من الحقول المطلوبة
            self.validate_required_fields(class_data)

            # التحقق من تفرد اسم الصف في نفس السنة الدراسية
            if self.class_name_exists(class_data['class_name'], class_data['academic_year']):
                raise ValueError("اسم الصف موجود مسبقاً في نفس السنة الدراسية")

            # إضافة الحالة الافتراضية
            if 'is_active' not in class_data:
                class_data['is_active'] = True

            # إضافة السعة الافتراضية
            if 'capacity' not in class_data:
                class_data['capacity'] = 30

            # إضافة تاريخ الإنشاء
            from datetime import datetime
            class_data['created_at'] = datetime.now().isoformat()
            class_data['updated_at'] = datetime.now().isoformat()

            return self.insert(class_data)
        except Exception as e:
            raise Exception(f"خطأ في إضافة الصف: {str(e)}")
    
    def update_class(self, class_id, class_data):
        """تحديث بيانات صف دراسي"""
        # التحقق من وجود الصف
        existing_class = self.get_by_id(class_id)
        if not existing_class:
            raise ValueError("الصف الدراسي غير موجود")
        
        # التحقق من تفرد اسم الصف
        if 'class_name' in class_data and 'academic_year' in class_data:
            self.validate_unique_class_name(
                class_data['class_name'], 
                class_data['academic_year'],
                exclude_id=class_id
            )
        
        return self.update(class_id, class_data)
    
    def get_active_classes(self):
        """جلب الصفوف النشطة فقط"""
        return self.get_all("is_active = 1")
    
    def get_classes_by_academic_year(self, academic_year):
        """جلب الصفوف حسب السنة الدراسية"""
        return self.get_all("academic_year = ? AND is_active = 1", (academic_year,))
    
    def get_classes_by_grade_level(self, grade_level):
        """جلب الصفوف حسب المرحلة الدراسية"""
        return self.get_all("grade_level = ? AND is_active = 1", (grade_level,))
    
    def search_classes(self, search_term):
        """البحث في الصفوف الدراسية"""
        return self.search(search_term, self.search_fields)
    
    def get_class_with_student_count(self):
        """جلب الصفوف مع عدد الطلاب في كل صف"""
        query = """
        SELECT c.*, 
               COUNT(s.student_id) as student_count
        FROM classes c
        LEFT JOIN students s ON c.class_id = s.class_id AND s.status = 'active'
        WHERE c.is_active = 1
        GROUP BY c.class_id
        ORDER BY c.grade_level, c.class_name
        """
        return self.db_manager.fetch_all(query)
    
    def get_class_students(self, class_id):
        """جلب طلاب صف معين"""
        query = """
        SELECT s.*
        FROM students s
        WHERE s.class_id = ? AND s.status = 'active'
        ORDER BY s.first_name, s.last_name
        """
        return self.db_manager.fetch_all(query, (class_id,))
    
    def deactivate_class(self, class_id):
        """إلغاء تفعيل صف"""
        return self.update_class(class_id, {'is_active': False})
    
    def activate_class(self, class_id):
        """تفعيل صف"""
        return self.update_class(class_id, {'is_active': True})
    
    def get_class_statistics(self):
        """إحصائيات الصفوف الدراسية"""
        stats = {}
        
        # إجمالي الصفوف النشطة
        stats['total_active'] = self.count("is_active = 1")
        
        # إجمالي الصفوف غير النشطة
        stats['total_inactive'] = self.count("is_active = 0")
        
        # الصفوف حسب المرحلة الدراسية
        grades_query = """
        SELECT grade_level, COUNT(*) as count 
        FROM classes 
        WHERE is_active = 1 
        GROUP BY grade_level
        """
        grades = self.db_manager.fetch_all(grades_query)
        stats['by_grade'] = {grade['grade_level']: grade['count'] for grade in grades}
        
        return stats
    
    def validate_unique_class_name(self, class_name, academic_year, exclude_id=None):
        """التحقق من تفرد اسم الصف في نفس السنة الدراسية"""
        where_clause = "class_name = ? AND academic_year = ?"
        params = [class_name, academic_year]
        
        if exclude_id:
            where_clause += f" AND {self.primary_key} != ?"
            params.append(exclude_id)
        
        if self.exists(where_clause, params):
            raise ValueError(f"اسم الصف '{class_name}' موجود مسبقاً في السنة الدراسية '{academic_year}'")
        
        return True

    def get_all_classes(self):
        """الحصول على جميع الصفوف"""
        return self.get_all()

    def get_classes_statistics(self):
        """الحصول على إحصائيات الصفوف"""
        try:
            stats = {}

            # إجمالي الصفوف
            stats['total_classes'] = self.count()

            # الصفوف النشطة
            stats['active_classes'] = self.count("is_active = 1")

            # الصفوف حسب المرحلة
            grades_query = """
            SELECT grade_level, COUNT(*) as count
            FROM classes
            WHERE is_active = 1
            GROUP BY grade_level
            """
            grades_result = self.db_manager.fetch_all(grades_query)
            stats['by_grade'] = {row['grade_level']: row['count'] for row in grades_result}

            # الصفوف حسب السنة الدراسية
            years_query = """
            SELECT academic_year, COUNT(*) as count
            FROM classes
            WHERE is_active = 1
            GROUP BY academic_year
            """
            years_result = self.db_manager.fetch_all(years_query)
            stats['by_year'] = {row['academic_year']: row['count'] for row in years_result}

            return stats
        except Exception as e:
            raise Exception(f"خطأ في جلب إحصائيات الصفوف: {str(e)}")

    def search_classes(self, search_term):
        """البحث في الصفوف"""
        try:
            where_clause = """
            class_name LIKE ? OR grade_level LIKE ? OR academic_year LIKE ?
            OR description LIKE ?
            """
            search_param = f"%{search_term}%"
            params = (search_param, search_param, search_param, search_param)
            return self.get_all(where_clause, params)
        except Exception as e:
            raise Exception(f"خطأ في البحث: {str(e)}")

    def get_active_classes(self):
        """الحصول على الصفوف النشطة"""
        try:
            return self.get_all("is_active = 1")
        except Exception as e:
            raise Exception(f"خطأ في جلب الصفوف النشطة: {str(e)}")

    def get_classes_by_grade(self, grade_level):
        """الحصول على الصفوف حسب المرحلة الدراسية"""
        try:
            return self.get_all("grade_level = ? AND is_active = 1", (grade_level,))
        except Exception as e:
            raise Exception(f"خطأ في جلب صفوف المرحلة: {str(e)}")

    def activate_class(self, class_id):
        """تفعيل الصف"""
        try:
            return self.update(class_id, {'is_active': True})
        except Exception as e:
            raise Exception(f"خطأ في تفعيل الصف: {str(e)}")

    def deactivate_class(self, class_id):
        """إلغاء تفعيل الصف"""
        try:
            return self.update(class_id, {'is_active': False})
        except Exception as e:
            raise Exception(f"خطأ في إلغاء تفعيل الصف: {str(e)}")

    def class_name_exists(self, class_name, academic_year, exclude_class_id=None):
        """التحقق من وجود اسم الصف في نفس السنة الدراسية"""
        try:
            if exclude_class_id:
                where_clause = "class_name = ? AND academic_year = ? AND class_id != ?"
                params = (class_name, academic_year, exclude_class_id)
            else:
                where_clause = "class_name = ? AND academic_year = ?"
                params = (class_name, academic_year)

            return self.exists(where_clause, params)
        except Exception:
            return False
