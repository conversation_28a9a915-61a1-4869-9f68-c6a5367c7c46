# ميزة تخصيص الخط في تطبيق إدارة المدرسة

## نظرة عامة
تم إضافة ميزة شاملة لتخصيص نوع الخط وحجم الخط في تبويب الإعدادات العامة، مع تطبيق التغييرات على جميع شاشات البرنامج فوراً وحفظها للجلسات المستقبلية.

## الميزات المطبقة

### 1. **اختيار نوع الخط** 🔤
- **قائمة شاملة**: عرض جميع الخطوط المتاحة في النظام
- **معاينة مباشرة**: رؤية شكل الخط قبل التطبيق
- **دعم الخطوط العربية**: دعم كامل للخطوط العربية والإنجليزية

### 2. **تحديد حجم الخط** 📏
- **نطاق مرن**: من 8 إلى 32 نقطة
- **تحديث فوري**: تغيير الحجم مع معاينة مباشرة
- **وحدة القياس**: عرض الحجم بالنقاط (pt)

### 3. **معاينة مباشرة** 👁️
- **نص تجريبي**: عرض نص باللغتين العربية والإنجليزية
- **تحديث فوري**: تغيير المعاينة مع كل تعديل
- **معلومات الخط**: عرض اسم الخط والحجم

### 4. **تطبيق فوري** ⚡
- **زر التطبيق**: تطبيق الخط على جميع الشاشات فوراً
- **حفظ تلقائي**: حفظ الإعدادات عند الضغط على "حفظ الإعدادات"
- **تحميل تلقائي**: تطبيق الخط المحفوظ عند بدء التطبيق

## التطبيق التقني

### 1. **تحديث شاشة الإعدادات (`src/ui/widgets/settings_widget.py`)**

#### إضافة الاستيرادات المطلوبة:
```python
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QLineEdit, QPushButton, QLabel, QFrame, QMessageBox,
                             QComboBox, QCheckBox, QSpinBox, QTabWidget,
                             QGroupBox, QTextEdit, QFileDialog, QScrollArea,
                             QFontComboBox, QApplication)  # ✅ إضافة جديدة
from PyQt5.QtCore import Qt, QSettings, pyqtSignal  # ✅ إضافة pyqtSignal
from PyQt5.QtGui import QFont
```

#### إضافة إشارة تحديث الخط:
```python
class SettingsWidget(QWidget):
    """ويدجت إعدادات النظام"""
    
    # إشارة تحديث الخط
    font_changed = pyqtSignal(QFont)  # ✅ إضافة جديدة
```

#### إضافة عناصر واجهة الخط:
```python
# نوع الخط
self.font_family_combo = QFontComboBox()
self.font_family_combo.setCurrentFont(QFont("Arial"))
appearance_layout.addRow("نوع الخط:", self.font_family_combo)

# حجم الخط (محسن)
self.font_size_spin = QSpinBox()
self.font_size_spin.setRange(8, 32)  # ✅ نطاق أوسع
self.font_size_spin.setValue(12)
self.font_size_spin.setSuffix(" نقطة")
appearance_layout.addRow("حجم الخط:", self.font_size_spin)

# معاينة الخط
self.font_preview_label = QLabel("معاينة الخط - Sample Text - نص تجريبي")
self.font_preview_label.setStyleSheet("""
    QLabel {
        border: 2px solid #bdc3c7;
        border-radius: 5px;
        padding: 10px;
        background-color: white;
        min-height: 40px;
    }
""")
appearance_layout.addRow("معاينة:", self.font_preview_label)

# زر تطبيق الخط فوراً
self.apply_font_button = QPushButton("تطبيق الخط الآن")
self.apply_font_button.setStyleSheet("""
    QPushButton {
        background-color: #3498db;
        color: white;
        border: none;
        border-radius: 5px;
        padding: 8px 15px;
        font-weight: bold;
    }
    QPushButton:hover {
        background-color: #2980b9;
    }
""")
self.apply_font_button.clicked.connect(self.apply_font_to_application)
appearance_layout.addRow("", self.apply_font_button)
```

#### دالة تحديث معاينة الخط:
```python
def update_font_preview(self):
    """تحديث معاينة الخط"""
    try:
        font_family = self.font_family_combo.currentFont().family()
        font_size = self.font_size_spin.value()
        
        # إنشاء خط جديد
        font = QFont(font_family, font_size)
        
        # تطبيق الخط على معاينة
        self.font_preview_label.setFont(font)
        
        # تحديث النص ليشمل معلومات الخط
        self.font_preview_label.setText(
            f"معاينة الخط - {font_family} - {font_size}pt\n"
            f"Sample Text - نص تجريبي - 1234567890"
        )
        
    except Exception as e:
        print(f"خطأ في تحديث معاينة الخط: {e}")
```

#### دالة تطبيق الخط على التطبيق:
```python
def apply_font_to_application(self):
    """تطبيق الخط على التطبيق بالكامل"""
    try:
        font_family = self.font_family_combo.currentFont().family()
        font_size = self.font_size_spin.value()
        
        # إنشاء خط جديد
        font = QFont(font_family, font_size)
        
        # تطبيق الخط على التطبيق بالكامل
        QApplication.instance().setFont(font)
        
        # إرسال إشارة تحديث الخط
        self.font_changed.emit(font)
        
        # حفظ إعدادات الخط
        self.settings.setValue("font_family", font_family)
        self.settings.setValue("font_size", font_size)
        
        print(f"تم تطبيق الخط: {font_family} - {font_size}pt")
        
    except Exception as e:
        print(f"خطأ في تطبيق الخط: {e}")
```

#### تحديث دالة تحميل الإعدادات:
```python
def load_settings(self):
    """تحميل الإعدادات المحفوظة"""
    try:
        # ... كود سابق ...
        
        # تحميل إعدادات الخط
        font_family = self.settings.value("font_family", "Arial")
        font_size = int(self.settings.value("font_size", 12))
        
        # تعيين نوع الخط
        font = QFont(font_family)
        self.font_family_combo.setCurrentFont(font)
        
        # تعيين حجم الخط
        self.font_size_spin.setValue(font_size)
        
        # تحديث معاينة الخط
        self.update_font_preview()
        
        # تطبيق الخط على التطبيق إذا كان محفوظ
        if self.settings.value("apply_font_on_startup", True, type=bool):
            self.apply_font_to_application()
```

#### تحديث دالة حفظ الإعدادات:
```python
def save_settings(self):
    """حفظ الإعدادات"""
    try:
        # ... كود سابق ...
        
        # حفظ إعدادات الخط
        self.settings.setValue("font_family", self.font_family_combo.currentFont().family())
        self.settings.setValue("font_size", self.font_size_spin.value())
        self.settings.setValue("apply_font_on_startup", True)
        
        # تطبيق الخط فوراً
        self.apply_font_to_application()
```

### 2. **تحديث النافذة الرئيسية (`src/ui/main_window.py`)**

#### ربط إشارة تحديث الخط:
```python
def create_content_widgets(self):
    # ... إنشاء الويدجتات ...
    
    # ربط إشارة تحديث الخط
    self.settings_widget.font_changed.connect(self.on_font_changed)
```

#### دالة معالجة تحديث الخط:
```python
def on_font_changed(self, font):
    """معالجة تحديث الخط"""
    try:
        # تطبيق الخط على النافذة الرئيسية
        self.setFont(font)
        
        # تطبيق الخط على جميع الويدجتات
        widgets = [
            self.dashboard_widget,
            self.students_widget,
            self.teachers_widget,
            self.subjects_widget,
            self.classes_widget,
            self.fees_widget,
            self.results_widget,
            self.reports_widget,
            self.currency_widget,
            self.users_widget,
            self.settings_widget
        ]
        
        for widget in widgets:
            if widget:
                self.apply_font_to_widget(widget, font)
        
        # تطبيق الخط على شريط القوائم والأدوات
        if hasattr(self, 'menuBar'):
            self.menuBar().setFont(font)
        
        if hasattr(self, 'toolbar'):
            self.toolbar.setFont(font)
        
        print(f"تم تطبيق الخط على جميع شاشات البرنامج: {font.family()} - {font.pointSize()}pt")
        
    except Exception as e:
        print(f"خطأ في تطبيق الخط: {e}")
```

#### دالة تطبيق الخط على ويدجت:
```python
def apply_font_to_widget(self, widget, font):
    """تطبيق الخط على ويدجت وجميع عناصره الفرعية"""
    try:
        widget.setFont(font)
        
        # تطبيق الخط على جميع العناصر الفرعية
        for child in widget.findChildren(QWidget):
            child.setFont(font)
            
    except Exception as e:
        print(f"خطأ في تطبيق الخط على الويدجت: {e}")
```

#### دالة تحميل الخط المحفوظ:
```python
def load_saved_font(self):
    """تحميل الخط المحفوظ عند بدء التطبيق"""
    try:
        from PyQt5.QtCore import QSettings
        settings = QSettings("SchoolManagement", "Settings")
        
        # تحميل إعدادات الخط
        font_family = settings.value("font_family", "Arial")
        font_size = int(settings.value("font_size", 12))
        apply_on_startup = settings.value("apply_font_on_startup", True, type=bool)
        
        if apply_on_startup:
            # إنشاء وتطبيق الخط
            font = QFont(font_family, font_size)
            QApplication.instance().setFont(font)
            self.on_font_changed(font)
            print(f"تم تحميل الخط المحفوظ: {font_family} - {font_size}pt")
        
    except Exception as e:
        print(f"خطأ في تحميل الخط المحفوظ: {e}")
```

## آلية العمل

### 1. **عند فتح تبويب الإعدادات العامة**:
1. تحميل إعدادات الخط المحفوظة
2. عرض نوع الخط وحجم الخط الحاليين
3. تحديث معاينة الخط
4. عرض زر "تطبيق الخط الآن"

### 2. **عند تغيير نوع أو حجم الخط**:
1. تحديث معاينة الخط فوراً
2. عرض اسم الخط والحجم في المعاينة
3. إظهار النص التجريبي بالخط الجديد

### 3. **عند الضغط على "تطبيق الخط الآن"**:
1. إنشاء كائن خط جديد
2. تطبيق الخط على التطبيق بالكامل
3. إرسال إشارة لتحديث جميع الشاشات
4. حفظ إعدادات الخط
5. طباعة رسالة تأكيد

### 4. **عند الضغط على "حفظ الإعدادات"**:
1. حفظ جميع الإعدادات بما في ذلك الخط
2. تطبيق الخط على التطبيق
3. تعيين تطبيق الخط عند بدء التطبيق

### 5. **عند بدء التطبيق**:
1. تحميل إعدادات الخط المحفوظة
2. تطبيق الخط على التطبيق إذا كان مفعل
3. تحديث جميع الشاشات بالخط المحفوظ

## الفوائد المحققة

### 1. **تخصيص شامل** 🎨:
- ✅ **اختيار من جميع الخطوط**: دعم جميع خطوط النظام
- ✅ **نطاق حجم واسع**: من 8 إلى 32 نقطة
- ✅ **معاينة مباشرة**: رؤية التغيير قبل التطبيق

### 2. **سهولة الاستخدام** 🎯:
- ✅ **واجهة بديهية**: عناصر واضحة وسهلة الاستخدام
- ✅ **تطبيق فوري**: زر لتطبيق التغييرات فوراً
- ✅ **حفظ تلقائي**: حفظ الإعدادات للجلسات المستقبلية

### 3. **تطبيق شامل** 🌐:
- ✅ **جميع الشاشات**: تطبيق الخط على كل شاشة في البرنامج
- ✅ **العناصر الفرعية**: تطبيق الخط على جميع العناصر
- ✅ **شريط القوائم**: تطبيق الخط على القوائم والأدوات

### 4. **الاستمرارية** 💾:
- ✅ **حفظ الإعدادات**: حفظ الخط المختار
- ✅ **تحميل تلقائي**: تطبيق الخط عند بدء التطبيق
- ✅ **استقرار الإعدادات**: الحفاظ على الخط عبر الجلسات

## الملفات المحدثة

### الملفات المعدلة:
1. **`src/ui/widgets/settings_widget.py`**:
   - إضافة QFontComboBox لاختيار نوع الخط
   - تحسين QSpinBox لحجم الخط (نطاق 8-32)
   - إضافة معاينة الخط مع تحديث فوري
   - إضافة زر "تطبيق الخط الآن"
   - دوال تحديث وتطبيق الخط
   - تحديث دوال تحميل وحفظ الإعدادات

2. **`src/ui/main_window.py`**:
   - ربط إشارة تحديث الخط
   - دالة معالجة تحديث الخط
   - دالة تطبيق الخط على الويدجتات
   - دالة تحميل الخط المحفوظ عند البدء

## اختبار الميزة

### خطوات الاختبار:
1. **تشغيل التطبيق** ✅
2. **فتح تبويب الإعدادات العامة**:
   - التحقق من وجود قائمة نوع الخط ✅
   - التحقق من وجود حقل حجم الخط ✅
   - التحقق من وجود معاينة الخط ✅
   - التحقق من وجود زر "تطبيق الخط الآن" ✅
3. **اختبار تغيير نوع الخط**:
   - اختيار خط مختلف ✅
   - التحقق من تحديث المعاينة ✅
4. **اختبار تغيير حجم الخط**:
   - تغيير الحجم ✅
   - التحقق من تحديث المعاينة ✅
5. **اختبار التطبيق الفوري**:
   - الضغط على "تطبيق الخط الآن" ✅
   - التحقق من تطبيق الخط على جميع الشاشات ✅
6. **اختبار الحفظ**:
   - الضغط على "حفظ الإعدادات" ✅
   - إعادة تشغيل التطبيق ✅
   - التحقق من تطبيق الخط المحفوظ ✅

### النتائج:
- ✅ **جميع العناصر تعمل بشكل صحيح**
- ✅ **المعاينة تتحدث فوراً**
- ✅ **التطبيق يشمل جميع الشاشات**
- ✅ **الحفظ والتحميل يعملان بشكل مثالي**

## النتيجة النهائية

**تم إضافة ميزة تخصيص الخط بنجاح مع تطبيق شامل على جميع شاشات البرنامج!**

- ✅ **اختيار نوع الخط**: قائمة شاملة بجميع خطوط النظام
- ✅ **تحديد حجم الخط**: نطاق مرن من 8 إلى 32 نقطة
- ✅ **معاينة مباشرة**: رؤية التغيير قبل التطبيق
- ✅ **تطبيق فوري**: زر لتطبيق التغييرات على جميع الشاشات
- ✅ **حفظ تلقائي**: حفظ الإعدادات للجلسات المستقبلية
- ✅ **تحميل تلقائي**: تطبيق الخط المحفوظ عند بدء التطبيق

الآن يمكن للمستخدمين:

- 🔤 **اختيار أي خط متاح** في النظام
- 📏 **تحديد حجم الخط المناسب** لاحتياجاتهم
- 👁️ **معاينة الخط** قبل التطبيق
- ⚡ **تطبيق التغييرات فوراً** على جميع الشاشات
- 💾 **حفظ الإعدادات** للاستخدام المستقبلي
- 🎨 **تخصيص مظهر البرنامج** حسب تفضيلاتهم

**مثال على الاستخدام:**
- اختيار خط "Tahoma" بحجم 14 نقطة
- معاينة النص: "معاينة الخط - Tahoma - 14pt"
- تطبيق فوري على جميع شاشات البرنامج
- حفظ تلقائي للإعدادات

🎉🔤✨🚀
