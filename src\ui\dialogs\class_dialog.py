#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة حوار إضافة وتعديل الصفوف الدراسية
"""

from PyQt5.QtWidgets import (QDialog, Q<PERSON>oxLayout, QHBoxLayout, QFormLayout,
                             QLineEdit, QComboBox, QSpinBox, QTextEdit,
                             QPushButton, QLabel, QMessageBox, QGroupBox,
                             QCheckBox, QFrame, QTabWidget, QWidget)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

from src.models.class_model import ClassModel
from src.models.teacher import Teacher
from src.utils.ui_styles import UIStyles


class ClassDialog(QDialog):
    """نافذة حوار إضافة وتعديل الصفوف الدراسية"""
    
    class_saved = pyqtSignal()  # إشارة عند حفظ الصف
    
    def __init__(self, class_data=None, parent=None):
        super().__init__(parent)
        self.class_data = class_data
        self.class_model = ClassModel()
        self.teacher_model = Teacher()
        self.is_edit_mode = class_data is not None
        
        self.setup_ui()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.load_class_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة
        title = "تعديل الصف" if self.is_edit_mode else "إضافة صف جديد"
        self.setWindowTitle(title)
        self.setFixedSize(600, 500)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

        # أنماط النافذة
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Tahoma, Arial;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #495057;
            }
            QLineEdit, QComboBox, QSpinBox, QTextEdit {
                border: 2px solid #dee2e6;
                border-radius: 5px;
                padding: 8px;
                font-size: 11px;
                background-color: white;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QTextEdit:focus {
                border-color: #007bff;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
        """)

        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # عنوان النافذة
        title = "تعديل بيانات الصف" if self.is_edit_mode else "إضافة صف دراسي جديد"
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #212529;
                padding: 15px;
                background-color: #e9ecef;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # مجموعة البيانات الأساسية
        basic_group = QGroupBox("البيانات الأساسية")
        basic_layout = QFormLayout(basic_group)
        basic_layout.setSpacing(10)
        
        # اسم الصف
        self.class_name_input = QLineEdit()
        self.class_name_input.setPlaceholderText("مثال: الصف الأول أ")
        basic_layout.addRow("اسم الصف *:", self.class_name_input)
        
        # المرحلة الدراسية
        self.grade_level_combo = QComboBox()
        grades = ['الأول', 'الثاني', 'الثالث', 'الرابع', 'الخامس', 'السادس',
                 'السابع', 'الثامن', 'التاسع', 'العاشر', 'الحادي عشر', 'الثاني عشر']
        self.grade_level_combo.addItems(grades)
        basic_layout.addRow("المرحلة الدراسية *:", self.grade_level_combo)
        
        # السنة الدراسية
        self.academic_year_combo = QComboBox()
        self.academic_year_combo.setEditable(True)
        current_year = 2024
        for i in range(5):  # آخر 5 سنوات والسنوات القادمة
            year = f"{current_year - 2 + i}-{current_year - 1 + i}"
            self.academic_year_combo.addItem(year)
        self.academic_year_combo.setCurrentText("2024-2025")
        basic_layout.addRow("السنة الدراسية *:", self.academic_year_combo)
        
        layout.addWidget(basic_group)
        
        # مجموعة الإعدادات
        settings_group = QGroupBox("إعدادات الصف")
        settings_layout = QFormLayout(settings_group)
        settings_layout.setSpacing(10)
        
        # السعة القصوى
        self.capacity_spin = QSpinBox()
        self.capacity_spin.setRange(1, 50)
        self.capacity_spin.setValue(30)
        self.capacity_spin.setSuffix(" طالب")
        settings_layout.addRow("السعة القصوى:", self.capacity_spin)
        
        # المعلم المسؤول
        self.teacher_combo = QComboBox()
        self.teacher_combo.addItem("لا يوجد", None)
        self.load_teachers()
        settings_layout.addRow("المعلم المسؤول:", self.teacher_combo)
        
        # حالة الصف
        self.is_active_checkbox = QCheckBox("الصف نشط")
        self.is_active_checkbox.setChecked(True)
        settings_layout.addRow("الحالة:", self.is_active_checkbox)
        
        layout.addWidget(settings_group)
        
        # مجموعة الوصف
        description_group = QGroupBox("وصف إضافي")
        description_layout = QVBoxLayout(description_group)
        
        self.description_input = QTextEdit()
        self.description_input.setPlaceholderText("وصف اختياري للصف...")
        self.description_input.setMaximumHeight(80)
        description_layout.addWidget(self.description_input)
        
        layout.addWidget(description_group)
        
        # أزرار الحفظ والإلغاء
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(0, 10, 0, 0)
        
        self.save_button = QPushButton("حفظ التغييرات" if self.is_edit_mode else "إضافة الصف")
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                font-size: 12px;
                padding: 12px 25px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                font-size: 12px;
                padding: 12px 25px;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.save_button)
        buttons_layout.addWidget(self.cancel_button)
        
        layout.addWidget(buttons_frame)
        
    def setup_connections(self):
        """ربط الأحداث"""
        self.save_button.clicked.connect(self.save_class)
        self.cancel_button.clicked.connect(self.reject)
        
    def load_teachers(self):
        """تحميل قائمة المعلمين"""
        try:
            teachers = self.teacher_model.get_active_teachers()
            for teacher in teachers:
                teacher_name = f"{teacher['first_name']} {teacher['last_name']}"
                self.teacher_combo.addItem(teacher_name, teacher['teacher_id'])
        except Exception as e:
            print(f"خطأ في تحميل المعلمين: {e}")
            
    def load_class_data(self):
        """تحميل بيانات الصف للتعديل"""
        if not self.class_data:
            return
            
        try:
            self.class_name_input.setText(self.class_data.get('class_name', ''))
            
            # تحديد المرحلة الدراسية
            grade_level = self.class_data.get('grade_level', '')
            index = self.grade_level_combo.findText(grade_level)
            if index >= 0:
                self.grade_level_combo.setCurrentIndex(index)
                
            # تحديد السنة الدراسية
            academic_year = self.class_data.get('academic_year', '')
            self.academic_year_combo.setCurrentText(academic_year)
            
            # السعة القصوى
            capacity = self.class_data.get('capacity', 30)
            self.capacity_spin.setValue(capacity)
            
            # المعلم المسؤول
            teacher_id = self.class_data.get('teacher_id')
            if teacher_id:
                for i in range(self.teacher_combo.count()):
                    if self.teacher_combo.itemData(i) == teacher_id:
                        self.teacher_combo.setCurrentIndex(i)
                        break
            
            # حالة الصف
            is_active = self.class_data.get('is_active', True)
            self.is_active_checkbox.setChecked(is_active)
            
            # الوصف
            description = self.class_data.get('description', '')
            self.description_input.setPlainText(description)
            
        except Exception as e:
            QMessageBox.warning(self, "تحذير", f"خطأ في تحميل بيانات الصف: {str(e)}")
            
    def validate_input(self):
        """التحقق من صحة البيانات المدخلة"""
        # التحقق من اسم الصف
        class_name = self.class_name_input.text().strip()
        if not class_name:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الصف")
            self.class_name_input.setFocus()
            return False
            
        # التحقق من المرحلة الدراسية
        if self.grade_level_combo.currentIndex() < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار المرحلة الدراسية")
            self.grade_level_combo.setFocus()
            return False
            
        # التحقق من السنة الدراسية
        academic_year = self.academic_year_combo.currentText().strip()
        if not academic_year:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال السنة الدراسية")
            self.academic_year_combo.setFocus()
            return False
            
        return True
        
    def save_class(self):
        """حفظ بيانات الصف"""
        if not self.validate_input():
            return
            
        try:
            # جمع البيانات
            class_data = {
                'class_name': self.class_name_input.text().strip(),
                'grade_level': self.grade_level_combo.currentText(),
                'academic_year': self.academic_year_combo.currentText().strip(),
                'capacity': self.capacity_spin.value(),
                'description': self.description_input.toPlainText().strip(),
                'is_active': self.is_active_checkbox.isChecked()
            }
            
            # المعلم المسؤول
            teacher_id = self.teacher_combo.currentData()
            if teacher_id:
                class_data['teacher_id'] = teacher_id
                class_data['teacher_name'] = self.teacher_combo.currentText()
            
            # حفظ البيانات
            if self.is_edit_mode:
                class_id = self.class_data['class_id']
                success = self.class_model.update_class(class_id, class_data)
                message = "تم تحديث بيانات الصف بنجاح"
            else:
                success = self.class_model.add_class(class_data)
                message = "تم إضافة الصف بنجاح"
                
            if success:
                QMessageBox.information(self, "نجح", message)
                self.class_saved.emit()
                self.accept()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في حفظ بيانات الصف")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ البيانات: {str(e)}")

    def showEvent(self, event):
        """عند إظهار النافذة"""
        super().showEvent(event)
        # رفع النافذة للمقدمة
        self.raise_()
        self.activateWindow()

    def exec_(self):
        """تنفيذ النافذة مع ضمان الظهور الصحيح"""
        # رفع النافذة للمقدمة
        self.raise_()
        self.activateWindow()
        return super().exec_()
