# دليل المستخدم - برنامج إدارة المدارس

## مقدمة
مرحباً بك في برنامج إدارة المدارس، وهو نظام شامل لإدارة المؤسسات التعليمية مطور باستخدام Python و PyQt5 مع دعم كامل للغة العربية.

## متطلبات النظام
- نظام التشغيل: Windows 10/11, macOS 10.14+, أو Linux Ubuntu 18.04+
- Python 3.8 أو أحدث
- ذاكرة وصول عشوائي: 4 جيجابايت على الأقل
- مساحة تخزين: 500 ميجابايت

## التثبيت والإعداد

### 1. تحميل البرنامج
قم بتحميل ملفات البرنامج من المصدر المحدد أو استنساخ المستودع.

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. إنشاء البيانات الوهمية (اختياري)
لإنشاء بيانات وهمية للاختبار:
```bash
python generate_sample_data.py
```

### 4. تشغيل البرنامج
```bash
python run.py
```

## تسجيل الدخول

### بيانات الدخول الافتراضية
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

### تغيير كلمة المرور
1. بعد تسجيل الدخول، انتقل إلى الإعدادات
2. اختر "إدارة المستخدمين"
3. حدد المستخدم واختر "تغيير كلمة المرور"

## الواجهة الرئيسية

### القائمة الجانبية
تحتوي على الوحدات التالية:
- **الرئيسية:** لوحة المعلومات والإحصائيات
- **الطلاب:** إدارة بيانات الطلاب
- **المعلمين:** إدارة بيانات المعلمين والموظفين
- **المواد الدراسية:** إدارة المناهج والمواد
- **الصفوف والفصول:** تنظيم الصفوف الدراسية
- **الرسوم الدراسية:** إدارة المدفوعات والرسوم
- **النتائج:** إدخال ومتابعة درجات الطلاب
- **التقارير:** إنشاء وتصدير التقارير
- **الإعدادات:** إعدادات النظام العامة

## إدارة الطلاب

### إضافة طالب جديد
1. انتقل إلى وحدة "الطلاب"
2. اضغط على زر "إضافة طالب"
3. املأ البيانات المطلوبة في التبويبات الثلاثة:
   - **البيانات الأساسية:** الاسم، تاريخ الميلاد، الجنس، إلخ
   - **بيانات ولي الأمر:** اسم ولي الأمر ومعلومات التواصل
   - **معلومات إضافية:** تاريخ التسجيل والملاحظات
4. اضغط "حفظ"

### تعديل بيانات طالب
1. حدد الطالب من الجدول
2. اضغط على زر "تعديل"
3. قم بتعديل البيانات المطلوبة
4. اضغط "حفظ"

### البحث عن الطلاب
استخدم حقل البحث في أعلى الصفحة للبحث بـ:
- رقم الطالب
- الاسم الأول أو الأخير
- رقم الهوية
- اسم ولي الأمر
- رقم الهاتف

### حذف طالب
1. حدد الطالب من الجدول
2. اضغط على زر "حذف"
3. أكد عملية الحذف

**ملاحظة:** الحذف هو "حذف ناعم" - يتم تعطيل الطالب وليس حذفه نهائياً.

## إدارة المعلمين
(قيد التطوير - سيتم إضافة المزيد من التفاصيل)

## إدارة المواد الدراسية
(قيد التطوير - سيتم إضافة المزيد من التفاصيل)

## إدارة الصفوف والفصول
(قيد التطوير - سيتم إضافة المزيد من التفاصيل)

## إدارة الرسوم الدراسية
(قيد التطوير - سيتم إضافة المزيد من التفاصيل)

## إدارة النتائج
(قيد التطوير - سيتم إضافة المزيد من التفاصيل)

## التقارير
(قيد التطوير - سيتم إضافة المزيد من التفاصيل)

## الإعدادات
(قيد التطوير - سيتم إضافة المزيد من التفاصيل)

## النسخ الاحتياطي
يُنصح بإنشاء نسخ احتياطية دورية من قاعدة البيانات:
1. انتقل إلى الإعدادات
2. اختر "النسخ الاحتياطي"
3. اضغط "إنشاء نسخة احتياطية"

## استكشاف الأخطاء وإصلاحها

### مشاكل شائعة

#### البرنامج لا يبدأ
- تأكد من تثبيت Python 3.8+
- تأكد من تثبيت جميع المتطلبات: `pip install -r requirements.txt`
- تحقق من وجود أخطاء في وحدة التحكم

#### مشاكل في قاعدة البيانات
- تأكد من وجود مجلد `db` في مجلد المشروع
- تحقق من صلاحيات الكتابة في مجلد المشروع

#### مشاكل في الخطوط العربية
- تأكد من وجود خطوط عربية مثبتة على النظام
- في Windows: تأكد من تثبيت "Tahoma" أو "Arial Unicode MS"

## الدعم الفني
للحصول على المساعدة:
1. راجع هذا الدليل أولاً
2. تحقق من ملف README.md
3. ابحث في قسم المشاكل المعروفة

## معلومات إضافية

### اختصارات لوحة المفاتيح
- **Ctrl+N:** إضافة عنصر جديد (حسب الوحدة النشطة)
- **Ctrl+E:** تعديل العنصر المحدد
- **Delete:** حذف العنصر المحدد
- **Ctrl+F:** التركيز على حقل البحث
- **F5:** تحديث البيانات

### نصائح للاستخدام الأمثل
1. قم بإنشاء نسخ احتياطية دورية
2. استخدم أرقام طلاب فريدة ومنطقية
3. تأكد من صحة بيانات التواصل
4. راجع التقارير بانتظام للتأكد من دقة البيانات

---

**إصدار الدليل:** 1.0.0  
**تاريخ آخر تحديث:** 2025-01-29  
**المطور:** Augment Agent
