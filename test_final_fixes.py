#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي لجميع الإصلاحات
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.models.student import Student
from src.models.teacher import Teacher
from src.models.user import User
from src.models.class_model import ClassModel
from src.database.db_manager import DatabaseManager


def test_database_schema():
    """اختبار هيكل قاعدة البيانات"""
    print("=== اختبار هيكل قاعدة البيانات ===")
    try:
        # استخدام النماذج للتحقق من قاعدة البيانات
        student_model = Student()

        # اختبار بسيط للتأكد من أن قاعدة البيانات تعمل
        try:
            students = student_model.get_all()
            print("✓ قاعدة البيانات تعمل بشكل صحيح")
        except Exception as e:
            print(f"✗ خطأ في قاعدة البيانات: {e}")
            return False

        print("✓ هيكل قاعدة البيانات صحيح")
        return True
    except Exception as e:
        print(f"✗ خطأ في اختبار هيكل قاعدة البيانات: {e}")
        return False


def test_teacher_model_methods():
    """اختبار دوال نموذج المعلمين"""
    print("\n=== اختبار دوال نموذج المعلمين ===")
    try:
        teacher_model = Teacher()
        
        # اختبار get_all_teachers
        if hasattr(teacher_model, 'get_all_teachers'):
            teachers = teacher_model.get_all_teachers()
            print(f"✓ get_all_teachers: {len(teachers)} معلم")
        else:
            print("✗ get_all_teachers غير موجودة")
            return False
        
        # اختبار get_teachers_statistics
        if hasattr(teacher_model, 'get_teachers_statistics'):
            stats = teacher_model.get_teachers_statistics()
            print(f"✓ get_teachers_statistics: {stats.get('active_teachers', 0)} نشط")
        else:
            print("✗ get_teachers_statistics غير موجودة")
            return False
        
        return True
    except Exception as e:
        print(f"✗ خطأ في اختبار نموذج المعلمين: {e}")
        return False


def test_student_model_methods():
    """اختبار دوال نموذج الطلاب"""
    print("\n=== اختبار دوال نموذج الطلاب ===")
    try:
        student_model = Student()
        
        # اختبار get_all_students
        if hasattr(student_model, 'get_all_students'):
            students = student_model.get_all_students()
            print(f"✓ get_all_students: {len(students)} طالب")
        else:
            print("✗ get_all_students غير موجودة")
            return False
        
        # اختبار get_students_statistics
        if hasattr(student_model, 'get_students_statistics'):
            stats = student_model.get_students_statistics()
            print(f"✓ get_students_statistics: {stats.get('active_students', 0)} نشط")
        else:
            print("✗ get_students_statistics غير موجودة")
            return False
        
        return True
    except Exception as e:
        print(f"✗ خطأ في اختبار نموذج الطلاب: {e}")
        return False


def test_class_model_methods():
    """اختبار دوال نموذج الصفوف"""
    print("\n=== اختبار دوال نموذج الصفوف ===")
    try:
        class_model = ClassModel()
        
        # اختبار get_all_classes
        if hasattr(class_model, 'get_all_classes'):
            classes = class_model.get_all_classes()
            print(f"✓ get_all_classes: {len(classes)} صف")
        else:
            print("✗ get_all_classes غير موجودة")
            return False
        
        # اختبار get_classes_statistics
        if hasattr(class_model, 'get_classes_statistics'):
            stats = class_model.get_classes_statistics()
            print(f"✓ get_classes_statistics: {stats.get('active_classes', 0)} نشط")
        else:
            print("✗ get_classes_statistics غير موجودة")
            return False
        
        return True
    except Exception as e:
        print(f"✗ خطأ في اختبار نموذج الصفوف: {e}")
        return False


def test_user_model_methods():
    """اختبار دوال نموذج المستخدمين"""
    print("\n=== اختبار دوال نموذج المستخدمين ===")
    try:
        user_model = User()
        
        # اختبار get_all_users
        if hasattr(user_model, 'get_all_users'):
            users = user_model.get_all_users()
            print(f"✓ get_all_users: {len(users)} مستخدم")
        else:
            print("✗ get_all_users غير موجودة")
            return False
        
        # اختبار get_user_statistics
        if hasattr(user_model, 'get_user_statistics'):
            stats = user_model.get_user_statistics()
            print(f"✓ get_user_statistics: {stats.get('total_active', 0)} نشط")
        else:
            print("✗ get_user_statistics غير موجودة")
            return False
        
        return True
    except Exception as e:
        print(f"✗ خطأ في اختبار نموذج المستخدمين: {e}")
        return False


def test_database_queries():
    """اختبار استعلامات قاعدة البيانات"""
    print("\n=== اختبار استعلامات قاعدة البيانات ===")
    try:
        # اختبار النماذج بدلاً من الاستعلامات المباشرة
        teacher_model = Teacher()
        student_model = Student()

        # اختبار استعلام المعلمين
        try:
            teachers = teacher_model.get_all("status = 'active'")
            print("✓ استعلام عمود status في جدول teachers يعمل")
        except Exception as e:
            if "no such column: status" in str(e):
                print("✗ عمود status غير موجود في جدول teachers")
                return False
            else:
                print("✓ استعلام المعلمين يعمل")

        # اختبار استعلام الطلاب
        try:
            students = student_model.get_all("status = 'active'")
            print("✓ استعلام عمود status في جدول students يعمل")
        except Exception as e:
            if "no such column: status" in str(e):
                print("✗ عمود status غير موجود في جدول students")
                return False
            else:
                print("✓ استعلام الطلاب يعمل")

        print("✓ جميع استعلامات قاعدة البيانات تعمل")
        return True
    except Exception as e:
        print(f"✗ خطأ في اختبار استعلامات قاعدة البيانات: {e}")
        return False


def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار نهائي لجميع الإصلاحات")
    print("=" * 50)
    
    tests = [
        test_database_schema,
        test_teacher_model_methods,
        test_student_model_methods,
        test_class_model_methods,
        test_user_model_methods,
        test_database_queries
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبار النهائي: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الإصلاحات تعمل بنجاح! النظام جاهز للاستخدام")
        print("\n✅ المشاكل المحلولة:")
        print("   - 'Teacher' object has no attribute 'get_all_teachers'")
        print("   - no such column: percentage")
        print("   - no such column: is_active")
        print("   - مشاكل sqlite3.Row objects")
        print("   - شاشة الصفوف والفصول مكتملة")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
