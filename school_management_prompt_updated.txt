# برومبت لإنشاء برنامج إدارة المدارس باستخدام Python وPyQt/PySide

## نظرة عامة
إنشاء تطبيق سطح مكتب باستخدام Python ومكتبة PyQt أو PySide لإدارة المدارس والمؤسسات التعليمية. التطبيق يهدف إلى توفير واجهة سهلة الاستخدام وشاملة لإدارة العمليات الأكاديمية والإدارية مع دعم اللغة العربية بشكل كامل، تصميم متجاوب، وميزات أمان متقدمة.

## المتطلبات الرئيسية

### 1. إدارة المواد الدراسية
- **وظائف**:
  - إضافة، تعديل، وحذف المواد الدراسية (مثل: الرياضيات، العلوم، اللغة العربية).
  - تحديد بيانات المادة (اسم المادة، رمز المادة، الصف الدراسي، المعلم المسؤول، الوصف).
  - ربط المواد بالفصول الدراسية.
- **واجهة المستخدم**:
  - جدول يعرض قائمة المواد مع خيارات البحث والتصفية.
  - نموذج إدخال بيانات لإضافة/تعديل المواد.
  - دعم اللغة العربية في الحقول والعناوين.

### 2. إدارة الطلاب
- **وظائف**:
  - إضافة، تعديل، وحذف بيانات الطلاب (الاسم، رقم الهوية، تاريخ الميلاد، العنوان، رقم الهاتف، بيانات ولي الأمر).
  - تسجيل الطلاب في الفصول الدراسية.
  - تتبع الحضور والغياب.
  - أرشفة بيانات الطلاب السابقين.
- **واجهة المستخدم**:
  - جدول لعرض بيانات الطلاب مع إمكانية البحث حسب الاسم أو رقم الهوية.
  - نموذج إدخال بيانات الطالب مع حقول إلزامية (مثل الاسم ورقم الهوية).
  - تقرير حضور/غياب يومي/شهري.

### 3. إدارة الرسوم الدراسية
- **وظائف**:
  - تسجيل الرسوم الدراسية لكل طالب (رسوم سنوية، فصلية، أو شهرية).
  - تتبع المدفوعات والمستحقات (مدفوع، متأخر، مستحق).
  - إصدار إيصالات دفع وفواتير.
  - إضافة خصومات أو رسوم إضافية (مثل: رسوم النقل، الأنشطة).
  - دعم طرق دفع متعددة (نقدي، بطاقة، تحويل بنكي).
- **واجهة المستخدم**:
  - جدول لعرض حالة الرسوم لكل طالب مع تصفية حسب الحالة (مدفوع/غير مدفوع).
  - نموذج لتسجيل الدفعات مع حقل لتاريخ الدفع وطريقته.
  - خيار تصدير الفواتير/الإيصالات إلى PDF.

### 4. إدارة الصفوف والفصول الدراسية
- **وظائف**:
  - إنشاء وتعديل وحذف الصفوف الدراسية (مثل: الصف الأول الابتدائي).
  - إنشاء فصول دراسية داخل الصف (مثل: 1-أ، 1-ب).
  - ربط الطلاب والمعلمين والمواد بالفصول.
  - جدولة الحصص الدراسية (جدول زمني أسبوعي).
- **واجهة المستخدم**:
  - عرض شجري (Tree View) للصفوف والفصول.
  - واجهة لإنشاء الجداول الزمنية مع خاصية السحب والإفلات (Drag-and-Drop).

### 5. إدارة المعلمين والموظفين وإدارة الرواتب وإدارة الموارد البشرية
- **وظائف**:
  - إضافة، تعديل، وحذف بيانات المعلمين والموظفين (الاسم، رقم الهوية، التخصص، بيانات التواصل).
  - إدارة الرواتب: تسجيل الراتب الأساسي، الحوافز، الخصومات، وإصدار كشوفات الرواتب.
  - تتبع الحضور والإجازات.
  - إدارة العقود والوثائق (مثل: عقد العمل، الشهادات).
- **واجهة المستخدم**:
  - جدول لعرض بيانات المعلمين/الموظفين مع خيارات التصفية.
  - نموذج إدخال بيانات الموظف.
  - تقرير شهري للرواتب والإجازات.

### 6. إدارة النتائج الدراسية
- **وظائف**:
  - إدخال درجات الطلاب لكل مادة (اختبارات، واجبات، مشاريع).
  - حساب المعدل التراكمي والنسبة المئوية.
  - إصدار كشوفات الدرجات.
  - دعم أنظمة التقييم المختلفة (مثل: درجات عددية، تقديرات حرفية).
- **واجهة المستخدم**:
  - نموذج إدخال درجات مع التحقق من صحة البيانات.
  - جدول لعرض النتائج حسب الفصل أو الطالب.
  - خيار تصدير النتائج إلى PDF/Excel.

### 7. إدارة التقارير
- **وظائف**:
  - إنشاء تقارير شاملة (تقارير الطلاب، المعلمين، الرسوم، الرواتب، الحضور، النتائج).
  - تخصيص التقارير (اختيار الحقول، التصفية حسب التاريخ أو الفصل).
  - تصدير التقارير إلى صيغ (PDF، Excel، CSV).
- **واجهة المستخدم**:
  - واجهة لتحديد معايير التقرير (مثل: الفترة الزمنية، الفصل).
  - معاينة التقرير قبل التصدير.

### 8. إدارة المستخدمين وصلاحياتهم
- **وظائف**:
  - إنشاء حسابات مستخدمين (مدير، معلم، موظف إداري).
  - تحديد صلاحيات المستخدم (مثل: قراءة فقط، تعديل، حذف).
  - تسجيل الدخول باستخدام اسم المستخدم وكلمة المرور.
  - تسجيل نشاط المستخدم (Log Activity).
- **واجهة المستخدم**:
  - نموذج تسجيل الدخول.
  - واجهة إدارة المستخدمين مع جدول لعرض الحسابات وصلاحياتها.

### 9. الإعدادات
- **وظائف**:
  - **الإعدادات العامة**: تحديد اسم المدرسة، السنة الدراسية، اللغة الافتراضية.
  - **إعدادات الطباعة والتصدير**: تخصيص تنسيق التقارير والفواتير (حجم الورق، الهوامش).
  - **إدارة الخطوط**: دعم خطوط عربية (مثل: Amiri، Noto Serif Arabic).
  - **ملف المدرسة**: تحديث شعار المدرسة، العنوان، ومعلومات التواصل.
  - **النسخ الاحتياطي واستعادته**: إنشاء نسخ احتياطية تلقائية/يدوية واستعادتها.
  - **العملة**: تحديد العملة المحلية (مثل: ريال سعودي) للرواتب والرسوم.
- **واجهة المستخدم**:
  - لوحة إعدادات مقسمة إلى علامات تبويب (Tabs) لكل فئة.
  - خيارات لمعاينة التغييرات قبل الحفظ.

## متطلبات تقنية
- **اللغة**: Python 3.x.
- **واجهة المستخدم**: PyQt5 أو PySide2.
- **قاعدة البيانات**: SQLite لتخزين البيانات محلياً (مع إمكانية التوسع لدعم MySQL/PostgreSQL).
- **الأمان**:
  - تشفير كلمات المرور باستخدام bcrypt أو hashlib.
  - التحقق من المدخلات لمنع هجمات SQL Injection.
- **التوافق**: دعم أنظمة Windows، macOS، وLinux.
- **اللغة**: واجهة كاملة باللغة العربية مع دعم الاتجاه من اليمين إلى اليسار (RTL).
- **التصميم**:
  - تصميم متجاوب وسهل الاستخدام.
  - استخدام أيقونات واضحة (مثل: FontAwesome أو Material Icons).
  - دعم الثيمات (فاتح/داكن).

## ميزات إضافية
- **تسجيل النشاط**: حفظ سجل لجميع العمليات (إضافة، تعديل، حذف) مع الطابع الزمني واسم المستخدم.
- **إشعارات داخل التطبيق**: تنبيهات عند إدخال بيانات غير صحيحة أو انتهاء مهلة دفع الرسوم.
- **دعم متعدد المستخدمين**: السماح لأكثر من مستخدم بالعمل في نفس الوقت (مع قاعدة بيانات مركزية).
- **تصدير/استيراد البيانات**: دعم استيراد/تصدير بيانات الطلاب، المعلمين، والرسوم عبر ملفات CSV/Excel.

## الهيكلية المقترحة
- **المجلدات**:
  - `src/`: يحتوي على التعليمات البرمجية الأساسية.
  - `ui/`: ملفات واجهة المستخدم المصممة بـ Qt Designer.
  - `db/`: ملفات قاعدة البيانات ونماذج SQL.
  - `resources/`: الأيقونات، الصور، والخطوط.
  - `reports/`: قوالب التقارير والفواتير.
- **الفئات الرئيسية (Classes)**:
  - `MainWindow`: النافذة الرئيسية للتطبيق.
  - `StudentManager`: إدارة بيانات الطلاب.
  - `FeesManager`: إدارة الرسوم الدراسية والمدفوعات.
  - `TeacherManager`: إدارة بيانات المعلمين والرواتب.
  - `CourseManager`: إدارة المواد الدراسية.
  - `ClassroomManager`: إدارة الصفوف والفصول.
  - `ResultsManager`: إدارة النتائج الدراسية.
  - `ReportGenerator`: إنشاء وتصدير التقارير.
  - `UserManager`: إدارة المستخدمين والصلاحيات.
  - `SettingsManager`: إدارة الإعدادات.
- **قاعدة البيانات**:
  - جداول مقترحة:
    - `students`: (student_id, name, dob, address, parent_contact, class_id).
    - `fees`: (fee_id, student_id, amount, due_date, status, payment_date, payment_method).
    - `teachers`: (teacher_id, name, specialization, contact, salary).
    - `courses`: (course_id, name, code, class_id, teacher_id).
    - `classes`: (class_id, name, grade_level).
    - `results`: (result_id, student_id, course_id, score, exam_date).
    - `users`: (user_id, username, password_hash, role, permissions).
    - `settings`: (setting_key, setting_value).

## إرشادات إضافية
- استخدام نمط MVC (Model-View-Controller) لفصل المنطق عن واجهة المستخدم.
- توثيق الكود باستخدام تعليقات واضحة.
- إضافة اختبارات وحدة (Unit Tests) باستخدام pytest لضمان استقرار التطبيق.
- دعم النسخ الاحتياطي التلقائي يومياً مع خيار استعادة يدوي.
- استخدام مكتبة `reportlab` أو مشابه لإنشاء تقارير وفواتير PDF.

## مخرجات التطبيق
- تطبيق مستقل قابل للتشغيل على أنظمة Windows/macOS/Linux.
- قاعدة بيانات SQLite مدمجة.
- ملفات واجهة مستخدم (UI) مصممة بـ Qt Designer.
- دليل مستخدم (PDF) يشرح كيفية استخدام التطبيق.
- ملفات تعليمات برمجية موثقة.

## ملاحظات نهائية
- التأكد من دعم اللغة العربية بشكل كامل في الواجهة وقاعدة البيانات.
- اختبار التطبيق مع بيانات وهمية للتأكد من استقراره.
- توفير خيار لتحديثات تلقائية للتطبيق (اختياري).