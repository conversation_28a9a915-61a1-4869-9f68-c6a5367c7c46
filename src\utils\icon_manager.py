#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الأيقونات والصور
يوفر نظام موحد لإدارة الأيقونات والصور في التطبيق
"""

import os
import base64
from PyQt5.QtGui import QIcon, QPixmap, QPainter, QColor, QFont
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtWidgets import QApplication
from src.utils.config import Config


class IconManager:
    """مدير الأيقونات والصور"""
    
    _instance = None
    _icons_cache = {}
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(IconManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        self.icons_dir = Config.RESOURCES_DIR / "icons"
        self.images_dir = Config.RESOURCES_DIR / "images"
        self.ensure_directories()
        # لا ننشئ الأيقونات في __init__ لتجنب مشاكل QApplication
    
    def ensure_directories(self):
        """التأكد من وجود مجلدات الأيقونات"""
        self.icons_dir.mkdir(parents=True, exist_ok=True)
        self.images_dir.mkdir(parents=True, exist_ok=True)
    
    def create_default_icons(self):
        """إنشاء الأيقونات الافتراضية"""
        # التحقق من وجود QApplication
        app = QApplication.instance()
        if not app:
            return

        # أيقونة التطبيق الرئيسية
        self.create_app_icon()

        # أيقونات القوائم
        self.create_menu_icons()

        # أيقونات الأزرار
        self.create_button_icons()

        # أيقونات الحالة
        self.create_status_icons()
    
    def create_app_icon(self):
        """إنشاء أيقونة التطبيق الرئيسية"""
        # إنشاء أيقونة بسيطة للتطبيق
        pixmap = QPixmap(64, 64)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # رسم خلفية دائرية
        painter.setBrush(QColor("#3498db"))
        painter.setPen(Qt.NoPen)
        painter.drawEllipse(4, 4, 56, 56)
        
        # رسم أيقونة المدرسة (كتاب)
        painter.setBrush(QColor("white"))
        painter.drawRect(18, 20, 28, 20)
        
        # رسم خطوط الكتاب
        painter.setPen(QColor("#3498db"))
        painter.drawLine(22, 25, 42, 25)
        painter.drawLine(22, 30, 42, 30)
        painter.drawLine(22, 35, 38, 35)
        
        painter.end()
        
        # حفظ الأيقونة
        icon_path = self.icons_dir / "app_icon.png"
        pixmap.save(str(icon_path))
        
        return QIcon(pixmap)
    
    def create_menu_icons(self):
        """إنشاء أيقونات القوائم"""
        menu_icons = {
            "dashboard": "🏠",
            "students": "👥",
            "teachers": "👨‍🏫",
            "subjects": "📚",
            "classes": "🏫",
            "fees": "💰",
            "results": "📊",
            "reports": "📋",
            "settings": "⚙️",
            "users": "👤",
            "currency": "💱"
        }
        
        for name, emoji in menu_icons.items():
            self.create_text_icon(emoji, name, size=32)
    
    def create_button_icons(self):
        """إنشاء أيقونات الأزرار"""
        button_icons = {
            "add": "➕",
            "edit": "✏️",
            "delete": "🗑️",
            "save": "💾",
            "cancel": "❌",
            "search": "🔍",
            "print": "🖨️",
            "export": "📤",
            "import": "📥",
            "refresh": "🔄",
            "back": "⬅️",
            "forward": "➡️",
            "up": "⬆️",
            "down": "⬇️"
        }
        
        for name, emoji in button_icons.items():
            self.create_text_icon(emoji, name, size=16)
    
    def create_status_icons(self):
        """إنشاء أيقونات الحالة"""
        status_icons = {
            "success": "✅",
            "warning": "⚠️",
            "error": "❌",
            "info": "ℹ️",
            "question": "❓"
        }
        
        for name, emoji in status_icons.items():
            self.create_text_icon(emoji, name, size=24)
    
    def create_text_icon(self, text, name, size=32, color=None):
        """إنشاء أيقونة من نص أو رمز تعبيري"""
        pixmap = QPixmap(size, size)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # إعداد الخط
        font = QFont("Segoe UI Emoji", size // 2)
        painter.setFont(font)
        
        # إعداد اللون
        if color:
            painter.setPen(QColor(color))
        else:
            painter.setPen(QColor("#2c3e50"))
        
        # رسم النص
        painter.drawText(pixmap.rect(), Qt.AlignCenter, text)
        painter.end()
        
        # حفظ الأيقونة
        icon_path = self.icons_dir / f"{name}.png"
        pixmap.save(str(icon_path))
        
        return QIcon(pixmap)
    
    def get_icon(self, name, size=None):
        """الحصول على أيقونة بالاسم"""
        # التحقق من وجود QApplication
        app = QApplication.instance()
        if not app:
            return QIcon()

        # إنشاء الأيقونات الافتراضية إذا لم تكن موجودة
        if not (self.icons_dir / f"{name}.png").exists():
            self.create_default_icons()

        # البحث في الكاش أولاً
        cache_key = f"{name}_{size}" if size else name
        if cache_key in self._icons_cache:
            return self._icons_cache[cache_key]

        # البحث عن الأيقونة في المجلد
        icon_path = self.icons_dir / f"{name}.png"

        if icon_path.exists():
            icon = QIcon(str(icon_path))
            if size:
                # تغيير حجم الأيقونة
                pixmap = icon.pixmap(QSize(size, size))
                icon = QIcon(pixmap)

            # حفظ في الكاش
            self._icons_cache[cache_key] = icon
            return icon

        # إنشاء أيقونة افتراضية إذا لم توجد
        return self.create_default_icon(name, size)
    
    def create_default_icon(self, name, size=32):
        """إنشاء أيقونة افتراضية"""
        pixmap = QPixmap(size, size)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # رسم مربع ملون
        painter.setBrush(QColor("#95a5a6"))
        painter.setPen(Qt.NoPen)
        painter.drawRoundedRect(2, 2, size-4, size-4, 4, 4)
        
        # رسم الحرف الأول من الاسم
        font = QFont("Arial", size // 3, QFont.Bold)
        painter.setFont(font)
        painter.setPen(QColor("white"))
        painter.drawText(pixmap.rect(), Qt.AlignCenter, name[0].upper())
        
        painter.end()
        
        return QIcon(pixmap)
    
    def get_app_icon(self):
        """الحصول على أيقونة التطبيق الرئيسية"""
        return self.get_icon("app_icon")
    
    def get_menu_icon(self, menu_name):
        """الحصول على أيقونة قائمة"""
        return self.get_icon(menu_name, 24)
    
    def get_button_icon(self, button_name):
        """الحصول على أيقونة زر"""
        return self.get_icon(button_name, 16)
    
    def get_status_icon(self, status_name):
        """الحصول على أيقونة حالة"""
        return self.get_icon(status_name, 24)
    
    def set_window_icon(self, window, icon_name=None):
        """تعيين أيقونة لنافذة"""
        if icon_name:
            icon = self.get_icon(icon_name)
        else:
            icon = self.get_app_icon()
        
        window.setWindowIcon(icon)
    
    def set_application_icon(self, app):
        """تعيين أيقونة التطبيق"""
        icon = self.get_app_icon()
        app.setWindowIcon(icon)
    
    def create_colored_icon(self, base_icon_name, color, size=None):
        """إنشاء أيقونة ملونة من أيقونة موجودة"""
        base_icon = self.get_icon(base_icon_name, size)
        if not base_icon:
            return None
        
        # الحصول على البكسل الأساسي
        pixmap = base_icon.pixmap(QSize(size or 32, size or 32))
        
        # إنشاء بكسل جديد ملون
        colored_pixmap = QPixmap(pixmap.size())
        colored_pixmap.fill(Qt.transparent)
        
        painter = QPainter(colored_pixmap)
        painter.setCompositionMode(QPainter.CompositionMode_SourceOver)
        painter.drawPixmap(0, 0, pixmap)
        
        painter.setCompositionMode(QPainter.CompositionMode_SourceIn)
        painter.fillRect(colored_pixmap.rect(), QColor(color))
        painter.end()
        
        return QIcon(colored_pixmap)
    
    def get_available_icons(self):
        """الحصول على قائمة بجميع الأيقونات المتاحة"""
        icons = []
        if self.icons_dir.exists():
            for file_path in self.icons_dir.glob("*.png"):
                icons.append(file_path.stem)
        return icons
    
    def clear_cache(self):
        """مسح كاش الأيقونات"""
        self._icons_cache.clear()


# إنشاء مثيل عام للاستخدام
icon_manager = IconManager()


def get_icon_manager():
    """الحصول على مدير الأيقونات"""
    return icon_manager


def get_icon(name, size=None):
    """دالة مساعدة للحصول على أيقونة"""
    return icon_manager.get_icon(name, size)


def set_window_icon(window, icon_name=None):
    """دالة مساعدة لتعيين أيقونة النافذة"""
    icon_manager.set_window_icon(window, icon_name)
