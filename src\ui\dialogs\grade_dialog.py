#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إدخال وتعديل الدرجات
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QLineEdit, QPushButton, QLabel, QFrame, QMessageBox,
                             QComboBox, QDoubleSpinBox, QTextEdit, QSpinBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

from src.models.student import Student
from src.models.subject import Subject


class GradeDialog(QDialog):
    """نافذة إدخال وتعديل الدرجات"""
    
    # إشارة حفظ البيانات
    grade_saved = pyqtSignal()
    
    def __init__(self, result_id=None, parent=None):
        super().__init__(parent)
        self.result_id = result_id
        self.student_model = Student()
        self.subject_model = Subject()
        self.is_edit_mode = result_id is not None
        
        self.setup_ui()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.load_grade_data()
        else:
            self.load_students()
            self.load_subjects()
            
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة
        title = "تعديل درجة" if self.is_edit_mode else "إدخال درجة جديدة"
        self.setWindowTitle(title)
        self.setFixedSize(500, 500)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # عنوان النافذة
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # نموذج البيانات
        form_frame = QFrame()
        form_frame.setFrameStyle(QFrame.StyledPanel)
        form_layout = QFormLayout(form_frame)
        form_layout.setSpacing(15)
        
        # اختيار الطالب
        self.student_combo = QComboBox()
        self.student_combo.setEnabled(not self.is_edit_mode)
        form_layout.addRow("الطالب *:", self.student_combo)
        
        # اختيار المادة
        self.subject_combo = QComboBox()
        self.subject_combo.setEnabled(not self.is_edit_mode)
        form_layout.addRow("المادة *:", self.subject_combo)
        
        # نوع التقييم
        self.assessment_type_combo = QComboBox()
        assessment_types = ["اختبار شهري", "اختبار نصف الفصل", "اختبار نهائي", 
                           "واجب", "مشروع", "مشاركة", "أخرى"]
        self.assessment_type_combo.addItems(assessment_types)
        form_layout.addRow("نوع التقييم *:", self.assessment_type_combo)
        
        # الدرجة المحصلة
        self.score_input = QDoubleSpinBox()
        self.score_input.setRange(0, 100)
        self.score_input.setValue(0)
        self.score_input.setSuffix(" درجة")
        form_layout.addRow("الدرجة المحصلة *:", self.score_input)
        
        # الدرجة الكاملة
        self.max_score_input = QSpinBox()
        self.max_score_input.setRange(1, 100)
        self.max_score_input.setValue(20)
        self.max_score_input.setSuffix(" درجة")
        form_layout.addRow("الدرجة الكاملة *:", self.max_score_input)
        
        # النسبة المئوية (محسوبة تلقائياً)
        self.percentage_label = QLabel("0.0%")
        self.percentage_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #27ae60;
                padding: 5px;
                background-color: #ecf0f1;
                border-radius: 3px;
            }
        """)
        form_layout.addRow("النسبة المئوية:", self.percentage_label)
        
        # التقدير (محسوب تلقائياً)
        self.grade_letter_label = QLabel("غير محدد")
        self.grade_letter_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #3498db;
                padding: 5px;
                background-color: #ecf0f1;
                border-radius: 3px;
            }
        """)
        form_layout.addRow("التقدير:", self.grade_letter_label)
        
        # ملاحظات
        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText("أدخل أي ملاحظات حول الدرجة")
        self.notes_input.setMaximumHeight(80)
        form_layout.addRow("ملاحظات:", self.notes_input)
        
        main_layout.addWidget(form_frame)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        self.save_button = QPushButton("حفظ")
        self.save_button.setFixedHeight(40)
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setFixedHeight(40)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.save_button)
        
        main_layout.addLayout(buttons_layout)
        
    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        self.save_button.clicked.connect(self.save_grade)
        self.cancel_button.clicked.connect(self.reject)
        
        # ربط حساب النسبة والتقدير
        self.score_input.valueChanged.connect(self.calculate_percentage)
        self.max_score_input.valueChanged.connect(self.calculate_percentage)
        
    def load_students(self):
        """تحميل قائمة الطلاب"""
        try:
            students = self.student_model.get_active_students()
            self.student_combo.clear()
            
            for student in students:
                name = f"{student['first_name']} {student['last_name']} - {student['student_number']}"
                self.student_combo.addItem(name, student['student_id'])
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل الطلاب: {str(e)}")
            
    def load_subjects(self):
        """تحميل قائمة المواد"""
        try:
            subjects = self.subject_model.get_active_subjects()
            self.subject_combo.clear()
            
            for subject in subjects:
                self.subject_combo.addItem(subject['subject_name'], subject['subject_id'])
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل المواد: {str(e)}")
            
    def calculate_percentage(self):
        """حساب النسبة المئوية والتقدير"""
        score = self.score_input.value()
        max_score = self.max_score_input.value()
        
        if max_score > 0:
            percentage = (score / max_score) * 100
            self.percentage_label.setText(f"{percentage:.1f}%")
            
            # تحديد التقدير
            grade_letter = self.get_grade_letter(percentage)
            self.grade_letter_label.setText(grade_letter)
        else:
            self.percentage_label.setText("0.0%")
            self.grade_letter_label.setText("غير محدد")
            
    def get_grade_letter(self, percentage):
        """تحديد التقدير بناءً على النسبة المئوية"""
        if percentage >= 90:
            return "ممتاز"
        elif percentage >= 80:
            return "جيد جداً"
        elif percentage >= 70:
            return "جيد"
        elif percentage >= 60:
            return "مقبول"
        else:
            return "راسب"
            
    def load_grade_data(self):
        """تحميل بيانات الدرجة للتعديل"""
        try:
            # استعلام بيانات الدرجة
            query = """
            SELECT r.*, s.first_name, s.last_name, s.student_number, sub.subject_name
            FROM results r
            JOIN students s ON r.student_id = s.student_id
            JOIN subjects sub ON r.subject_id = sub.subject_id
            WHERE r.result_id = ?
            """
            result = self.student_model.db_manager.fetch_one(query, (self.result_id,))
            
            if not result:
                QMessageBox.critical(self, "خطأ", "لم يتم العثور على الدرجة")
                self.reject()
                return
                
            # ملء الحقول بالبيانات
            student_name = f"{result['first_name']} {result['last_name']} - {result['student_number']}"
            self.student_combo.addItem(student_name, result['student_id'])
            self.student_combo.setCurrentIndex(0)
            
            self.subject_combo.addItem(result['subject_name'], result['subject_id'])
            self.subject_combo.setCurrentIndex(0)
            
            # نوع التقييم
            assessment_index = self.assessment_type_combo.findText(result['exam_type'])
            if assessment_index >= 0:
                self.assessment_type_combo.setCurrentIndex(assessment_index)
                
            self.score_input.setValue(result['score'])
            self.max_score_input.setValue(result['max_score'])
            self.notes_input.setPlainText(str(result['notes'] or ''))
            
            # حساب النسبة والتقدير
            self.calculate_percentage()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل بيانات الدرجة: {str(e)}")
            
    def save_grade(self):
        """حفظ بيانات الدرجة"""
        try:
            # التحقق من الحقول المطلوبة
            if not self.is_edit_mode:
                if self.student_combo.currentIndex() < 0:
                    QMessageBox.warning(self, "تحذير", "يرجى اختيار الطالب")
                    return
                    
                if self.subject_combo.currentIndex() < 0:
                    QMessageBox.warning(self, "تحذير", "يرجى اختيار المادة")
                    return
            
            # جمع البيانات من النموذج
            score = self.score_input.value()
            max_score = self.max_score_input.value()
            percentage = (score / max_score) * 100 if max_score > 0 else 0
            assessment_type = self.assessment_type_combo.currentText()
            notes = self.notes_input.toPlainText().strip() or None
            
            # حفظ البيانات
            if self.is_edit_mode:
                # تحديث الدرجة
                query = """
                UPDATE results SET
                exam_type = ?, score = ?, max_score = ?, percentage = ?, notes = ?
                WHERE result_id = ?
                """
                params = (assessment_type, score, max_score, percentage, notes, self.result_id)
                self.student_model.db_manager.execute_query(query, params)
                QMessageBox.information(self, "نجح", "تم تحديث الدرجة بنجاح")
            else:
                # إضافة درجة جديدة
                student_id = self.student_combo.currentData()
                subject_id = self.subject_combo.currentData()
                
                query = """
                INSERT INTO results (student_id, subject_id, exam_type,
                score, max_score, percentage, notes, class_id, exam_date, academic_year, semester)
                VALUES (?, ?, ?, ?, ?, ?, ?, 1, DATE('now'), '2024-2025', 'الفصل الأول')
                """
                params = (student_id, subject_id, assessment_type, score, max_score, percentage, notes)
                self.student_model.db_manager.execute_query(query, params)
                QMessageBox.information(self, "نجح", "تم إضافة الدرجة بنجاح")
            
            # إرسال إشارة الحفظ
            self.grade_saved.emit()
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ البيانات: {str(e)}")
