#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء بيانات وهمية للاختبار
"""

from datetime import datetime, date, timedelta
import random

from src.models.student import Student
from src.models.teacher import Teacher
from src.models.subject import Subject
from src.models.class_model import ClassModel


class SampleDataGenerator:
    """مولد البيانات الوهمية"""
    
    def __init__(self):
        self.student_model = Student()
        self.teacher_model = Teacher()
        self.subject_model = Subject()
        self.class_model = ClassModel()
        
    def generate_all_sample_data(self):
        """إنشاء جميع البيانات الوهمية"""
        try:
            print("جاري إنشاء البيانات الوهمية...")
            
            # إنشاء المواد الدراسية
            self.create_sample_subjects()
            print("تم إنشاء المواد الدراسية")
            
            # إنشاء الصفوف الدراسية
            self.create_sample_classes()
            print("تم إنشاء الصفوف الدراسية")
            
            # إنشاء المعلمين
            self.create_sample_teachers()
            print("تم إنشاء المعلمين")
            
            # إنشاء الطلاب
            self.create_sample_students()
            print("تم إنشاء الطلاب")
            
            print("تم إنشاء جميع البيانات الوهمية بنجاح!")
            
        except Exception as e:
            print(f"خطأ في إنشاء البيانات الوهمية: {e}")
    
    def create_sample_subjects(self):
        """إنشاء مواد دراسية وهمية"""
        subjects = [
            {"subject_code": "MATH101", "subject_name": "الرياضيات", "credit_hours": 4},
            {"subject_code": "ARAB101", "subject_name": "اللغة العربية", "credit_hours": 4},
            {"subject_code": "ENG101", "subject_name": "اللغة الإنجليزية", "credit_hours": 3},
            {"subject_code": "SCI101", "subject_name": "العلوم", "credit_hours": 3},
            {"subject_code": "HIST101", "subject_name": "التاريخ", "credit_hours": 2},
            {"subject_code": "GEO101", "subject_name": "الجغرافيا", "credit_hours": 2},
            {"subject_code": "ART101", "subject_name": "التربية الفنية", "credit_hours": 1},
            {"subject_code": "PE101", "subject_name": "التربية البدنية", "credit_hours": 1},
            {"subject_code": "REL101", "subject_name": "التربية الإسلامية", "credit_hours": 2},
            {"subject_code": "COMP101", "subject_name": "الحاسوب", "credit_hours": 2}
        ]
        
        for subject_data in subjects:
            try:
                # التحقق من عدم وجود المادة مسبقاً
                existing = self.subject_model.get_subject_by_code(subject_data["subject_code"])
                if not existing:
                    self.subject_model.add_subject(subject_data)
            except Exception as e:
                print(f"خطأ في إضافة المادة {subject_data['subject_name']}: {e}")
    
    def create_sample_classes(self):
        """إنشاء صفوف دراسية وهمية"""
        classes = [
            {"class_name": "الأول أ", "grade_level": "الصف الأول", "academic_year": "2024-2025", "capacity": 25},
            {"class_name": "الأول ب", "grade_level": "الصف الأول", "academic_year": "2024-2025", "capacity": 25},
            {"class_name": "الثاني أ", "grade_level": "الصف الثاني", "academic_year": "2024-2025", "capacity": 30},
            {"class_name": "الثاني ب", "grade_level": "الصف الثاني", "academic_year": "2024-2025", "capacity": 30},
            {"class_name": "الثالث أ", "grade_level": "الصف الثالث", "academic_year": "2024-2025", "capacity": 28},
            {"class_name": "الثالث ب", "grade_level": "الصف الثالث", "academic_year": "2024-2025", "capacity": 28},
            {"class_name": "الرابع أ", "grade_level": "الصف الرابع", "academic_year": "2024-2025", "capacity": 32},
            {"class_name": "الخامس أ", "grade_level": "الصف الخامس", "academic_year": "2024-2025", "capacity": 30},
            {"class_name": "السادس أ", "grade_level": "الصف السادس", "academic_year": "2024-2025", "capacity": 35}
        ]
        
        for class_data in classes:
            try:
                # التحقق من عدم وجود الصف مسبقاً
                existing = self.class_model.get_all(
                    "class_name = ? AND academic_year = ?", 
                    (class_data["class_name"], class_data["academic_year"])
                )
                if not existing:
                    self.class_model.add_class(class_data)
            except Exception as e:
                print(f"خطأ في إضافة الصف {class_data['class_name']}: {e}")
    
    def create_sample_teachers(self):
        """إنشاء معلمين وهميين"""
        teachers = [
            {
                "employee_number": "T001",
                "first_name": "أحمد",
                "last_name": "محمد",
                "phone": "0501234567",
                "email": "<EMAIL>",
                "specialization": "الرياضيات",
                "position": "معلم",
                "hire_date": "2020-09-01",
                "salary": 8000.00,
                "gender": "male"
            },
            {
                "employee_number": "T002",
                "first_name": "فاطمة",
                "last_name": "علي",
                "phone": "0507654321",
                "email": "<EMAIL>",
                "specialization": "اللغة العربية",
                "position": "معلمة",
                "hire_date": "2019-09-01",
                "salary": 7500.00,
                "gender": "female"
            },
            {
                "employee_number": "T003",
                "first_name": "خالد",
                "last_name": "السعد",
                "phone": "0509876543",
                "email": "<EMAIL>",
                "specialization": "العلوم",
                "position": "معلم",
                "hire_date": "2021-09-01",
                "salary": 7000.00,
                "gender": "male"
            },
            {
                "employee_number": "T004",
                "first_name": "مريم",
                "last_name": "أحمد",
                "phone": "0502468135",
                "email": "<EMAIL>",
                "specialization": "اللغة الإنجليزية",
                "position": "معلمة",
                "hire_date": "2022-09-01",
                "salary": 6500.00,
                "gender": "female"
            }
        ]
        
        for teacher_data in teachers:
            try:
                # التحقق من عدم وجود المعلم مسبقاً
                existing = self.teacher_model.get_teacher_by_employee_number(teacher_data["employee_number"])
                if not existing:
                    self.teacher_model.add_teacher(teacher_data)
            except Exception as e:
                print(f"خطأ في إضافة المعلم {teacher_data['first_name']} {teacher_data['last_name']}: {e}")
    
    def create_sample_students(self):
        """إنشاء طلاب وهميين"""
        # أسماء وهمية
        first_names_male = ["محمد", "أحمد", "علي", "حسن", "عبدالله", "يوسف", "إبراهيم", "عمر", "سعد", "فهد"]
        first_names_female = ["فاطمة", "عائشة", "مريم", "زينب", "خديجة", "سارة", "نور", "هند", "ريم", "لينا"]
        last_names = ["الأحمد", "المحمد", "العلي", "السعد", "الحسن", "اليوسف", "الإبراهيم", "العمر", "الفهد", "الزهراني"]
        
        # الحصول على الصفوف المتاحة
        classes = self.class_model.get_active_classes()
        
        student_counter = 1
        
        for class_info in classes:
            # إنشاء طلاب لكل صف
            num_students = random.randint(15, min(class_info['capacity'], 25))
            
            for i in range(num_students):
                gender = random.choice(['male', 'female'])
                first_name = random.choice(first_names_male if gender == 'male' else first_names_female)
                last_name = random.choice(last_names)
                
                # تاريخ ميلاد عشوائي (بين 6 و 18 سنة)
                birth_year = datetime.now().year - random.randint(6, 18)
                birth_month = random.randint(1, 12)
                birth_day = random.randint(1, 28)
                birth_date = date(birth_year, birth_month, birth_day)
                
                student_data = {
                    "student_number": f"S{student_counter:04d}",
                    "first_name": first_name,
                    "last_name": last_name,
                    "date_of_birth": birth_date.strftime("%Y-%m-%d"),
                    "gender": gender,
                    "class_id": class_info['class_id'],
                    "parent_name": f"والد {first_name}",
                    "parent_phone": f"050{random.randint(1000000, 9999999)}",
                    "enrollment_date": "2024-09-01",
                    "status": "active"
                }
                
                try:
                    # التحقق من عدم وجود الطالب مسبقاً
                    existing = self.student_model.get_student_by_number(student_data["student_number"])
                    if not existing:
                        self.student_model.add_student(student_data)
                        student_counter += 1
                except Exception as e:
                    print(f"خطأ في إضافة الطالب {first_name} {last_name}: {e}")


def main():
    """تشغيل مولد البيانات الوهمية"""
    generator = SampleDataGenerator()
    generator.generate_all_sample_data()


if __name__ == "__main__":
    main()
