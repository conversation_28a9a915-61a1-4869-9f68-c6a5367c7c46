#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الإصلاحات المطبقة على النظام
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.models.student import Student
from src.models.teacher import Teacher
from src.models.user import User
from src.database.db_manager import DatabaseManager


def test_database_creation():
    """اختبار إنشاء قاعدة البيانات"""
    print("=== اختبار قاعدة البيانات ===")
    try:
        db_manager = DatabaseManager()
        db_manager.create_tables()
        print("✓ تم إنشاء قاعدة البيانات بنجاح")
        return True
    except Exception as e:
        print(f"✗ خطأ في إنشاء قاعدة البيانات: {e}")
        return False


def test_user_model():
    """اختبار نموذج المستخدمين"""
    print("\n=== اختبار نموذج المستخدمين ===")
    try:
        user_model = User()
        
        # اختبار إنشاء مدير افتراضي
        user_model.create_default_admin()
        print("✓ تم إنشاء المدير الافتراضي")
        
        # اختبار جلب جميع المستخدمين
        users = user_model.get_all()
        print(f"✓ تم جلب {len(users)} مستخدم")
        
        # اختبار الإحصائيات
        stats = user_model.get_user_statistics()
        print(f"✓ إحصائيات المستخدمين: {stats['total_active']} نشط")
        
        return True
    except Exception as e:
        print(f"✗ خطأ في نموذج المستخدمين: {e}")
        return False


def test_student_model():
    """اختبار نموذج الطلاب"""
    print("\n=== اختبار نموذج الطلاب ===")
    try:
        student_model = Student()
        
        # اختبار جلب جميع الطلاب
        students = student_model.get_all()
        print(f"✓ تم جلب {len(students)} طالب")
        
        # اختبار الإحصائيات
        stats = student_model.get_students_statistics()
        print(f"✓ إحصائيات الطلاب: {stats['active_students']} نشط")
        
        # اختبار البحث
        search_results = student_model.search_students("test")
        print(f"✓ نتائج البحث: {len(search_results)} نتيجة")
        
        return True
    except Exception as e:
        print(f"✗ خطأ في نموذج الطلاب: {e}")
        return False


def test_teacher_model():
    """اختبار نموذج المعلمين"""
    print("\n=== اختبار نموذج المعلمين ===")
    try:
        teacher_model = Teacher()
        
        # اختبار جلب جميع المعلمين
        teachers = teacher_model.get_all()
        print(f"✓ تم جلب {len(teachers)} معلم")
        
        # اختبار الإحصائيات
        stats = teacher_model.get_teachers_statistics()
        print(f"✓ إحصائيات المعلمين: {stats['active_teachers']} نشط")
        
        # اختبار البحث
        search_results = teacher_model.search_teachers("test")
        print(f"✓ نتائج البحث: {len(search_results)} نتيجة")
        
        return True
    except Exception as e:
        print(f"✗ خطأ في نموذج المعلمين: {e}")
        return False


def test_dashboard_compatibility():
    """اختبار توافق لوحة المعلومات"""
    print("\n=== اختبار لوحة المعلومات ===")
    try:
        student_model = Student()
        teacher_model = Teacher()
        
        # اختبار الدوال المطلوبة للوحة المعلومات
        student_stats = student_model.get_students_statistics()
        teacher_stats = teacher_model.get_teachers_statistics()
        
        # التحقق من وجود المفاتيح المطلوبة
        required_student_keys = ['active_students', 'by_gender']
        required_teacher_keys = ['active_teachers', 'by_gender']
        
        for key in required_student_keys:
            if key not in student_stats:
                raise Exception(f"مفتاح مفقود في إحصائيات الطلاب: {key}")
        
        for key in required_teacher_keys:
            if key not in teacher_stats:
                raise Exception(f"مفتاح مفقود في إحصائيات المعلمين: {key}")
        
        print("✓ جميع المفاتيح المطلوبة موجودة")
        print(f"✓ الطلاب النشطون: {student_stats['active_students']}")
        print(f"✓ المعلمون النشطون: {teacher_stats['active_teachers']}")
        
        return True
    except Exception as e:
        print(f"✗ خطأ في اختبار لوحة المعلومات: {e}")
        return False


def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار الإصلاحات المطبقة على النظام")
    print("=" * 50)
    
    tests = [
        test_database_creation,
        test_user_model,
        test_student_model,
        test_teacher_model,
        test_dashboard_compatibility
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبار: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
